# compiled output
/dist
*/dist
*/node_modules
backend/node_modules
backend/dist
frontend/node_modules
frontend/dist
node_modules
# /uploads

.env
# Logs
logs
# *.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

*/*.log

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

memory-bank
*/dist
*/docs
*/node_modules
*/*.sql
*/*.txt
*/*.log