variables:
  IMAGE_NAME_FRONTEND: aistechgs/fe-wbs-kg
  IMAGE_NAME_BACKEND: aistechgs/be-wbs-kg
  DEFAULT_TAG: "latest" # Default tag if CI_COMMIT_BRANCH is not set

stages:
  - build
  - deploy

frontend-build:
  stage: build
  tags:
    - docker
  image: docker:24.0.5
  services:
    - name: docker:24.0.5-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
    DOCKER_BUILDKIT: 1
  before_script:
    # Wait for Docker daemon and TLS certs to be ready
    - sleep 10
    - until docker info; do echo "Waiting for Docker daemon..."; sleep 2; done
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    # Create frontend environment file and extract build args
    - echo "$ENV_FE" > .env.frontend
    - export VITE_API_BASE_URL=$(grep VITE_API_BASE_URL .env.frontend | cut -d '=' -f2)
    - export VITE_BACKEND_URL=$(grep VITE_BACKEND_URL .env.frontend | cut -d '=' -f2)
    - export VITE_WS_URL=$(grep VITE_WS_URL .env.frontend | cut -d '=' -f2)
  script:
    - cd frontend
    # Enable BuildKit for faster builds and better caching
    - docker build
      --pull
      --cache-from "${IMAGE_NAME_FRONTEND}:latest"
      --build-arg BUILDKIT_INLINE_CACHE=1
      --build-arg VITE_API_BASE_URL="$VITE_API_BASE_URL"
      --build-arg VITE_BACKEND_URL="$VITE_BACKEND_URL"
      --build-arg VITE_WS_URL="$VITE_WS_URL"
      --target production
      -t "${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
      -t "${IMAGE_NAME_FRONTEND}:latest"
      .
    - docker push "${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
    - docker push "${IMAGE_NAME_FRONTEND}:latest"
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

backend-build:
  stage: build
  tags:
    - docker
  image: docker:24.0.5
  services:
    - name: docker:24.0.5-dind
      alias: docker
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
    DOCKER_BUILDKIT: 1
  before_script:
    # Wait for Docker daemon and TLS certs to be ready
    - sleep 10
    - until docker info; do echo "Waiting for Docker daemon..."; sleep 2; done
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
  script:
    - cd backend
    # Enable BuildKit for faster builds and better caching
    - docker build
      --pull
      --cache-from "${IMAGE_NAME_BACKEND}:latest"
      --build-arg BUILDKIT_INLINE_CACHE=1
      -t "${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
      -t "${IMAGE_NAME_BACKEND}:latest"
      .
    - docker push "${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}"
    - docker push "${IMAGE_NAME_BACKEND}:latest"
  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'

deploy:
  stage: deploy
  variables:
    TAG: "${CI_COMMIT_BRANCH:-$DEFAULT_TAG}" # Pass the tag to docker-compose
  tags:
    - deploy
  needs:
    - job: frontend-build
    - job: backend-build
  before_script:
    - sudo rm -rf backend/.tmp || true # Prevent permission issues
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    # Create backend environment file from GitLab file variable
  script:
    # Clean up old containers and images
    - docker compose -f docker-compose.dev.yml down || true
    - docker container prune -f || true
    # Pull latest images
    - docker pull ${IMAGE_NAME_FRONTEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}
    - docker pull ${IMAGE_NAME_BACKEND}:${CI_COMMIT_BRANCH:-$DEFAULT_TAG}
    # Deploy using docker-compose.dev.yml
    - docker compose -f docker-compose.dev.yml up -d
    # Verify deployment
    - docker compose -f docker-compose.dev.yml ps

  rules:
    - if: $CI_COMMIT_BRANCH == 'development'
