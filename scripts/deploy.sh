#!/bin/bash

# Deployment Script for WBS Aistech Application
# Supports both development and production deployments with nginx reverse proxy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="csrit-bank-sulsel"
DOMAIN="wbs-kg.aistech.id"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker and Docker Compose are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p ssl/certs
    mkdir -p ssl/private
    mkdir -p logs/nginx
    mkdir -p logs/backend
    mkdir -p uploads
    
    log_success "Directories created"
}

# Generate environment file if it doesn't exist
generate_env_file() {
    local env_file="$1"
    local env_type="$2"
    
    if [[ ! -f "$env_file" ]]; then
        log_info "Generating $env_file..."
        
        if [[ "$env_type" == "production" ]]; then
            cat > "$env_file" << EOF
# Production Environment Variables
NODE_ENV=production

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=csrit_backend
DB_USER=postgres
DB_PASS=your_secure_password_here

# JWT Configuration
JWT_ACCESS_TOKEN_SECRET=your_jwt_access_secret_here
JWT_REFRESH_TOKEN_SECRET=your_jwt_refresh_secret_here
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400

# CORS Configuration
CORS_ORIGIN=https://${DOMAIN}

# Redis Configuration
REDIS_PASSWORD=your_redis_password_here

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Application Configuration
APP_PORT=3000
EOF
        else
            cat > "$env_file" << EOF
# Development Environment Variables
NODE_ENV=development

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=csrit_backend_dev
DB_USER=postgres
DB_PASS=password

# JWT Configuration
JWT_ACCESS_TOKEN_SECRET=dev_access_secret
JWT_REFRESH_TOKEN_SECRET=dev_refresh_secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400

# CORS Configuration
CORS_ORIGIN=http://localhost

# Application Configuration
APP_PORT=3000
EOF
        fi
        
        log_warning "Please edit $env_file and update the configuration values"
        log_success "$env_file generated"
    else
        log_info "$env_file already exists"
    fi
}

# Deploy development environment
deploy_development() {
    log_info "Deploying development environment..."
    
    # Generate development environment file
    generate_env_file ".env.dev" "development"
    
    # Generate development SSL certificate
    log_info "Generating development SSL certificate..."
    ./scripts/generate-ssl-certs.sh dev
    
    # Build and start services
    log_info "Building and starting development services..."
    docker-compose -f docker-compose.dev.yml --env-file .env.dev down --remove-orphans
    docker-compose -f docker-compose.dev.yml --env-file .env.dev build --no-cache
    docker-compose -f docker-compose.dev.yml --env-file .env.dev up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_service_health "development"
    
    log_success "Development environment deployed successfully!"
    log_info "Application is available at: http://localhost"
    log_info "API is available at: http://localhost/api"
}

# Deploy production environment
deploy_production() {
    log_info "Deploying production environment..."
    
    # Generate production environment file
    generate_env_file ".env.prod" "production"
    
    # Check SSL certificates
    if [[ ! -f "ssl/certs/${DOMAIN}.crt" || ! -f "ssl/private/${DOMAIN}.key" ]]; then
        log_warning "SSL certificates not found!"
        log_info "Generating self-signed certificate for testing..."
        ./scripts/generate-ssl-certs.sh dev
        log_warning "Please replace with valid SSL certificates for production!"
        log_info "Run: ./scripts/generate-ssl-certs.sh prod for instructions"
    fi
    
    # Build and start services
    log_info "Building and starting production services..."
    docker-compose -f docker-compose.nginx.yml --env-file .env.prod down --remove-orphans
    docker-compose -f docker-compose.nginx.yml --env-file .env.prod build --no-cache
    docker-compose -f docker-compose.nginx.yml --env-file .env.prod up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 60
    
    # Check service health
    check_service_health "production"
    
    log_success "Production environment deployed successfully!"
    log_info "Application is available at: https://${DOMAIN}"
    log_info "API is available at: https://${DOMAIN}/api"
}

# Check service health
check_service_health() {
    local env_type="$1"
    local base_url
    
    if [[ "$env_type" == "production" ]]; then
        base_url="https://${DOMAIN}"
    else
        base_url="http://localhost"
    fi
    
    log_info "Checking service health..."
    
    # Check if nginx is responding
    if curl -f -s "${base_url}/health" > /dev/null; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        log_info "Checking individual services..."
        
        if [[ "$env_type" == "production" ]]; then
            docker-compose -f docker-compose.nginx.yml logs --tail=20
        else
            docker-compose -f docker-compose.dev.yml logs --tail=20
        fi
    fi
}

# Stop services
stop_services() {
    local env_type="$1"
    
    log_info "Stopping $env_type services..."
    
    if [[ "$env_type" == "production" ]]; then
        docker-compose -f docker-compose.nginx.yml down
    else
        docker-compose -f docker-compose.dev.yml down
    fi
    
    log_success "$env_type services stopped"
}

# Show logs
show_logs() {
    local env_type="$1"
    local service="$2"
    
    if [[ "$env_type" == "production" ]]; then
        if [[ -n "$service" ]]; then
            docker-compose -f docker-compose.nginx.yml logs -f "$service"
        else
            docker-compose -f docker-compose.nginx.yml logs -f
        fi
    else
        if [[ -n "$service" ]]; then
            docker-compose -f docker-compose.dev.yml logs -f "$service"
        else
            docker-compose -f docker-compose.dev.yml logs -f
        fi
    fi
}

# Main script logic
case "$1" in
    "dev"|"development")
        check_dependencies
        create_directories
        deploy_development
        ;;
    "prod"|"production")
        check_dependencies
        create_directories
        deploy_production
        ;;
    "stop")
        if [[ "$2" == "prod" || "$2" == "production" ]]; then
            stop_services "production"
        else
            stop_services "development"
        fi
        ;;
    "logs")
        if [[ "$2" == "prod" || "$2" == "production" ]]; then
            show_logs "production" "$3"
        else
            show_logs "development" "$3"
        fi
        ;;
    "health")
        if [[ "$2" == "prod" || "$2" == "production" ]]; then
            check_service_health "production"
        else
            check_service_health "development"
        fi
        ;;
    *)
        echo -e "${YELLOW}Usage: $0 [dev|prod|stop|logs|health] [options]${NC}"
        echo ""
        echo -e "${YELLOW}Commands:${NC}"
        echo "  dev                    - Deploy development environment"
        echo "  prod                   - Deploy production environment"
        echo "  stop [dev|prod]        - Stop services"
        echo "  logs [dev|prod] [service] - Show logs"
        echo "  health [dev|prod]      - Check service health"
        echo ""
        echo -e "${YELLOW}Examples:${NC}"
        echo "  $0 dev                 # Deploy development"
        echo "  $0 prod                # Deploy production"
        echo "  $0 stop dev            # Stop development services"
        echo "  $0 logs prod nginx     # Show production nginx logs"
        echo "  $0 health dev          # Check development health"
        exit 1
        ;;
esac
