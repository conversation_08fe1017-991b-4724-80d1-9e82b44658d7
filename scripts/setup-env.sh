#!/bin/bash

# Environment Setup Script for WBS Aistech
# Sets up environment files for development and production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Generate secure random string
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Setup development environment
setup_development() {
    log_info "Setting up development environment..."
    
    # Backend development environment
    if [[ ! -f ".env.dev" ]]; then
        log_info "Creating backend development environment file..."
        cp .env.dev.example .env.dev
        log_success "Created .env.dev"
    else
        log_warning ".env.dev already exists, skipping..."
    fi
    
    # Frontend development environment
    if [[ ! -f "frontend/.env.development" ]]; then
        log_info "Creating frontend development environment file..."
        cp frontend/.env.development.example frontend/.env.development
        log_success "Created frontend/.env.development"
    else
        log_warning "frontend/.env.development already exists, skipping..."
    fi
    
    log_success "Development environment setup complete!"
    log_info "You can now run: ./scripts/deploy.sh dev"
}

# Setup production environment
setup_production() {
    log_info "Setting up production environment..."
    
    # Backend production environment
    if [[ ! -f ".env.prod" ]]; then
        log_info "Creating backend production environment file..."
        cp .env.prod.example .env.prod
        
        # Generate secure secrets
        log_info "Generating secure secrets..."
        JWT_ACCESS_SECRET=$(generate_secret)
        JWT_REFRESH_SECRET=$(generate_secret)
        SESSION_SECRET=$(generate_secret)
        REDIS_PASSWORD=$(generate_secret)
        DB_PASSWORD=$(generate_secret)
        
        # Replace placeholders with generated secrets
        sed -i.bak "s/CHANGE_THIS_TO_SECURE_RANDOM_STRING/$JWT_ACCESS_SECRET/g" .env.prod
        sed -i.bak "s/CHANGE_THIS_TO_DIFFERENT_SECURE_RANDOM_STRING/$JWT_REFRESH_SECRET/g" .env.prod
        sed -i.bak "s/CHANGE_THIS_SESSION_SECRET/$SESSION_SECRET/g" .env.prod
        sed -i.bak "s/CHANGE_THIS_REDIS_PASSWORD/$REDIS_PASSWORD/g" .env.prod
        sed -i.bak "s/CHANGE_THIS_SECURE_PASSWORD/$DB_PASSWORD/g" .env.prod
        
        # Remove backup file
        rm .env.prod.bak
        
        log_success "Created .env.prod with generated secrets"
        log_warning "Please review and update .env.prod with your specific configuration"
    else
        log_warning ".env.prod already exists, skipping..."
    fi
    
    # Frontend production environment
    if [[ ! -f "frontend/.env.production" ]]; then
        log_info "Creating frontend production environment file..."
        cp frontend/.env.production.example frontend/.env.production
        log_success "Created frontend/.env.production"
    else
        log_warning "frontend/.env.production already exists, skipping..."
    fi
    
    log_success "Production environment setup complete!"
    log_warning "IMPORTANT: Review and update the following files:"
    log_warning "  - .env.prod (database passwords, domain, external services)"
    log_warning "  - frontend/.env.production (domain, analytics, external services)"
    log_info "After configuration, run: ./scripts/deploy.sh prod"
}

# Validate environment files
validate_environment() {
    local env_type="$1"
    local errors=0
    
    log_info "Validating $env_type environment configuration..."
    
    if [[ "$env_type" == "development" ]]; then
        # Check development files
        if [[ ! -f ".env.dev" ]]; then
            log_error "Missing .env.dev file"
            errors=$((errors + 1))
        fi
        
        if [[ ! -f "frontend/.env.development" ]]; then
            log_error "Missing frontend/.env.development file"
            errors=$((errors + 1))
        fi
    else
        # Check production files
        if [[ ! -f ".env.prod" ]]; then
            log_error "Missing .env.prod file"
            errors=$((errors + 1))
        else
            # Check for placeholder values in production
            if grep -q "CHANGE_THIS" .env.prod; then
                log_error "Found placeholder values in .env.prod - please update configuration"
                errors=$((errors + 1))
            fi
        fi
        
        if [[ ! -f "frontend/.env.production" ]]; then
            log_error "Missing frontend/.env.production file"
            errors=$((errors + 1))
        fi
        
        # Check SSL certificates for production
        if [[ ! -f "ssl/certs/wbs-kg.aistech.id.crt" || ! -f "ssl/private/wbs-kg.aistech.id.key" ]]; then
            log_warning "SSL certificates not found - will use self-signed for testing"
        fi
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_success "$env_type environment validation passed"
        return 0
    else
        log_error "$env_type environment validation failed with $errors errors"
        return 1
    fi
}

# Show environment status
show_status() {
    log_info "Environment Status:"
    echo ""
    
    echo -e "${YELLOW}Backend Environment Files:${NC}"
    if [[ -f ".env.dev" ]]; then
        echo -e "  ${GREEN}✓${NC} .env.dev (development)"
    else
        echo -e "  ${RED}✗${NC} .env.dev (development)"
    fi
    
    if [[ -f ".env.prod" ]]; then
        echo -e "  ${GREEN}✓${NC} .env.prod (production)"
        if grep -q "CHANGE_THIS" .env.prod; then
            echo -e "    ${YELLOW}⚠${NC} Contains placeholder values"
        fi
    else
        echo -e "  ${RED}✗${NC} .env.prod (production)"
    fi
    
    echo ""
    echo -e "${YELLOW}Frontend Environment Files:${NC}"
    if [[ -f "frontend/.env.development" ]]; then
        echo -e "  ${GREEN}✓${NC} frontend/.env.development"
    else
        echo -e "  ${RED}✗${NC} frontend/.env.development"
    fi
    
    if [[ -f "frontend/.env.production" ]]; then
        echo -e "  ${GREEN}✓${NC} frontend/.env.production"
    else
        echo -e "  ${RED}✗${NC} frontend/.env.production"
    fi
    
    echo ""
    echo -e "${YELLOW}SSL Certificates:${NC}"
    if [[ -f "ssl/certs/wbs-kg.aistech.id.crt" && -f "ssl/private/wbs-kg.aistech.id.key" ]]; then
        echo -e "  ${GREEN}✓${NC} SSL certificates found"
    else
        echo -e "  ${YELLOW}⚠${NC} SSL certificates not found (use ./scripts/generate-ssl-certs.sh)"
    fi
}

# Main script logic
case "$1" in
    "dev"|"development")
        setup_development
        ;;
    "prod"|"production")
        setup_production
        ;;
    "validate")
        if [[ "$2" == "prod" || "$2" == "production" ]]; then
            validate_environment "production"
        else
            validate_environment "development"
        fi
        ;;
    "status")
        show_status
        ;;
    *)
        echo -e "${YELLOW}Usage: $0 [dev|prod|validate|status] [options]${NC}"
        echo ""
        echo -e "${YELLOW}Commands:${NC}"
        echo "  dev                    - Setup development environment"
        echo "  prod                   - Setup production environment"
        echo "  validate [dev|prod]    - Validate environment configuration"
        echo "  status                 - Show environment status"
        echo ""
        echo -e "${YELLOW}Examples:${NC}"
        echo "  $0 dev                 # Setup development environment"
        echo "  $0 prod                # Setup production environment"
        echo "  $0 validate prod       # Validate production configuration"
        echo "  $0 status              # Show current status"
        exit 1
        ;;
esac
