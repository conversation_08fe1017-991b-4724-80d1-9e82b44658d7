# Development Environment Variables for WBS Aistech
# Copy this file to .env.dev for local development

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
APP_PORT=3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Settings (Development)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=csrit_backend_dev
DB_USER=postgres
DB_PASS=password

# Database Connection Pool Settings
DB_POOL_MIN=1
DB_POOL_MAX=5
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# =============================================================================
# JWT AUTHENTICATION (Development)
# =============================================================================
# Simple secrets for development (NOT for production)
JWT_ACCESS_TOKEN_SECRET=dev_access_secret_key_12345
JWT_REFRESH_TOKEN_SECRET=dev_refresh_secret_key_67890
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Development CORS - allow localhost
CORS_ORIGIN=http://localhost



# =============================================================================
# EMAIL CONFIGURATION (Development - Optional)
# =============================================================================
# Use a test email service like Mailtrap for development
SMTP_HOST=sandbox.smtp.mailtrap.io
SMTP_PORT=2525
SMTP_SECURE=false
SMTP_USER=a61bd56579ef9f
SMTP_PASS=c8efac4266a17c

# Email Templates
EMAIL_FROM=dev@localhost
EMAIL_FROM_NAME=Aistech

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size in bytes (50MB for dev)
MAX_FILE_SIZE=52428800

# Allowed file types
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt

# Upload directory
UPLOAD_DIR=./uploads

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# More verbose logging for development
LOG_LEVEL=debug
LOG_DIR=./logs

# =============================================================================
# SECURITY CONFIGURATION (Relaxed for development)
# =============================================================================
# Rate limiting (more permissive for development)
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=1000

# Session configuration
SESSION_SECRET=dev_session_secret
SESSION_MAX_AGE=86400000

# =============================================================================
# EXTERNAL SERVICES (Development)
# =============================================================================
# N8N Webhook Configuration (local or test instance)
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# Third-party API keys (test keys)
EXTERNAL_API_KEY=test_api_key

# =============================================================================
# MONITORING AND HEALTH CHECKS
# =============================================================================
# Health check configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# BACKUP CONFIGURATION (Disabled for development)
# =============================================================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Cache settings (shorter TTL for development)
CACHE_TTL=300
CACHE_MAX_ITEMS=100

# Database query timeout
DB_QUERY_TIMEOUT=30000

# =============================================================================
# FEATURE FLAGS (All enabled for development)
# =============================================================================
FEATURE_CHAT_ENABLED=true
FEATURE_FILE_UPLOAD_ENABLED=true
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_AUDIT_LOGGING=true

# =============================================================================
# DEVELOPMENT/DEBUG (Enabled for development)
# =============================================================================
DEBUG_MODE=true
ENABLE_SWAGGER=true
ENABLE_CORS_CREDENTIALS=true

# Development specific settings
HOT_RELOAD=true
WATCH_MODE=true
