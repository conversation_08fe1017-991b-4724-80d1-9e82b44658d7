# WBS Aistech - Production Deployment Guide

This guide provides comprehensive instructions for deploying the WBS Aistech application with same-domain configuration (frontend at `https://wbs-kg.aistech.id`, API at `https://wbs-kg.aistech.id/api`).

## 🏗️ Architecture Overview

The application uses a reverse proxy architecture with Nginx:
- **Frontend**: Vue.js 3 application served as static files
- **Backend**: NestJS API server with global `/api` prefix
- **Reverse Proxy**: Nginx routes requests to appropriate services
- **Database**: PostgreSQL with Redis for caching
- **SSL**: HTTPS termination at Nginx level

## 📋 Prerequisites

### For GitLab CI/CD Deployment
- GitLab Runner with `docker` and `deploy` tags configured
- Docker and Docker Compose installed on deployment server
- GitLab Runner registered with your GitLab project
- Domain name configured (optional)
- Minimum 2GB RAM, 20GB disk space

### GitLab Runner Setup

1. **Install GitLab Runner on your deployment server**:
   ```bash
   # Download and install GitLab Runner
   curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash
   sudo apt-get install gitlab-runner
   ```

2. **Register GitLab Runner**:
   ```bash
   sudo gitlab-runner register
   ```
   - Enter your GitLab instance URL
   - Enter the registration token from your project's Settings → CI/CD → Runners
   - Enter a description for the runner
   - Enter tags: `docker,deploy`
   - Enter the executor: `shell`

3. **Configure Docker permissions**:
   ```bash
   sudo usermod -aG docker gitlab-runner
   sudo systemctl restart gitlab-runner
   ```

### For Manual Deployment
- Docker and Docker Compose installed
- Domain name configured (wbs-kg.aistech.id)
- SSL certificates (for production)
- Minimum 2GB RAM, 20GB disk space

## 🚀 Quick Start

### GitLab CI/CD Deployment (Recommended)

The project is configured for automatic deployment via GitLab CI/CD:

1. **Setup GitLab Variables** (see Configuration section below)
2. **Push to development branch**:
   ```bash
   git push origin development
   ```
3. **Monitor deployment** in GitLab CI/CD pipelines

The CI/CD pipeline will:
- Build Docker images for frontend and backend
- Deploy using `docker-compose.dev.yml`
- Use environment files from GitLab variables (ENV_FE, ENV_BE)
- Perform health checks

The application will be available at:
- Frontend: http://your-server-ip or https://your-domain.com
- API: http://your-server-ip/api or https://your-domain.com/api
- Health Check: http://your-server-ip/health

### Manual Development Deployment

```bash
# Clone the repository
git clone <repository-url>
cd be-wbs-kg

# Create environment files
cp .env.backend.example .env.backend
cp .env.frontend.example .env.frontend

# Deploy development environment
./scripts/deploy.sh dev
```

The application will be available at:
- Frontend: http://localhost
- API: http://localhost/api
- Health Check: http://localhost/health

### Manual Production Deployment

```bash
# Deploy production environment
./scripts/deploy.sh prod
```

The application will be available at:
- Frontend: https://wbs-kg.aistech.id
- API: https://wbs-kg.aistech.id/api
- Health Check: https://wbs-kg.aistech.id/health

## 📁 Project Structure

```
be-wbs-kg/
├── backend/                 # NestJS backend application
├── frontend/               # Vue.js frontend application
├── nginx/                  # Nginx configurations
│   ├── nginx.conf         # Production configuration
│   └── nginx.dev.conf     # Development configuration
├── scripts/               # Deployment scripts
│   ├── deploy.sh         # Main deployment script
│   └── generate-ssl-certs.sh # SSL certificate generation
├── ssl/                   # SSL certificates directory
│   ├── certs/            # Certificate files
│   └── private/          # Private key files
├── docker-compose.nginx.yml # Production Docker Compose
├── docker-compose.dev.yml   # Development Docker Compose
└── DEPLOYMENT.md          # This file
```

## 🔧 Configuration

### Environment Variables

#### GitLab CI/CD Variables Setup

For automated deployment via GitLab CI/CD, you need to set up environment files as GitLab file variables:

1. **Navigate to your GitLab project**: Settings → CI/CD → Variables
2. **Add the following file variables**:

**ENV_BE** (Type: File, Protected: Yes, Masked: No)
```env
NODE_ENV=production
DB_HOST=your_cloud_db_host
DB_PORT=5432
DB_NAME=csrit_backend
DB_USER=your_db_user
DB_PASS=your_secure_password
JWT_ACCESS_TOKEN_SECRET=your_jwt_secret_here
JWT_REFRESH_TOKEN_SECRET=your_refresh_secret_here
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400
CORS_ORIGIN=https://your-domain.com
PORT=3000
```

**ENV_FE** (Type: File, Protected: Yes, Masked: No)
```env
VITE_API_BASE_URL=/api
VITE_BACKEND_URL=https://your-domain.com
VITE_WS_URL=wss://your-domain.com
```

> **Note**: Frontend environment variables are processed at **build time**, not runtime. The CI/CD pipeline extracts these variables from ENV_FE and passes them as Docker build arguments. The built frontend image contains static files with these values already embedded.

#### Local Development Environment Variables

#### Backend (.env.backend)
```env
NODE_ENV=development
DB_HOST=your_cloud_db_host
DB_PORT=5432
DB_NAME=csrit_backend_dev
DB_USER=your_db_user
DB_PASS=your_secure_password
JWT_ACCESS_TOKEN_SECRET=dev_access_secret
JWT_REFRESH_TOKEN_SECRET=dev_refresh_secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400
CORS_ORIGIN=http://localhost
PORT=3000
```

#### Frontend (Build-time variables)
For local development, these are passed as Docker build arguments:
```env
VITE_API_BASE_URL=/api
VITE_BACKEND_URL=http://localhost
VITE_WS_URL=ws://localhost
```

> **Important**: Frontend environment variables must be available at **build time** because Vite embeds them into the static files during the build process.

### SSL Certificates

#### Development (Self-signed)
```bash
./scripts/generate-ssl-certs.sh dev
```

#### Production Options

**Option 1: Let's Encrypt (Recommended)**
```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d wbs-kg.aistech.id -d www.wbs-kg.aistech.id

# Copy to project
sudo cp /etc/letsencrypt/live/wbs-kg.aistech.id/fullchain.pem ssl/certs/wbs-kg.aistech.id.crt
sudo cp /etc/letsencrypt/live/wbs-kg.aistech.id/privkey.pem ssl/private/wbs-kg.aistech.id.key
```

**Option 2: Commercial Certificate**
```bash
# Generate CSR
openssl req -new -newkey rsa:2048 -nodes \
  -keyout ssl/private/wbs-kg.aistech.id.key \
  -out wbs-kg.aistech.id.csr

# Submit CSR to CA and place certificate at ssl/certs/wbs-kg.aistech.id.crt
```

## 🐳 Docker Commands

### Development
```bash
# Start development environment
./scripts/deploy.sh dev

# Stop services
./scripts/deploy.sh stop dev

# View logs
./scripts/deploy.sh logs dev

# View specific service logs
./scripts/deploy.sh logs dev backend

# Check health
./scripts/deploy.sh health dev
```

### Production
```bash
# Start production environment
./scripts/deploy.sh prod

# Stop services
./scripts/deploy.sh stop prod

# View logs
./scripts/deploy.sh logs prod

# Check health
./scripts/deploy.sh health prod
```

### Manual Docker Commands
```bash
# Development with environment files
ENV_FE=.env.frontend ENV_BE=.env.backend docker-compose -f docker-compose.dev.yml up -d
docker-compose -f docker-compose.dev.yml down

# Production
docker-compose -f docker-compose.nginx.yml --env-file .env.prod up -d
docker-compose -f docker-compose.nginx.yml down

# GitLab CI/CD equivalent commands (for reference)
export ENV_FE=.env.frontend
export ENV_BE=.env.backend
docker-compose -f docker-compose.dev.yml up -d
```

## 🔍 Monitoring and Troubleshooting

### Health Checks
- Backend: `GET /health`
- Frontend: `GET /` (should return index.html)
- Database: Automatic health checks in Docker Compose

### Log Locations
- Nginx: `docker-compose logs nginx`
- Backend: `docker-compose logs backend`
- Database: `docker-compose logs postgres`

### Common Issues

#### 1. SSL Certificate Errors
```bash
# Check certificate validity
openssl x509 -in ssl/certs/wbs-kg.aistech.id.crt -text -noout

# Regenerate development certificate
./scripts/generate-ssl-certs.sh dev
```

#### 2. API Not Accessible
```bash
# Check backend health
curl http://localhost:3000/health  # Direct backend
curl http://localhost/api/health   # Through nginx

# Check nginx configuration
docker exec csrit-nginx nginx -t
```

#### 3. Database Connection Issues
```bash
# Check database logs
docker-compose logs postgres

# Test database connection
docker exec -it csrit-postgres psql -U postgres -d csrit_backend
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Valid SSL certificates installed
- [ ] Strong database passwords
- [ ] JWT secrets are cryptographically secure
- [ ] CORS origins properly configured
- [ ] Rate limiting enabled in Nginx
- [ ] Security headers configured
- [ ] Non-root user in containers
- [ ] Firewall configured (ports 80, 443 only)

### Security Headers (Configured in Nginx)
- `X-Frame-Options: SAMEORIGIN`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`
- `Content-Security-Policy: ...`

## 📊 Performance Optimization

### Nginx Optimizations
- Gzip compression enabled
- Static file caching (1 year for assets)
- Connection keep-alive
- Buffer optimization

### Application Optimizations
- Production builds minified
- Tree-shaking enabled
- Code splitting implemented
- Health checks configured

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and redeploy
./scripts/deploy.sh prod
```

### SSL Certificate Renewal (Let's Encrypt)
```bash
# Automatic renewal (add to crontab)
0 12 * * * /usr/bin/certbot renew --quiet

# Manual renewal
sudo certbot renew
```

### Database Backups
```bash
# Create backup
docker exec csrit-postgres pg_dump -U postgres csrit_backend > backup.sql

# Restore backup
docker exec -i csrit-postgres psql -U postgres csrit_backend < backup.sql
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review application logs
3. Verify configuration files
4. Contact the development team

## 🔗 Related Documentation

- [NestJS Documentation](https://docs.nestjs.com/)
- [Vue.js Documentation](https://vuejs.org/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
