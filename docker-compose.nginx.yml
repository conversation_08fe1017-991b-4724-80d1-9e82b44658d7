# Docker Compose configuration with <PERSON><PERSON>x reverse proxy
# For same-domain deployment: frontend at https://wbs-kg.aistech.id, API at https://wbs-kg.aistech.id/api

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: csrit-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-csrit_backend}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - csrit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-csrit_backend}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NestJS Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: csrit-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-csrit_backend}
      DB_USER: ${DB_USER:-postgres}
      DB_PASS: ${DB_PASS:-password}
      JWT_ACCESS_TOKEN_SECRET: ${JWT_ACCESS_TOKEN_SECRET}
      JWT_REFRESH_TOKEN_SECRET: ${JWT_REFRESH_TOKEN_SECRET}
      JWT_ACCESS_TOKEN_EXPIRATION_TIME: ${JWT_ACCESS_TOKEN_EXPIRATION_TIME:-3600}
      JWT_REFRESH_TOKEN_EXPIRATION_TIME: ${JWT_REFRESH_TOKEN_EXPIRATION_TIME:-86400}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://wbs-kg.aistech.id}
      PORT: 3000
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - csrit-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Don't expose port directly - only accessible through nginx
    expose:
      - "3000"

  # Vue.js Frontend (build stage)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
      args:
        VITE_API_BASE_URL: /api
        VITE_BACKEND_URL: https://wbs-kg.aistech.id
        VITE_WS_URL: wss://wbs-kg.aistech.id
    container_name: csrit-frontend-build
    volumes:
      - frontend_dist:/app/dist
    networks:
      - csrit-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: csrit-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # Nginx configuration
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      # Frontend static files
      - frontend_dist:/usr/share/nginx/html:ro
      # SSL certificates (mount your SSL certificates here)
      - ./ssl/certs:/etc/ssl/certs:ro
      - ./ssl/private:/etc/ssl/private:ro
      # Logs
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - csrit-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3



volumes:
  postgres_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  frontend_dist:
    driver: local
  nginx_logs:
    driver: local

networks:
  csrit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
