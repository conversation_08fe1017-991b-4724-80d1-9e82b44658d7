#!/bin/bash

# 💾 PostgreSQL Backup and Restore Script
# Production-grade backup and disaster recovery solution

set -e  # Exit on any error

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-dbwbs-kg}"
DB_USER="${DB_USER:-aistech}"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/postgresql}"
LOG_DIR="${LOG_DIR:-/var/log/postgresql-backup}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
COMPRESSION_LEVEL="${COMPRESSION_LEVEL:-6}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_DIR/backup.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_DIR/backup.log"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_DIR/backup.log"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_DIR/backup.log"
}

# Initialize directories
init_directories() {
    mkdir -p "$BACKUP_DIR"/{daily,weekly,monthly}
    mkdir -p "$LOG_DIR"
    
    # Set proper permissions
    chmod 750 "$BACKUP_DIR"
    chmod 750 "$LOG_DIR"
}

# Database connectivity check
check_database_connection() {
    log "🔍 Checking database connectivity..."
    
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        error "Cannot connect to database. Please check connection parameters."
    fi
    
    success "✅ Database connection verified"
}

# Create full database backup
create_full_backup() {
    local backup_type="$1"  # daily, weekly, monthly
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/$backup_type/${DB_NAME}_${backup_type}_${timestamp}.sql"
    
    log "📦 Creating $backup_type backup: $backup_file"
    
    # Create backup with compression
    if pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --format=custom \
        --compress="$COMPRESSION_LEVEL" \
        --file="$backup_file.pgdump"; then
        
        # Create additional SQL dump for compatibility
        pg_dump \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            -d "$DB_NAME" \
            --no-password \
            --format=plain \
            --file="$backup_file"
        
        # Compress SQL file
        gzip "$backup_file"
        
        # Calculate checksums
        md5sum "$backup_file.pgdump" > "$backup_file.pgdump.md5"
        md5sum "$backup_file.gz" > "$backup_file.gz.md5"
        
        # Log backup info
        local size=$(du -h "$backup_file.pgdump" | cut -f1)
        log "📊 Backup size: $size"
        
        success "✅ $backup_type backup completed: $backup_file"
        
        # Update latest backup symlink
        ln -sf "$backup_file.pgdump" "$BACKUP_DIR/$backup_type/latest.pgdump"
        
        echo "$backup_file.pgdump" > "$BACKUP_DIR/$backup_type/latest_backup.txt"
        
    else
        error "Failed to create $backup_type backup"
    fi
}

# Create schema-only backup
create_schema_backup() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local schema_file="$BACKUP_DIR/schema/schema_${timestamp}.sql"
    
    log "📋 Creating schema backup: $schema_file"
    
    mkdir -p "$BACKUP_DIR/schema"
    
    if pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --schema-only \
        --no-password \
        --file="$schema_file"; then
        
        gzip "$schema_file"
        success "✅ Schema backup completed: $schema_file.gz"
    else
        error "Failed to create schema backup"
    fi
}

# Restore database from backup
restore_database() {
    local backup_file="$1"
    local target_db="${2:-${DB_NAME}_restore_$(date +%Y%m%d_%H%M%S)}"
    
    if [ ! -f "$backup_file" ]; then
        error "Backup file not found: $backup_file"
    fi
    
    log "🔄 Restoring database from: $backup_file"
    log "🎯 Target database: $target_db"
    
    # Verify backup integrity
    if [[ "$backup_file" == *.md5 ]]; then
        local original_file="${backup_file%.md5}"
        if ! md5sum -c "$backup_file"; then
            error "Backup file integrity check failed"
        fi
        backup_file="$original_file"
    fi
    
    # Create target database
    log "📝 Creating target database: $target_db"
    createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$target_db" || {
        warning "Database $target_db might already exist"
    }
    
    # Restore based on file type
    if [[ "$backup_file" == *.pgdump ]]; then
        # Custom format restore
        pg_restore \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            -d "$target_db" \
            --verbose \
            --no-password \
            --clean \
            --if-exists \
            "$backup_file"
    elif [[ "$backup_file" == *.sql.gz ]]; then
        # Compressed SQL restore
        gunzip -c "$backup_file" | psql \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            -d "$target_db"
    elif [[ "$backup_file" == *.sql ]]; then
        # Plain SQL restore
        psql \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            -U "$DB_USER" \
            -d "$target_db" \
            -f "$backup_file"
    else
        error "Unsupported backup file format: $backup_file"
    fi
    
    success "✅ Database restored to: $target_db"
}

# Cleanup old backups
cleanup_old_backups() {
    log "🧹 Cleaning up old backups (older than $RETENTION_DAYS days)"
    
    for backup_type in daily weekly monthly; do
        local deleted_count=0
        
        # Find and delete old backups
        while IFS= read -r -d '' file; do
            rm -f "$file"
            ((deleted_count++))
        done < <(find "$BACKUP_DIR/$backup_type" -name "*.pgdump" -mtime +$RETENTION_DAYS -print0)
        
        # Clean up associated files
        find "$BACKUP_DIR/$backup_type" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
        find "$BACKUP_DIR/$backup_type" -name "*.md5" -mtime +$RETENTION_DAYS -delete
        
        if [ $deleted_count -gt 0 ]; then
            log "🗑️  Deleted $deleted_count old $backup_type backups"
        fi
    done
    
    success "✅ Cleanup completed"
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        error "Backup file not found: $backup_file"
    fi
    
    log "🔍 Verifying backup integrity: $backup_file"
    
    # Check MD5 if available
    if [ -f "$backup_file.md5" ]; then
        if md5sum -c "$backup_file.md5"; then
            success "✅ Backup integrity verified (MD5)"
        else
            error "Backup integrity check failed (MD5)"
        fi
    fi
    
    # Test restore to temporary database
    local test_db="test_restore_$(date +%Y%m%d_%H%M%S)"
    
    log "🧪 Testing restore to temporary database: $test_db"
    
    if restore_database "$backup_file" "$test_db"; then
        # Verify table count
        local table_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$test_db" -t -c "
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        " | tr -d ' ')
        
        log "📊 Restored database has $table_count tables"
        
        # Cleanup test database
        dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$test_db"
        
        success "✅ Backup verification completed successfully"
    else
        error "Backup verification failed - restore test unsuccessful"
    fi
}

# List available backups
list_backups() {
    log "📋 Available backups:"
    
    for backup_type in daily weekly monthly; do
        echo -e "\n${YELLOW}$backup_type backups:${NC}"
        
        if [ -d "$BACKUP_DIR/$backup_type" ]; then
            find "$BACKUP_DIR/$backup_type" -name "*.pgdump" -printf "%T@ %Tc %p\n" | sort -n | while read timestamp date time file; do
                local size=$(du -h "$file" 2>/dev/null | cut -f1 || echo "N/A")
                echo "  $date $time - $file ($size)"
            done
        else
            echo "  No $backup_type backups found"
        fi
    done
}

# Main functions
case "${1:-}" in
    "daily")
        init_directories
        check_database_connection
        create_full_backup "daily"
        cleanup_old_backups
        ;;
    "weekly")
        init_directories
        check_database_connection
        create_full_backup "weekly"
        create_schema_backup
        cleanup_old_backups
        ;;
    "monthly")
        init_directories
        check_database_connection
        create_full_backup "monthly"
        create_schema_backup
        cleanup_old_backups
        ;;
    "restore")
        if [ -z "$2" ]; then
            error "Usage: $0 restore <backup_file> [target_database]"
        fi
        check_database_connection
        restore_database "$2" "$3"
        ;;
    "verify")
        if [ -z "$2" ]; then
            error "Usage: $0 verify <backup_file>"
        fi
        check_database_connection
        verify_backup "$2"
        ;;
    "list")
        list_backups
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    *)
        echo "Usage: $0 {daily|weekly|monthly|restore|verify|list|cleanup}"
        echo ""
        echo "Commands:"
        echo "  daily    - Create daily backup"
        echo "  weekly   - Create weekly backup with schema"
        echo "  monthly  - Create monthly backup with schema"
        echo "  restore  - Restore from backup file"
        echo "  verify   - Verify backup integrity"
        echo "  list     - List available backups"
        echo "  cleanup  - Remove old backups"
        exit 1
        ;;
esac
