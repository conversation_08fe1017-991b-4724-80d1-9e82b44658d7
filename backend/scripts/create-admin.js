#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const bcrypt = require('bcrypt');
const pgp = require('pg-promise')();

const connectionConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
};

const db = pgp(connectionConfig);

async function createAdminUser() {
  try {
    console.log('👤 Creating admin user...');
    
    // Check if admin user already exists
    const existingAdmin = await db.oneOrNone(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      ['admin', '<EMAIL>']
    );
    
    if (existingAdmin) {
      console.log('⚠️  Admin user already exists, skipping creation...');
      return;
    }
    
    // Get Admin role ID
    const adminRole = await db.oneOrNone(
      'SELECT id FROM master_role WHERE name = $1',
      ['Admin']
    );
    
    if (!adminRole) {
      console.error('❌ Admin role not found! Make sure roles are seeded first.');
      return;
    }
    
    // Generate admin user data
    const adminData = {
      id: 'aaaaaaaa-aaaa-4aaa-aaaa-aaaaaaaaaaaa',
      nama: 'System Administrator',
      username: 'admin',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      roleId: adminRole.id,
      status: 1,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // Insert admin user
    await db.none(`
      INSERT INTO users (id, nama, username, email, password, "roleId", status, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      adminData.id,
      adminData.nama,
      adminData.username,
      adminData.email,
      adminData.password,
      adminData.roleId,
      adminData.status,
      adminData.created_at,
      adminData.updated_at
    ]);
    
    console.log('✅ Admin user created successfully!');
    console.log('📋 Admin Credentials:');
    console.log('   Username: admin');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('⚠️  Please change the default password after first login!');
    
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
  } finally {
    pgp.end();
  }
}

createAdminUser();
