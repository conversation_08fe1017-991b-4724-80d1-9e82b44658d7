#!/bin/bash

# 🌩️ Aiven Cloud PostgreSQL Deployment Script
# Optimized for cloud database deployment with SSL and connection limits

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Load environment variables
load_env() {
    if [ -f ".env.local" ]; then
        log "📋 Loading environment from .env.local"
        export $(cat .env.local | grep -v '^#' | xargs)
    else
        error ".env.local file not found. Please create it first."
    fi
}

# Test cloud database connection
test_connection() {
    log "🔗 Testing Aiven cloud database connection..."
    
    # Test with psql if available
    if command -v psql &> /dev/null; then
        if psql "postgres://$DB_USER:$DB_PASS@$DB_HOST:$DB_PORT/$DB_NAME?sslmode=require" -c "SELECT version();" > /dev/null 2>&1; then
            success "✅ Direct PostgreSQL connection successful"
        else
            warning "⚠️ Direct psql connection failed, but Node.js connection may still work"
        fi
    else
        log "📝 psql not available, will test via Node.js"
    fi
    
    # Test Node.js connection
    node -e "
        const { Client } = require('pg');
        const client = new Client({
            host: '$DB_HOST',
            port: $DB_PORT,
            user: '$DB_USER',
            password: '$DB_PASS',
            database: '$DB_NAME',
            ssl: { rejectUnauthorized: false }
        });
        
        client.connect()
            .then(() => {
                console.log('✅ Node.js PostgreSQL connection successful');
                return client.query('SELECT version()');
            })
            .then((result) => {
                console.log('📊 Database version:', result.rows[0].version.split(' ')[0], result.rows[0].version.split(' ')[1]);
                return client.end();
            })
            .catch((err) => {
                console.error('❌ Connection failed:', err.message);
                process.exit(1);
            });
    "
}

# Create schema and check extensions
setup_schema() {
    log "🏗️ Setting up dedicated schema for wbs-kg..."

    node -e "
        const { Client } = require('pg');
        const client = new Client({
            host: '$DB_HOST',
            port: $DB_PORT,
            user: '$DB_USER',
            password: '$DB_PASS',
            database: '$DB_NAME',
            ssl: { rejectUnauthorized: false }
        });

        async function setupSchema() {
            await client.connect();

            // Create dedicated schema
            const schemaName = '$DB_SCHEMA' || 'wbs-kg_backend';
            try {
                await client.query(\`CREATE SCHEMA IF NOT EXISTS \\\"\${schemaName}\\\"\`);
                console.log(\`✅ Schema \${schemaName} created/verified\`);

                // Set search path to use our schema
                await client.query(\`SET search_path TO \\\"\${schemaName}\\\", public\`);
                console.log(\`✅ Search path set to \${schemaName}\`);
            } catch (err) {
                console.log(\`❌ Schema setup failed: \${err.message}\`);
            }

            // Check extensions (these are database-wide)
            const extensions = ['uuid-ossp', 'unaccent', 'pg_trgm', 'pg_stat_statements'];

            for (const ext of extensions) {
                try {
                    await client.query(\`CREATE EXTENSION IF NOT EXISTS \\\"\${ext}\\\"\`);
                    console.log(\`✅ Extension \${ext} available\`);
                } catch (err) {
                    if (err.message.includes('permission denied')) {
                        console.log(\`⚠️ Extension \${ext} requires superuser privileges (may already be available)\`);
                    } else {
                        console.log(\`❌ Extension \${ext} failed: \${err.message}\`);
                    }
                }
            }

            await client.end();
        }

        setupSchema().catch(console.error);
    "
}

# Pre-migration backup (cloud-friendly)
create_backup() {
    log "💾 Creating pre-migration backup..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if command -v pg_dump &> /dev/null; then
        if pg_dump "postgres://$DB_USER:$DB_PASS@$DB_HOST:$DB_PORT/$DB_NAME?sslmode=require" > "$backup_file"; then
            success "✅ Backup created: $backup_file"
            echo "$backup_file" > "latest_backup.txt"
        else
            warning "⚠️ Backup failed, but continuing with migration"
        fi
    else
        warning "⚠️ pg_dump not available, skipping backup"
    fi
}

# Install dependencies and build
prepare_application() {
    log "📦 Preparing application..."
    
    # Install dependencies
    if [ ! -d "node_modules" ]; then
        log "Installing dependencies..."
        npm install
    fi
    
    # Build application
    log "Building application..."
    npm run build
    
    success "✅ Application prepared"
}

# Run migrations with cloud optimizations
run_migrations() {
    log "🚀 Running database migrations..."
    
    # Set cloud-optimized environment
    export DB_SSL=true
    export DB_SSL_REJECT_UNAUTHORIZED=false
    export DB_POOL_MAX=10  # Lower for cloud
    export DB_POOL_MIN=2
    export DB_QUERY_TIMEOUT=45000  # Higher for cloud latency
    
    # Run TypeORM migrations
    if npm run migration:run; then
        success "✅ Migrations completed successfully"
    else
        error "❌ Migration failed"
    fi
}

# Verify optimizations
verify_optimizations() {
    log "🔍 Verifying database optimizations..."
    
    node -e "
        const { Client } = require('pg');
        const client = new Client({
            host: '$DB_HOST',
            port: $DB_PORT,
            user: '$DB_USER',
            password: '$DB_PASS',
            database: '$DB_NAME',
            ssl: { rejectUnauthorized: false }
        });
        
        async function verify() {
            await client.connect();
            
            // Check optimization indexes
            const indexResult = await client.query(\`
                SELECT COUNT(*) as index_count 
                FROM pg_indexes 
                WHERE schemaname = 'public' 
                    AND indexname LIKE 'idx_%'
            \`);
            
            console.log(\`📊 Optimization indexes created: \${indexResult.rows[0].index_count}\`);
            
            // Check critical indexes
            const criticalIndexes = [
                'idx_berita_tgl_posting_desc',
                'idx_chat_messages_room_timestamp',
                'idx_users_role_status',
                'idx_user_activity_username_created'
            ];
            
            for (const indexName of criticalIndexes) {
                const result = await client.query(\`
                    SELECT COUNT(*) as exists 
                    FROM pg_indexes 
                    WHERE indexname = \$1 AND schemaname = 'public'
                \`, [indexName]);
                
                if (result.rows[0].exists === '1') {
                    console.log(\`✅ Critical index verified: \${indexName}\`);
                } else {
                    console.log(\`❌ Critical index missing: \${indexName}\`);
                }
            }
            
            // Check table statistics
            const tableResult = await client.query(\`
                SELECT 
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE schemaname = 'public' 
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
                LIMIT 5
            \`);
            
            console.log('\\n📊 Largest tables:');
            tableResult.rows.forEach(row => {
                console.log(\`  \${row.tablename}: \${row.size}\`);
            });
            
            await client.end();
        }
        
        verify().catch(console.error);
    "
}

# Performance test (cloud-optimized)
run_performance_test() {
    log "📊 Running cloud-optimized performance test..."
    
    node -e "
        const { Client } = require('pg');
        const client = new Client({
            host: '$DB_HOST',
            port: $DB_PORT,
            user: '$DB_USER',
            password: '$DB_PASS',
            database: '$DB_NAME',
            ssl: { rejectUnauthorized: false }
        });
        
        async function performanceTest() {
            await client.connect();
            
            console.log('🧪 Testing content query performance...');
            
            // Test content queries
            const tests = [
                {
                    name: 'Berita listing',
                    query: 'SELECT * FROM berita ORDER BY tgl_posting DESC LIMIT 10'
                },
                {
                    name: 'Artikel by category',
                    query: \"SELECT * FROM artikel WHERE kategori = 'teknologi' ORDER BY tgl_posting DESC LIMIT 5\"
                },
                {
                    name: 'User with role',
                    query: 'SELECT u.*, r.name FROM users u LEFT JOIN master_role r ON u.\"roleId\" = r.id LIMIT 5'
                }
            ];
            
            for (const test of tests) {
                const start = Date.now();
                try {
                    await client.query(test.query);
                    const duration = Date.now() - start;
                    console.log(\`  ✅ \${test.name}: \${duration}ms\`);
                } catch (err) {
                    console.log(\`  ❌ \${test.name}: \${err.message}\`);
                }
            }
            
            await client.end();
        }
        
        performanceTest().catch(console.error);
    "
}

# Main deployment function
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              Aiven Cloud PostgreSQL Deployment              ║"
    echo "║                Database Optimization Migration               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    load_env
    test_connection
    create_backup
    prepare_application
    run_migrations
    verify_optimizations
    run_performance_test
    
    echo ""
    success "🎉 Aiven cloud deployment completed successfully!"
    echo ""
    log "📊 Expected performance improvements:"
    log "  • Content queries: 70-80% faster"
    log "  • Chat system: 60-70% faster"
    log "  • User management: 50-60% faster"
    echo ""
    log "🚀 Start your application with: npm run start:dev"
    log "🌐 Database: $DB_HOST:$DB_PORT"
    log "💾 Backup: $(cat latest_backup.txt 2>/dev/null || echo 'No backup created')"
}

# Handle rollback
rollback() {
    log "🔄 Rolling back migrations..."
    
    if [ -f "latest_backup.txt" ]; then
        local backup_file=$(cat latest_backup.txt)
        if [ -f "$backup_file" ]; then
            warning "⚠️ Restoring from backup: $backup_file"
            if psql "postgres://$DB_USER:$DB_PASS@$DB_HOST:$DB_PORT/$DB_NAME?sslmode=require" < "$backup_file"; then
                success "✅ Database restored from backup"
            else
                error "❌ Failed to restore from backup"
            fi
        else
            error "Backup file not found: $backup_file"
        fi
    else
        error "No backup information found"
    fi
}

# Command handling
case "${1:-}" in
    "rollback")
        load_env
        rollback
        ;;
    "test")
        load_env
        test_connection
        ;;
    "verify")
        load_env
        verify_optimizations
        ;;
    *)
        main
        ;;
esac
