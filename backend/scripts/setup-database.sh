#!/bin/bash

# 🚀 Database Setup Script for WBS-KG Backend
# This script sets up the database with migrations and seeds

set -e  # Exit on any error

echo "🔧 WBS-KG Database Setup"
echo "========================"

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "❌ Error: .env.local file not found!"
    echo "Please create .env.local with your database configuration."
    exit 1
fi

# Load environment variables
source .env.local

echo "📍 Database: $DB_NAME@$DB_HOST:$DB_PORT"
echo "👤 User: $DB_USER"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Install required packages for migration script
echo "📦 Installing migration dependencies..."
npm install --save-dev pg-promise bcrypt uuid

# Make migration script executable
chmod +x ./scripts/migrate-and-seed.js

# Run the migration and seeding
echo "🚀 Running database migration and seeding..."
npm run migrate:dev

echo ""
echo "🎉 Database setup completed!"
echo ""
echo "📋 Default Admin Credentials:"
echo "   Username: admin"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "⚠️  IMPORTANT: Change the default password after first login!"
echo ""
echo "🔗 Next steps:"
echo "1. Start the application: npm run start:dev"
echo "2. Login with admin credentials"
echo "3. Change the default password"
echo "4. Create additional users as needed"
