#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });
const bcrypt = require('bcrypt');
const pgp = require('pg-promise')();

const connectionConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
};

const db = pgp(connectionConfig);

async function createTestUser() {
  try {
    console.log('👤 Creating test user...');
    
    // Check if test user already exists
    const existingUser = await db.oneOrNone(
      'SELECT id FROM users WHERE username = $1',
      ['testuser']
    );
    
    if (existingUser) {
      console.log('⚠️  Test user already exists, skipping creation...');
      return;
    }
    
    // Get User role ID
    const userRole = await db.oneOrNone(
      'SELECT id FROM master_role WHERE name = $1',
      ['User']
    );
    
    if (!userRole) {
      console.error('❌ User role not found!');
      return;
    }
    
    // Generate test user data
    const userData = {
      id: 'bbbbbbbb-bbbb-4bbb-bbbb-bbbbbbbbbbbb',
      nama: 'Test User',
      username: 'testuser',
      email: '<EMAIL>',
      password: await bcrypt.hash('test123', 10),
      roleId: userRole.id,
      status: 1,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // Insert test user
    await db.none(`
      INSERT INTO users (id, nama, username, email, password, "roleId", status, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      userData.id,
      userData.nama,
      userData.username,
      userData.email,
      userData.password,
      userData.roleId,
      userData.status,
      userData.created_at,
      userData.updated_at
    ]);
    
    console.log('✅ Test user created successfully!');
    console.log('📋 Test User Credentials:');
    console.log('   Username: testuser');
    console.log('   Email: <EMAIL>');
    console.log('   Password: test123');
    
  } catch (error) {
    console.error('❌ Failed to create test user:', error.message);
  } finally {
    pgp.end();
  }
}

createTestUser();
