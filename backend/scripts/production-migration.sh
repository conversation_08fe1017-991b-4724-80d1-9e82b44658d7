#!/bin/bash

# 🚀 Production Database Optimization Migration Script
# This script safely applies database optimizations with zero downtime

set -e  # Exit on any error

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-dbwbs-kg}"
DB_USER="${DB_USER:-aistech}"
BACKUP_DIR="/tmp/db_backups"
LOG_FILE="/tmp/migration_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Pre-migration checks
pre_migration_checks() {
    log "🔍 Starting pre-migration checks..."
    
    # Check database connectivity
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        error "Cannot connect to database. Please check connection parameters."
    fi
    
    # Check disk space (need at least 1GB free)
    available_space=$(df /tmp | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 1048576 ]; then
        error "Insufficient disk space. Need at least 1GB free in /tmp"
    fi
    
    # Check if migrations directory exists
    if [ ! -d "src/migrations" ]; then
        error "Migrations directory not found. Please run from project root."
    fi
    
    # Check for existing indexes to avoid conflicts
    log "Checking for existing indexes..."
    existing_indexes=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM pg_indexes 
        WHERE indexname LIKE 'idx_%' 
        AND schemaname = 'public'
    " | tr -d ' ')
    
    log "Found $existing_indexes existing custom indexes"
    
    success "✅ Pre-migration checks completed"
}

# Create database backup
create_backup() {
    log "📦 Creating database backup..."
    
    mkdir -p "$BACKUP_DIR"
    backup_file="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$backup_file"; then
        success "✅ Backup created: $backup_file"
        echo "$backup_file" > "$BACKUP_DIR/latest_backup.txt"
    else
        error "Failed to create database backup"
    fi
}

# Validate migration files
validate_migrations() {
    log "🔍 Validating migration files..."
    
    migration_files=(
        "src/migrations/1735000001000-AddContentPerformanceIndexes.ts"
        "src/migrations/1735000002000-AddChatSystemIndexes.ts"
        "src/migrations/1735000003000-AddUserManagementIndexes.ts"
        "src/migrations/1735000004000-AddAdvancedPerformanceIndexes.ts"
        "src/migrations/1735000005000-AddFullTextSearchIndexes.ts"
    )
    
    for file in "${migration_files[@]}"; do
        if [ ! -f "$file" ]; then
            error "Migration file not found: $file"
        fi
        log "✓ Found: $file"
    done
    
    success "✅ All migration files validated"
}

# Run performance baseline
run_baseline() {
    log "📊 Running performance baseline..."
    
    baseline_file="/tmp/performance_baseline_$(date +%Y%m%d_%H%M%S).txt"
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF > "$baseline_file"
-- Performance Baseline Queries
\timing on

-- Content queries (current performance)
EXPLAIN ANALYZE SELECT * FROM berita ORDER BY tgl_posting DESC LIMIT 10;
EXPLAIN ANALYZE SELECT * FROM artikel WHERE kategori = 'teknologi' ORDER BY tgl_posting DESC LIMIT 10;
EXPLAIN ANALYZE SELECT * FROM layanan ORDER BY tgl_posting DESC LIMIT 10;

-- Chat system queries
EXPLAIN ANALYZE SELECT * FROM chat_rooms WHERE status = 'active' ORDER BY last_message_at DESC LIMIT 10;
EXPLAIN ANALYZE SELECT * FROM chat_messages WHERE chatRoomId = (SELECT id FROM chat_rooms LIMIT 1) ORDER BY timestamp DESC LIMIT 50;

-- User management queries
EXPLAIN ANALYZE SELECT * FROM users WHERE status = 1;
EXPLAIN ANALYZE SELECT * FROM user_activity WHERE username = 'admin' ORDER BY created_at DESC LIMIT 20;

\timing off
EOF
    
    log "📊 Baseline saved to: $baseline_file"
}

# Execute migrations
execute_migrations() {
    log "🚀 Starting migration execution..."
    
    # Build the project first
    log "Building project..."
    if ! npm run build; then
        error "Failed to build project"
    fi
    
    # Run migrations
    log "Executing TypeORM migrations..."
    if ! npm run migration:run; then
        error "Migration execution failed"
    fi
    
    success "✅ Migrations executed successfully"
}

# Verify migrations
verify_migrations() {
    log "🔍 Verifying migration results..."
    
    # Check if indexes were created
    created_indexes=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM pg_indexes 
        WHERE indexname LIKE 'idx_%' 
        AND schemaname = 'public'
    " | tr -d ' ')
    
    log "Total custom indexes after migration: $created_indexes"
    
    # Verify specific critical indexes
    critical_indexes=(
        "idx_berita_tgl_posting_desc"
        "idx_chat_messages_room_timestamp"
        "idx_users_role_status"
        "idx_user_activity_username_created"
    )
    
    for index in "${critical_indexes[@]}"; do
        exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
            SELECT COUNT(*) FROM pg_indexes 
            WHERE indexname = '$index' 
            AND schemaname = 'public'
        " | tr -d ' ')
        
        if [ "$exists" -eq 1 ]; then
            log "✓ Critical index verified: $index"
        else
            warning "⚠ Critical index missing: $index"
        fi
    done
    
    success "✅ Migration verification completed"
}

# Run performance comparison
run_performance_test() {
    log "📊 Running post-migration performance test..."
    
    performance_file="/tmp/performance_after_$(date +%Y%m%d_%H%M%S).txt"
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF > "$performance_file"
-- Post-Migration Performance Test
\timing on

-- Content queries (should be much faster now)
EXPLAIN ANALYZE SELECT * FROM berita ORDER BY tgl_posting DESC LIMIT 10;
EXPLAIN ANALYZE SELECT * FROM artikel WHERE kategori = 'teknologi' ORDER BY tgl_posting DESC LIMIT 10;
EXPLAIN ANALYZE SELECT * FROM layanan ORDER BY tgl_posting DESC LIMIT 10;

-- Chat system queries
EXPLAIN ANALYZE SELECT * FROM chat_rooms WHERE status = 'active' ORDER BY last_message_at DESC LIMIT 10;
EXPLAIN ANALYZE SELECT * FROM chat_messages WHERE chatRoomId = (SELECT id FROM chat_rooms LIMIT 1) ORDER BY timestamp DESC LIMIT 50;

-- User management queries
EXPLAIN ANALYZE SELECT * FROM users WHERE status = 1;
EXPLAIN ANALYZE SELECT * FROM user_activity WHERE username = 'admin' ORDER BY created_at DESC LIMIT 20;

\timing off
EOF
    
    log "📊 Performance test results saved to: $performance_file"
}

# Main execution
main() {
    log "🚀 Starting Production Database Optimization"
    log "📝 Log file: $LOG_FILE"
    
    pre_migration_checks
    create_backup
    validate_migrations
    run_baseline
    execute_migrations
    verify_migrations
    run_performance_test
    
    success "🎉 Production database optimization completed successfully!"
    log "📊 Performance comparison files available in /tmp/"
    log "📦 Database backup available at: $(cat $BACKUP_DIR/latest_backup.txt)"
    log "📝 Full log available at: $LOG_FILE"
}

# Rollback function
rollback() {
    log "🔄 Starting rollback procedure..."
    
    if [ -f "$BACKUP_DIR/latest_backup.txt" ]; then
        backup_file=$(cat "$BACKUP_DIR/latest_backup.txt")
        if [ -f "$backup_file" ]; then
            warning "⚠ Restoring database from backup: $backup_file"
            if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" < "$backup_file"; then
                success "✅ Database restored from backup"
            else
                error "Failed to restore database from backup"
            fi
        else
            error "Backup file not found: $backup_file"
        fi
    else
        error "No backup information found"
    fi
}

# Handle script arguments
case "${1:-}" in
    "rollback")
        rollback
        ;;
    *)
        main
        ;;
esac
