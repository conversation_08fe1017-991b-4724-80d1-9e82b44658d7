#!/bin/bash

# 📊 PostgreSQL Performance Benchmark Script
# Comprehensive testing framework for database optimization validation

set -e

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-dbwbs-kg}"
DB_USER="${DB_USER:-aistech}"
BENCHMARK_DIR="/tmp/performance_benchmarks"
RESULTS_DIR="$BENCHMARK_DIR/results"
TEST_ITERATIONS="${TEST_ITERATIONS:-5}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Initialize benchmark environment
init_benchmark() {
    log "🚀 Initializing performance benchmark environment"
    
    mkdir -p "$RESULTS_DIR"
    
    # Check database connectivity
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        error "Cannot connect to database"
    fi
    
    # Check if pg_stat_statements is available
    local has_pg_stat_statements=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM pg_extension WHERE extname = 'pg_stat_statements'
    " | tr -d ' ')
    
    if [ "$has_pg_stat_statements" -eq 0 ]; then
        warning "pg_stat_statements extension not found. Some metrics will be unavailable."
    fi
    
    success "✅ Benchmark environment initialized"
}

# Reset statistics
reset_stats() {
    log "🔄 Resetting database statistics"
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF'
-- Reset PostgreSQL statistics
SELECT pg_stat_reset();

-- Reset pg_stat_statements if available
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements') THEN
        PERFORM pg_stat_statements_reset();
    END IF;
END $$;
EOF
    
    success "✅ Statistics reset completed"
}

# Content performance benchmark
benchmark_content_queries() {
    local test_name="content_queries"
    local result_file="$RESULTS_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).txt"
    
    log "📰 Running content queries benchmark"
    
    cat > /tmp/content_benchmark.sql << 'EOF'
\timing on
\set QUIET on

-- Test 1: Berita listing (most common query)
\echo '=== BERITA LISTING TEST ==='
SELECT 'Test 1: Berita listing' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM berita ORDER BY tgl_posting DESC LIMIT 20;

-- Test 2: Berita by category
\echo '=== BERITA CATEGORY FILTER TEST ==='
SELECT 'Test 2: Berita category filter' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM berita WHERE kategori = 'teknologi' ORDER BY tgl_posting DESC LIMIT 10;

-- Test 3: Artikel listing
\echo '=== ARTIKEL LISTING TEST ==='
SELECT 'Test 3: Artikel listing' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM artikel ORDER BY tgl_posting DESC LIMIT 20;

-- Test 4: Layanan listing
\echo '=== LAYANAN LISTING TEST ==='
SELECT 'Test 4: Layanan listing' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM layanan ORDER BY tgl_posting DESC LIMIT 20;

-- Test 5: Event listing
\echo '=== EVENT LISTING TEST ==='
SELECT 'Test 5: Event listing' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM event ORDER BY created_at DESC LIMIT 20;

-- Test 6: Content search simulation
\echo '=== CONTENT SEARCH TEST ==='
SELECT 'Test 6: Content search' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT 'berita' as type, judul, tgl_posting FROM berita WHERE judul ILIKE '%keamanan%'
UNION ALL
SELECT 'artikel' as type, judul, tgl_posting FROM artikel WHERE judul ILIKE '%keamanan%'
UNION ALL
SELECT 'layanan' as type, judul, tgl_posting FROM layanan WHERE judul ILIKE '%keamanan%'
ORDER BY tgl_posting DESC;

\timing off
EOF

    # Run benchmark multiple times
    for i in $(seq 1 $TEST_ITERATIONS); do
        echo "=== ITERATION $i ===" >> "$result_file"
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/content_benchmark.sql >> "$result_file" 2>&1
        echo "" >> "$result_file"
    done
    
    success "✅ Content queries benchmark completed: $result_file"
}

# Chat system performance benchmark
benchmark_chat_queries() {
    local test_name="chat_queries"
    local result_file="$RESULTS_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).txt"
    
    log "💬 Running chat system benchmark"
    
    cat > /tmp/chat_benchmark.sql << 'EOF'
\timing on
\set QUIET on

-- Test 1: Active chat rooms
\echo '=== ACTIVE CHAT ROOMS TEST ==='
SELECT 'Test 1: Active chat rooms' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM chat_rooms WHERE status = 'active' ORDER BY last_message_at DESC LIMIT 20;

-- Test 2: Chat room messages
\echo '=== CHAT ROOM MESSAGES TEST ==='
SELECT 'Test 2: Chat room messages' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT m.*, 
       CASE WHEN m."senderGuestUserId" IS NOT NULL THEN 'guest' ELSE 'operator' END as sender_type
FROM chat_messages m 
WHERE m."chatRoomId" = (SELECT id FROM chat_rooms LIMIT 1)
ORDER BY m.timestamp DESC LIMIT 50;

-- Test 3: User chat rooms with participants
\echo '=== USER CHAT ROOMS TEST ==='
SELECT 'Test 3: User chat rooms with participants' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT r.*, p.participant_type
FROM chat_rooms r
INNER JOIN chat_room_participants p ON r.id = p."chatRoomId"
WHERE p."guestUserId" = (SELECT id FROM chat_users LIMIT 1)
ORDER BY r.last_message_at DESC;

-- Test 4: Recent messages across all rooms
\echo '=== RECENT MESSAGES TEST ==='
SELECT 'Test 4: Recent messages' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT m.*, r.title as room_title
FROM chat_messages m
INNER JOIN chat_rooms r ON m."chatRoomId" = r.id
WHERE m.created_at > NOW() - INTERVAL '1 hour'
  AND m.is_deleted = false
ORDER BY m.timestamp DESC LIMIT 100;

-- Test 5: Chat statistics
\echo '=== CHAT STATISTICS TEST ==='
SELECT 'Test 5: Chat statistics' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT 
    (SELECT COUNT(*) FROM chat_rooms WHERE status = 'active') as active_rooms,
    (SELECT COUNT(*) FROM chat_users WHERE is_active = true) as active_users,
    (SELECT COUNT(*) FROM chat_messages WHERE created_at > NOW() - INTERVAL '1 hour') as messages_last_hour;

\timing off
EOF

    # Run benchmark multiple times
    for i in $(seq 1 $TEST_ITERATIONS); do
        echo "=== ITERATION $i ===" >> "$result_file"
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/chat_benchmark.sql >> "$result_file" 2>&1
        echo "" >> "$result_file"
    done
    
    success "✅ Chat system benchmark completed: $result_file"
}

# User management benchmark
benchmark_user_queries() {
    local test_name="user_queries"
    local result_file="$RESULTS_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).txt"
    
    log "👤 Running user management benchmark"
    
    cat > /tmp/user_benchmark.sql << 'EOF'
\timing on
\set QUIET on

-- Test 1: User listing with roles
\echo '=== USER LISTING TEST ==='
SELECT 'Test 1: User listing with roles' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT u.*, r.name as role_name 
FROM users u 
LEFT JOIN master_role r ON u."roleId" = r.id 
WHERE u.status = 1 
ORDER BY u.created_at DESC;

-- Test 2: User activity log
\echo '=== USER ACTIVITY LOG TEST ==='
SELECT 'Test 2: User activity log' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT * FROM user_activity 
WHERE username = (SELECT username FROM users LIMIT 1)
ORDER BY created_at DESC LIMIT 50;

-- Test 3: Role-based access check
\echo '=== ROLE ACCESS CHECK TEST ==='
SELECT 'Test 3: Role access check' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT u.username, r.name as role, ha.id_menu
FROM users u
INNER JOIN master_role r ON u."roleId" = r.id
INNER JOIN hak_akses ha ON r.id = ha."roleId"
WHERE u.status = 1;

-- Test 4: Recent user activity
\echo '=== RECENT ACTIVITY TEST ==='
SELECT 'Test 4: Recent user activity' as test_name;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT username, route, action, created_at
FROM user_activity 
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC LIMIT 100;

\timing off
EOF

    # Run benchmark multiple times
    for i in $(seq 1 $TEST_ITERATIONS); do
        echo "=== ITERATION $i ===" >> "$result_file"
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f /tmp/user_benchmark.sql >> "$result_file" 2>&1
        echo "" >> "$result_file"
    done
    
    success "✅ User management benchmark completed: $result_file"
}

# Index usage analysis
analyze_index_usage() {
    local result_file="$RESULTS_DIR/index_analysis_$(date +%Y%m%d_%H%M%S).txt"
    
    log "📊 Analyzing index usage"
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF' > "$result_file"
-- Index Usage Analysis
\echo '=== INDEX USAGE STATISTICS ==='

SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    pg_size_pretty(pg_relation_size(indexrelid)) as size,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        ELSE 'GOOD_USAGE'
    END as usage_status
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

\echo '=== TABLE SCAN ANALYSIS ==='

SELECT 
    schemaname,
    tablename,
    seq_scan as sequential_scans,
    idx_scan as index_scans,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    CASE 
        WHEN seq_scan > idx_scan AND seq_scan > 100 THEN 'NEEDS_INDEX'
        WHEN idx_scan > seq_scan THEN 'GOOD_INDEX_USAGE'
        ELSE 'LOW_ACTIVITY'
    END as recommendation
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY seq_scan DESC;

\echo '=== CACHE HIT RATIO ==='

SELECT 
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    CASE 
        WHEN heap_blks_read + heap_blks_hit = 0 THEN 0
        ELSE ROUND((heap_blks_hit::numeric / (heap_blks_read + heap_blks_hit)) * 100, 2)
    END as cache_hit_ratio
FROM pg_statio_user_tables 
WHERE schemaname = 'public'
    AND (heap_blks_read + heap_blks_hit) > 0
ORDER BY cache_hit_ratio ASC;
EOF
    
    success "✅ Index analysis completed: $result_file"
}

# Generate performance report
generate_report() {
    local report_file="$RESULTS_DIR/performance_report_$(date +%Y%m%d_%H%M%S).html"
    
    log "📋 Generating performance report"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Database Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e8f4f8; border-radius: 3px; }
        .good { background: #d4edda; }
        .warning { background: #fff3cd; }
        .danger { background: #f8d7da; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Database Performance Report</h1>
        <p>Generated: $(date)</p>
        <p>Database: $DB_NAME @ $DB_HOST:$DB_PORT</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <div class="metric">Content Queries: $(ls $RESULTS_DIR/content_queries_*.txt 2>/dev/null | wc -l) tests</div>
        <div class="metric">Chat Queries: $(ls $RESULTS_DIR/chat_queries_*.txt 2>/dev/null | wc -l) tests</div>
        <div class="metric">User Queries: $(ls $RESULTS_DIR/user_queries_*.txt 2>/dev/null | wc -l) tests</div>
        <div class="metric">Iterations per test: $TEST_ITERATIONS</div>
    </div>
    
    <div class="section">
        <h2>Database Statistics</h2>
        <pre>$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT 
    'Total Tables' as metric, COUNT(*) as value
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
UNION ALL
SELECT 
    'Total Indexes' as metric, COUNT(*) as value
FROM pg_indexes 
WHERE schemaname = 'public'
UNION ALL
SELECT 
    'Database Size' as metric, pg_size_pretty(pg_database_size('$DB_NAME')) as value;
")</pre>
    </div>
    
    <div class="section">
        <h2>Recent Test Files</h2>
        <ul>
EOF

    # Add links to recent test files
    find "$RESULTS_DIR" -name "*.txt" -mtime -1 | sort -r | head -10 | while read file; do
        echo "            <li><a href=\"file://$file\">$(basename "$file")</a></li>" >> "$report_file"
    done

    cat >> "$report_file" << EOF
        </ul>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ul>
            <li>Review index usage statistics in the analysis files</li>
            <li>Monitor cache hit ratios (should be > 95%)</li>
            <li>Check for tables with high sequential scan counts</li>
            <li>Verify query execution times are within acceptable limits</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    success "✅ Performance report generated: $report_file"
}

# Main execution
case "${1:-}" in
    "full")
        init_benchmark
        reset_stats
        benchmark_content_queries
        benchmark_chat_queries
        benchmark_user_queries
        analyze_index_usage
        generate_report
        ;;
    "content")
        init_benchmark
        reset_stats
        benchmark_content_queries
        ;;
    "chat")
        init_benchmark
        reset_stats
        benchmark_chat_queries
        ;;
    "users")
        init_benchmark
        reset_stats
        benchmark_user_queries
        ;;
    "indexes")
        init_benchmark
        analyze_index_usage
        ;;
    "report")
        generate_report
        ;;
    *)
        echo "Usage: $0 {full|content|chat|users|indexes|report}"
        echo ""
        echo "Commands:"
        echo "  full     - Run complete benchmark suite"
        echo "  content  - Test content queries only"
        echo "  chat     - Test chat system queries only"
        echo "  users    - Test user management queries only"
        echo "  indexes  - Analyze index usage only"
        echo "  report   - Generate HTML report"
        echo ""
        echo "Environment variables:"
        echo "  TEST_ITERATIONS - Number of test iterations (default: 5)"
        echo "  DB_HOST, DB_PORT, DB_USER, DB_NAME - Database connection"
        exit 1
        ;;
esac

log "🎉 Benchmark completed. Results available in: $RESULTS_DIR"
