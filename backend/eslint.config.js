import js from '@eslint/js';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsparser from '@typescript-eslint/parser';
import unusedImports from 'eslint-plugin-unused-imports';
import globals from 'globals';

export default [
  js.configs.recommended,
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: import.meta.dirname,
        sourceType: 'module',
      },
      globals: {
        ...globals.node,
        ...globals.jest,
        Express: 'readonly',
        TokenPayload: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': tseslint,
      'unused-imports': unusedImports,
    },
    rules: {
      // Detect unused imports and variables
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
      // Allow console and process in Node.js environment
      'no-console': 'off',
      'no-process-env': 'off',
      // Allow empty catch blocks for error handling patterns
      'no-empty': ['error', { allowEmptyCatch: true }],
      // Allow case declarations
      'no-case-declarations': 'off',
      // Allow control characters in regex for security patterns
      'no-control-regex': 'off',
      // Allow useless escape for regex patterns
      'no-useless-escape': 'off',
      // Allow unreachable code for development patterns
      'no-unreachable': 'warn',
      // Allow useless catch for logging patterns
      'no-useless-catch': 'off',
      // Allow undefined types for Express and custom interfaces
      'no-undef': ['error', { typeof: false }],
    },
  },
  {
    files: ['**/*.js'],
    languageOptions: {
      sourceType: 'commonjs',
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },
  },
  {
    ignores: ['eslint.config.js', 'test/**/*', 'dist/**/*', 'node_modules/**/*'],
  },
];
