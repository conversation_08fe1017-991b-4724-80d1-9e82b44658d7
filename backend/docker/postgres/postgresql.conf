# 🐘 Optimized PostgreSQL Configuration for Docker

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 100
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB                    # 25% of container memory
effective_cache_size = 1GB                # 75% of container memory
work_mem = 64MB                          # Per connection working memory
maintenance_work_mem = 256MB              # For maintenance operations
dynamic_shared_memory_type = posix

# Performance Settings
random_page_cost = 1.1                    # SSD optimization
effective_io_concurrency = 200            # SSD optimization
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Write Ahead Logging
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_timeout = 5min

# Query Planner
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Autovacuum
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000         # Log slow queries (1 second)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_statement = 'none'                    # Don't log all statements
log_temp_files = 10MB                     # Log temp files > 10MB

# Extensions and Modules
shared_preload_libraries = 'pg_stat_statements'

# pg_stat_statements Configuration
pg_stat_statements.track = all
pg_stat_statements.max = 10000
pg_stat_statements.track_utility = off

# Locale and Formatting
datestyle = 'iso, mdy'
timezone = 'Asia/Makassar'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Lock Management
deadlock_timeout = 1s
max_locks_per_transaction = 64

# Client Connection Defaults
statement_timeout = 30000                 # 30 seconds
lock_timeout = 10000                      # 10 seconds
idle_in_transaction_session_timeout = 300000  # 5 minutes

# Error Reporting and Logging
log_error_verbosity = default
log_hostname = off
log_connections = on
log_disconnections = on
