-- 🔧 PostgreSQL Extensions Initialization Script

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable unaccent for better text search
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Enable trigram matching for fuzzy search
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enable query statistics tracking
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create application user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'aistech') THEN
        CREATE ROLE aistech WITH LOGIN PASSWORD 'changeme';
    END IF;
END
$$;

-- Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE dbcsrit TO aistech;
GRANT ALL ON SCHEMA public TO aistech;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO aistech;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO aistech;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO aistech;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO aistech;

-- Log initialization completion
SELECT 'PostgreSQL extensions and user setup completed' AS status;
