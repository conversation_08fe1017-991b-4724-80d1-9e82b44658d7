#!/bin/bash

# 🔄 Migration Entrypoint Script for Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Wait for database to be ready
wait_for_db() {
    log "🔍 Waiting for database to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
            success "✅ Database is ready"
            return 0
        fi
        
        log "Attempt $attempt/$max_attempts - Database not ready, waiting..."
        sleep 2
        ((attempt++))
    done
    
    error "Database failed to become ready after $max_attempts attempts"
}

# Run initial schema creation
run_initial_migration() {
    log "🚀 Running initial schema migration..."
    
    if npm run migration:run; then
        success "✅ Initial migration completed"
    else
        error "Initial migration failed"
    fi
}

# Run optimization migrations
run_optimization_migration() {
    log "⚡ Running database optimizations..."
    
    if ./scripts/production-migration.sh; then
        success "✅ Database optimization completed"
    else
        error "Database optimization failed"
    fi
}

# Run performance benchmark
run_performance_test() {
    log "📊 Running performance benchmark..."
    
    if ./scripts/performance-benchmark.sh full; then
        success "✅ Performance benchmark completed"
    else
        log "⚠️ Performance benchmark failed (non-critical)"
    fi
}

# Seed initial data (if requested)
run_seeding() {
    if [ "$RUN_SEEDING" = "true" ]; then
        log "🌱 Running database seeding..."
        
        if npm run seed; then
            success "✅ Database seeding completed"
        else
            log "⚠️ Database seeding failed (non-critical)"
        fi
    fi
}

# Main execution
main() {
    log "🚀 Starting database migration process..."
    
    # Wait for database
    wait_for_db
    
    # Check if this is a fresh installation
    local table_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
    " | tr -d ' ')
    
    if [ "$table_count" -eq 0 ]; then
        log "📋 Fresh database detected - running initial migration"
        run_initial_migration
        run_seeding
    else
        log "📊 Existing database detected - running optimizations only"
    fi
    
    # Always run optimizations
    run_optimization_migration
    
    # Run performance test
    run_performance_test
    
    success "🎉 Migration process completed successfully!"
}

# Handle different commands
case "${1:-migrate}" in
    "migrate")
        main
        ;;
    "optimize")
        wait_for_db
        run_optimization_migration
        ;;
    "benchmark")
        wait_for_db
        run_performance_test
        ;;
    "seed")
        wait_for_db
        run_seeding
        ;;
    *)
        echo "Usage: $0 {migrate|optimize|benchmark|seed}"
        echo ""
        echo "Commands:"
        echo "  migrate   - Run full migration process (default)"
        echo "  optimize  - Run optimizations only"
        echo "  benchmark - Run performance benchmark only"
        echo "  seed      - Run database seeding only"
        exit 1
        ;;
esac
