# 🔄 Migration Dockerfile for Database Optimizations

FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    postgresql-client \
    bash \
    curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create logs directory
RUN mkdir -p logs

# Make scripts executable
RUN chmod +x scripts/*.sh

# Migration entrypoint script
COPY docker/migration/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set entrypoint
ENTRYPOINT ["/entrypoint.sh"]
