import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

export interface OfficeHoursConfig {
  timezone: string;
  startHour: number;
  endHour: number;
  workingDays: number[]; // 0=Sunday, 1=Monday, etc.
}

export interface HolidayCheck {
  isHoliday: boolean;
  holidayName?: string;
  reason?: string;
}

@Injectable()
export class OfficeHoursService {
  private readonly logger = new Logger(OfficeHoursService.name);
  private readonly config: OfficeHoursConfig;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    let parsedWorkingDays: number[] = [];
    try {
      const daysString = this.configService.get<string>('OFFICE_DAYS', '0,1');
      parsedWorkingDays = daysString
        .split(',')
        .map((day) => parseInt(day.trim()))
        .filter((day) => !isNaN(day) && day >= 0 && day <= 6);
      if (parsedWorkingDays.length === 0 && daysString.length > 0) {
        this.logger.warn(
          `OFFICE_DAYS environment variable "${daysString}" parsed to an empty list. Defaulting to no working days.`,
        );
      } else if (parsedWorkingDays.length === 0) {
        this.logger.warn(
          `OFFICE_DAYS environment variable is empty or invalid. Defaulting to no working days.`,
        );
      }
    } catch (e) {
      this.logger.error(
        `Error parsing OFFICE_DAYS. Defaulting to no working days. Error: ${e.message}`,
      );
      parsedWorkingDays = [];
    }

    this.config = {
      timezone: this.configService.get<string>(
        'OFFICE_TIMEZONE',
        'Asia/Makassar',
      ),
      startHour: parseInt(
        this.configService.get<string>('OFFICE_START_HOUR', '8'),
        10,
      ),
      endHour: parseInt(
        this.configService.get<string>('OFFICE_END_HOUR', '17'),
        10,
      ),
      workingDays: parsedWorkingDays,
    };
    this.logger.log(
      `OfficeHoursService initialized with config: ${JSON.stringify(this.config)}`,
    );
  }

  /**
   * Check if current time is within office hours.
   * Note: date.toLocaleString('en-US', ...) can be sensitive to the server's system locale if 'en-US' is not available.
   * For robust multi-server deployments, consider using a dedicated date/timezone library like 'date-fns-tz' or 'luxon' if issues arise.
   */
  isWithinOfficeHours(date: Date = new Date()): boolean {
    try {
      const officeTime = new Date(
        date.toLocaleString('en-US', { timeZone: this.config.timezone }),
      );
      const currentDay = officeTime.getDay(); // Sunday = 0, Monday = 1, ...
      const currentHour = officeTime.getHours();

      const isWorkingDay = this.config.workingDays.includes(currentDay);
      const isWorkingHour =
        currentHour >= this.config.startHour &&
        currentHour < this.config.endHour;

      this.logger.debug(
        `Office hours check for date ${date.toISOString()} (TZ: ${this.config.timezone}): Day=${currentDay}, Hour=${currentHour}. IsWorkingDay=${isWorkingDay}, IsWorkingHour=${isWorkingHour}. Config: Start=${this.config.startHour}, End=${this.config.endHour}, Days=${this.config.workingDays.join(',')}`,
      );
      return isWorkingDay && isWorkingHour;
    } catch (error) {
      this.logger.error(
        `Error checking office hours for date ${date.toISOString()}: ${error.message}`,
        error.stack,
      );
      return false; // Fail safe: assume outside office hours on error
    }
  }

  /**
   * Check if today is a holiday.
   * Attempts to fetch from HOLIDAY_API_URL first, then falls back to a local hardcoded list.
   */
  async isHoliday(date: Date = new Date()): Promise<HolidayCheck> {
    const holidayApiUrl = this.configService.get<string>('HOLIDAY_API_URL');
    const holidayApiKey = this.configService.get<string>('HOLIDAY_API_KEY');

    if (holidayApiUrl) {
      try {
        const headers: Record<string, string> = {};
        if (holidayApiKey) {
          headers['X-Api-Key'] = holidayApiKey;
        }

        const response = await firstValueFrom(
          this.httpService.get(holidayApiUrl, { headers }),
        );
        const holidaysFromApi: Array<{ date: string; name: string }> =
          response.data;
        const dateString = date.toISOString().split('T')[0];
        const apiHoliday = holidaysFromApi.find((h) => h.date === dateString);
        if (apiHoliday) {
          this.logger.log(
            `Holiday detected from API: ${apiHoliday.name} on ${dateString}`,
          );
          return {
            isHoliday: true,
            holidayName: apiHoliday.name,
            reason: 'API',
          };
        }
      } catch (apiError) {
        this.logger.warn(
          `Holiday API call to ${holidayApiUrl} failed, falling back to local list. Error: ${apiError.message}`,
        );
      }
    } else {
      // Use Indonesian holiday API as default
      try {
        const year = date.getFullYear();
        const indonesianHolidayUrl = `https://libur.deno.dev/api?year=${year}`;

        const response = await firstValueFrom(
          this.httpService.get(indonesianHolidayUrl),
        );
        const holidaysFromApi: Array<{ date: string; name: string }> =
          response.data;
        const dateString = date.toISOString().split('T')[0];
        const apiHoliday = holidaysFromApi.find((h) => h.date === dateString);
        if (apiHoliday) {
          this.logger.log(
            `Holiday detected from Indonesian API: ${apiHoliday.name} on ${dateString}`,
          );
          return {
            isHoliday: true,
            holidayName: apiHoliday.name,
            reason: 'Indonesian Holiday API',
          };
        }
      } catch (apiError) {
        this.logger.warn(
          `Indonesian Holiday API call failed, falling back to local list. Error: ${apiError.message}`,
        );
      }
    }

    // Fallback to local list
    const localHolidays = await this.getHardcodedHolidayList(date);
    const dateString = date.toISOString().split('T')[0];
    const localHoliday = localHolidays.find((h) => h.date === dateString);

    if (localHoliday) {
      this.logger.log(
        `Holiday detected from local list: ${localHoliday.name} on ${dateString}`,
      );
      return {
        isHoliday: true,
        holidayName: localHoliday.name,
        reason: 'Local List',
      };
    }

    return { isHoliday: false };
  }

  /**
   * Get list of hardcoded holidays for the year.
   * CRITICAL: This hardcoded list is for DEMONSTRATION & FALLBACK ONLY.
   * In a production environment, this method MUST be updated to fetch data from the HOLIDAY_API_URL.
   * If HOLIDAY_API_URL is configured, attempt to fetch from it first. If it fails or is not configured, then use this.
   * Expected holiday API response for a given year/date range: [{ "date": "YYYY-MM-DD", "name": "Holiday Name" }, ...]
   */
  private async getHardcodedHolidayList(
    date: Date,
  ): Promise<Array<{ date: string; name: string }>> {
    const year = date.getFullYear();
    // Example holidays for Indonesia (Bank Sulsel context)
    return [
      { date: `${year}-01-01`, name: "New Year's Day" },
      { date: `${year}-08-17`, name: 'Independence Day' },
      { date: `${year}-12-25`, name: 'Christmas Day' },
      // Add other significant Indonesian holidays
      { date: `${year}-05-01`, name: 'Labor Day' },
      { date: `${year}-06-01`, name: 'Pancasila Day' },
    ];
  }

  /**
   * Determine if chat should be handled by operator or bot based on office hours and holiday status.
   */
  async shouldRouteToOperator(date: Date = new Date()): Promise<{
    routeToOperator: boolean;
    reason: string;
    details?: HolidayCheck;
  }> {
    const isOfficeHours = this.isWithinOfficeHours(date);
    const holidayCheck = await this.isHoliday(date);

    if (holidayCheck.isHoliday) {
      const reason = `Holiday: ${holidayCheck.holidayName} (Source: ${holidayCheck.reason})`;
      this.logger.log(`Routing decision: Not to operator. Reason: ${reason}`);
      return { routeToOperator: false, reason, details: holidayCheck };
    }

    if (!isOfficeHours) {
      const reason = `Outside office hours (Current time in ${this.config.timezone}: ${new Date(date.toLocaleString('en-US', { timeZone: this.config.timezone })).toLocaleTimeString()})`;
      this.logger.log(`Routing decision: Not to operator. Reason: ${reason}`);
      return { routeToOperator: false, reason };
    }

    const reason = 'Within office hours and no holiday detected.';
    this.logger.log(`Routing decision: To operator. Reason: ${reason}`);
    return { routeToOperator: true, reason };
  }

  /**
   * Get next available office hours start time from the given date.
   */
  getNextOfficeHours(currentDate: Date = new Date()): Date | null {
    if (this.config.workingDays.length === 0) {
      this.logger.warn(
        'Cannot determine next office hours as no working days are configured.',
      );
      return null;
    }

    const nextOfficeDay = new Date(
      currentDate.toLocaleString('en-US', { timeZone: this.config.timezone }),
    );
    nextOfficeDay.setHours(this.config.startHour, 0, 0, 0); // Set to start of office hours for today

    for (let i = 0; i < 7; i++) {
      // Check next 7 days
      if (
        nextOfficeDay.getTime() >
          new Date(
            currentDate.toLocaleString('en-US', {
              timeZone: this.config.timezone,
            }),
          ).getTime() &&
        this.config.workingDays.includes(nextOfficeDay.getDay())
      ) {
        // Found a future working day's start time
        // Further check if this day is a holiday (optional, could be complex)
        return new Date(
          nextOfficeDay.toLocaleString('en-US', { timeZone: 'UTC' }),
        ); // Return in UTC or consistent TZ
      }
      nextOfficeDay.setDate(nextOfficeDay.getDate() + 1);
      nextOfficeDay.setHours(this.config.startHour, 0, 0, 0);
    }
    this.logger.warn(
      'Could not find next available office hours within the next 7 days.',
    );
    return null; // Should ideally not happen if working days are configured
  }
}
