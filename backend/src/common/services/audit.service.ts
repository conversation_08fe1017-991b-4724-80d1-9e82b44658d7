import { Injectable, Logger } from '@nestjs/common';
import { FileAccessLogDto } from '../dto/file-access-log.dto';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  /**
   * Log file access attempts for audit purposes
   * Currently logs to console, can be extended to persist to database
   */
  async logFileAccess(logData: FileAccessLogDto): Promise<void> {
    try {
      // Format log entry for better readability
      const logEntry = {
        timestamp: logData.timestamp.toISOString(),
        userId: logData.userId,
        fileId: logData.fileId,
        action: logData.action,
        ipAddress: logData.ipAddress,
        success: logData.success,
        details: logData.details || 'N/A',
      };

      // Log to console with structured format
      this.logger.log(
        `FILE_ACCESS: ${JSON.stringify(logEntry)}`,
        'FileAccessAudit',
      );

      // TODO: In future iterations, this can be extended to:
      // - Store in database table for persistent audit trail
      // - Send to external logging service (e.g., ELK stack)
      // - Trigger alerts for suspicious access patterns
      // - Generate audit reports
    } catch (error) {
      this.logger.error(
        `Failed to log file access: ${error.message}`,
        error.stack,
        'FileAccessAudit',
      );
      // Don't throw error to avoid breaking file access flow
    }
  }

  /**
   * Log successful file access
   */
  async logSuccessfulAccess(
    userId: string,
    fileId: string,
    action: string,
    ipAddress: string,
    details?: string,
  ): Promise<void> {
    await this.logFileAccess({
      userId,
      fileId,
      action,
      timestamp: new Date(),
      ipAddress,
      success: true,
      details,
    });
  }

  /**
   * Log failed file access attempt
   */
  async logFailedAccess(
    userId: string,
    fileId: string,
    action: string,
    ipAddress: string,
    details?: string,
  ): Promise<void> {
    await this.logFileAccess({
      userId,
      fileId,
      action,
      timestamp: new Date(),
      ipAddress,
      success: false,
      details,
    });
  }
}
