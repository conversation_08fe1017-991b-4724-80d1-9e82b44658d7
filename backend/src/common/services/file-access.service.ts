import {
  Injectable,
  ForbiddenException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { existsSync, createReadStream } from 'fs';
import { join } from 'path';
import { Response } from 'express';

export interface FileAccessCheck {
  fileId: string;
  userId: string;
  filePath: string;
  moduleType: string;
}

export interface FileDownloadOptions {
  contentType?: string;
  filename?: string;
  inline?: boolean;
}

@Injectable()
export class FileAccessService {
  private readonly logger = new Logger(FileAccessService.name);

  /**
   * Check if user has access to a specific file
   * This is a placeholder that should be implemented per module
   */
  async checkFileAccess(check: FileAccessCheck): Promise<boolean> {
    this.logger.log(
      `Checking file access for user ${check.userId} to file ${check.fileId} in module ${check.moduleType}`,
    );

    // For now, return true to allow development to proceed
    // This will be replaced with proper permission logic in a later subtask
    return true;
  }

  /**
   * Securely serve a file with proper access control and error handling
   */
  async serveFile(
    filePath: string,
    response: Response,
    options: FileDownloadOptions = {},
  ): Promise<void> {
    try {
      // Validate file path to prevent directory traversal
      const normalizedPath = this.normalizePath(filePath);

      // Check if file exists
      if (!existsSync(normalizedPath)) {
        this.logger.warn(`File not found: ${normalizedPath}`);
        throw new NotFoundException('File not found');
      }

      // Set default options
      const {
        contentType = 'application/octet-stream',
        filename = 'download',
        inline = false,
      } = options;

      // Create file stream
      const fileStream = createReadStream(normalizedPath);

      // Set response headers
      response.set({
        'Content-Type': contentType,
        'Content-Disposition': `${inline ? 'inline' : 'attachment'}; filename="${filename}"`,
        'Cache-Control': 'private, no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      // Handle stream errors
      fileStream.on('error', (error) => {
        this.logger.error(`Error streaming file ${normalizedPath}:`, error);
        if (!response.headersSent) {
          response.status(500).json({
            success: false,
            message: 'Error streaming file',
          });
        }
      });

      // Pipe file to response
      fileStream.pipe(response);

      this.logger.log(`Successfully served file: ${normalizedPath}`);
    } catch (error) {
      this.logger.error(`Failed to serve file ${filePath}:`, error);

      if (!response.headersSent) {
        if (error instanceof NotFoundException) {
          response.status(404).json({
            success: false,
            message: 'File not found',
          });
        } else if (error instanceof ForbiddenException) {
          response.status(403).json({
            success: false,
            message: 'Access denied',
          });
        } else {
          response.status(500).json({
            success: false,
            message: 'Internal server error',
          });
        }
      }
    }
  }

  /**
   * Normalize and validate file path to prevent directory traversal attacks
   */
  private normalizePath(filePath: string): string {
    const normalizedPath = join(process.cwd(), filePath);

    // Ensure the path is within the project directory
    if (!normalizedPath.startsWith(process.cwd())) {
      throw new ForbiddenException('Invalid file path');
    }

    return normalizedPath;
  }

  /**
   * Get appropriate content type based on file extension
   */
  getContentType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();

    const mimeTypes: Record<string, string> = {
      pdf: 'application/pdf',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      txt: 'text/plain',
      csv: 'text/csv',
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * Sanitize filename for safe download
   */
  sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_|_$/g, '');
  }
}
