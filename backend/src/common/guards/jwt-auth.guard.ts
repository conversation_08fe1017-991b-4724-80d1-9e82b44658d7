import {
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { EncryptionService } from '../decorators/encrypt_decrypt';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    private encryptionService: EncryptionService,
  ) {
    super();
  }

  canActivate(context: ExecutionContext): any {
    const isPublic = this.reflector.getAllAndOverride('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) return true;

    const request = context.switchToHttp().getRequest();

    try {
      const role = this.reflector.getAllAndOverride('RoleAccess', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (role !== undefined) {
        // Secure JWT token extraction
        const token = this.extractToken(request);
        if (!token) {
          throw new UnauthorizedException('No authentication token provided');
        }

        // Secure JWT verification with proper options
        const jwtCompile = jwt.verify(token, process.env.JWT_ACCESS_TOKEN_SECRET, {
          algorithms: ['HS256'], // Explicitly specify allowed algorithms
          issuer: 'wbs-kg-backend',
          audience: 'wbs-kg-frontend',
          maxAge: '15m', // Maximum token age
        }) as any;

        // Validate token structure
        if (!jwtCompile.role || !jwtCompile.id || !jwtCompile.username) {
          throw new UnauthorizedException('Invalid token structure');
        }

        const decryptedRole = this.encryptionService.decrypt(jwtCompile.role);

        // Case-sensitive role comparison for better security
        if (role !== decryptedRole) {
          throw new ForbiddenException('Insufficient privileges for this resource');
        }

        // Add user info to request for downstream use
        request.user = {
          id: this.encryptionService.decrypt(jwtCompile.id),
          username: this.encryptionService.decrypt(jwtCompile.username),
          role: decryptedRole,
        };

        return true;
      }

      return super.canActivate(context);
    } catch (error) {
      // Don't log sensitive information in production
      if (process.env.NODE_ENV !== 'production') {
        console.error('JWT Guard error:', error.message);
      }

      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        throw error;
      }

      throw new UnauthorizedException('Authentication failed');
    }
  }

  private extractToken(request: any): string | null {
    // Try cookie first (for web app)
    if (request.cookies?.Authentication) {
      return request.cookies.Authentication;
    }

    // Try Authorization header (for API calls)
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return null;
  }

  handleRequest(err, user, info) {
    if (err || !user) {
      throw err || new UnauthorizedException();
    }
    return user;
  }
}
