/**
 * CSRF Protection Guard
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import * as crypto from 'crypto';

@Injectable()
export class CsrfGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Check if CSRF protection is disabled for this route
    const skipCsrf = this.reflector.getAllAndOverride<boolean>('skipCsrf', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipCsrf) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Skip CSRF for safe methods (GET, HEAD, OPTIONS)
    if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
      // Generate and set CSRF token for safe methods
      this.generateAndSetCsrfToken(request, response);
      return true;
    }

    // For unsafe methods, validate CSRF token
    return this.validateCsrfToken(request);
  }

  private generateAndSetCsrfToken(request: any, response: any): void {
    // Generate CSRF token if not exists
    if (!request.session?.csrfToken) {
      const token = crypto.randomBytes(32).toString('hex');
      
      // Store in session
      if (!request.session) {
        request.session = {};
      }
      request.session.csrfToken = token;

      // Set as cookie (for client-side access)
      response.cookie('XSRF-TOKEN', token, {
        httpOnly: false, // Allow client-side access
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      // Set as header (for server-side verification)
      response.setHeader('X-CSRF-Token', token);
    }
  }

  private validateCsrfToken(request: any): boolean {
    const sessionToken = request.session?.csrfToken;
    
    if (!sessionToken) {
      throw new ForbiddenException('CSRF token not found in session');
    }

    // Check multiple possible sources for CSRF token
    const clientToken = 
      request.headers['x-csrf-token'] ||
      request.headers['x-xsrf-token'] ||
      request.body?._csrf ||
      request.query?._csrf;

    if (!clientToken) {
      throw new BadRequestException('CSRF token required');
    }

    // Constant-time comparison to prevent timing attacks
    if (!this.constantTimeEquals(sessionToken, clientToken)) {
      throw new ForbiddenException('Invalid CSRF token');
    }

    return true;
  }

  private constantTimeEquals(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }
}

/**
 * Decorator to skip CSRF protection for specific routes
 */
export const SkipCsrf = () => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata('skipCsrf', true, descriptor.value);
    } else {
      Reflect.defineMetadata('skipCsrf', true, target);
    }
  };
};

/**
 * CSRF Token Service for manual token generation/validation
 */
@Injectable()
export class CsrfService {
  generateToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  validateToken(sessionToken: string, clientToken: string): boolean {
    if (!sessionToken || !clientToken) {
      return false;
    }

    if (sessionToken.length !== clientToken.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < sessionToken.length; i++) {
      result |= sessionToken.charCodeAt(i) ^ clientToken.charCodeAt(i);
    }

    return result === 0;
  }

  setTokenInResponse(response: any, token: string): void {
    response.cookie('XSRF-TOKEN', token, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000,
    });

    response.setHeader('X-CSRF-Token', token);
  }
}
