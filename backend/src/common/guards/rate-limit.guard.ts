/**
 * Advanced Rate Limiting Guard
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/Denial_of_Service_Cheat_Sheet.html
 */

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  blockDurationMs?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitData {
  count: number;
  resetTime: number;
  blocked?: boolean;
  blockUntil?: number;
}

@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly store = new Map<string, RateLimitData>();
  private readonly defaultConfig: RateLimitConfig = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    blockDurationMs: 60 * 60 * 1000, // 1 hour block after exceeding limit
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  };

  constructor(private reflector: Reflector) {
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    // Get rate limit configuration from decorator or use default
    const config = this.reflector.getAllAndOverride<RateLimitConfig>('rateLimit', [
      context.getHandler(),
      context.getClass(),
    ]) || this.defaultConfig;

    // Skip rate limiting if disabled
    const skipRateLimit = this.reflector.getAllAndOverride<boolean>('skipRateLimit', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipRateLimit) {
      return true;
    }

    const key = this.generateKey(request);
    const now = Date.now();

    // Get or create rate limit data
    let rateLimitData = this.store.get(key);
    if (!rateLimitData || now > rateLimitData.resetTime) {
      rateLimitData = {
        count: 0,
        resetTime: now + config.windowMs,
      };
    }

    // Check if currently blocked
    if (rateLimitData.blocked && rateLimitData.blockUntil && now < rateLimitData.blockUntil) {
      this.setRateLimitHeaders(response, config, rateLimitData, true);
      throw new HttpException(
        {
          error: 'Rate limit exceeded',
          message: 'Too many requests. You are temporarily blocked.',
          retryAfter: Math.ceil((rateLimitData.blockUntil - now) / 1000),
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Remove block if expired
    if (rateLimitData.blocked && rateLimitData.blockUntil && now >= rateLimitData.blockUntil) {
      rateLimitData.blocked = false;
      rateLimitData.blockUntil = undefined;
      rateLimitData.count = 0;
      rateLimitData.resetTime = now + config.windowMs;
    }

    // Increment request count
    rateLimitData.count++;

    // Check if limit exceeded
    if (rateLimitData.count > config.maxRequests) {
      // Block the IP/user
      if (config.blockDurationMs) {
        rateLimitData.blocked = true;
        rateLimitData.blockUntil = now + config.blockDurationMs;
      }

      this.store.set(key, rateLimitData);
      this.setRateLimitHeaders(response, config, rateLimitData, true);

      throw new HttpException(
        {
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil(config.windowMs / 1000),
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // Update store
    this.store.set(key, rateLimitData);

    // Set rate limit headers
    this.setRateLimitHeaders(response, config, rateLimitData, false);

    return true;
  }

  private generateKey(request: Request): string {
    // Use IP address as primary key
    const ip = this.getClientIp(request);
    
    // For authenticated users, also consider user ID
    const userId = (request as any).user?.id;
    
    return userId ? `user:${userId}` : `ip:${ip}`;
  }

  private getClientIp(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  private setRateLimitHeaders(
    response: Response,
    config: RateLimitConfig,
    data: RateLimitData,
    exceeded: boolean,
  ): void {
    const remaining = Math.max(0, config.maxRequests - data.count);
    const resetTime = Math.ceil(data.resetTime / 1000);

    response.setHeader('X-RateLimit-Limit', config.maxRequests);
    response.setHeader('X-RateLimit-Remaining', remaining);
    response.setHeader('X-RateLimit-Reset', resetTime);

    if (exceeded) {
      const retryAfter = Math.ceil((data.resetTime - Date.now()) / 1000);
      response.setHeader('Retry-After', retryAfter);
    }

    if (data.blocked && data.blockUntil) {
      const blockRetryAfter = Math.ceil((data.blockUntil - Date.now()) / 1000);
      response.setHeader('X-RateLimit-Block-Retry-After', blockRetryAfter);
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, data] of this.store.entries()) {
      // Remove expired entries
      if (now > data.resetTime && (!data.blocked || (data.blockUntil && now > data.blockUntil))) {
        this.store.delete(key);
      }
    }
  }
}

/**
 * Decorator to configure rate limiting for specific routes
 */
export const RateLimit = (config: Partial<RateLimitConfig>) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata('rateLimit', config, descriptor.value);
    } else {
      Reflect.defineMetadata('rateLimit', config, target);
    }
  };
};

/**
 * Decorator to skip rate limiting for specific routes
 */
export const SkipRateLimit = () => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata('skipRateLimit', true, descriptor.value);
    } else {
      Reflect.defineMetadata('skipRateLimit', true, target);
    }
  };
};

/**
 * Predefined rate limit configurations
 */
export const RateLimitConfigs = {
  // Strict rate limiting for authentication endpoints
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
    blockDurationMs: 60 * 60 * 1000, // 1 hour block
  },
  
  // Moderate rate limiting for API endpoints
  API: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
    blockDurationMs: 30 * 60 * 1000, // 30 minutes block
  },
  
  // Lenient rate limiting for public endpoints
  PUBLIC: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
    blockDurationMs: 10 * 60 * 1000, // 10 minutes block
  },
  
  // Very strict for password reset
  PASSWORD_RESET: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 attempts per hour
    blockDurationMs: 24 * 60 * 60 * 1000, // 24 hours block
  },
};
