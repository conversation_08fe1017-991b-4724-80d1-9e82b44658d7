import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

@Injectable()
export class JwtConfigService {
  private readonly accessTokenSecret: string;
  private readonly refreshTokenSecret: string;
  private readonly accessTokenExpiration: string;
  private readonly refreshTokenExpiration: string;

  constructor(private configService: ConfigService) {
    // Validate and set JWT secrets with fallback generation
    this.accessTokenSecret = this.validateAndGetSecret('JWT_ACCESS_TOKEN_SECRET');
    this.refreshTokenSecret = this.validateAndGetSecret('JWT_REFRESH_TOKEN_SECRET');
    
    // Validate expiration times
    this.accessTokenExpiration = this.validateExpiration('JWT_ACCESS_TOKEN_EXPIRATION_TIME', '900'); // 15 minutes default
    this.refreshTokenExpiration = this.validateExpiration('JWT_REFRESH_TOKEN_EXPIRATION_TIME', '604800'); // 7 days default
  }

  private validateAndGetSecret(envKey: string): string {
    const secret = this.configService.get<string>(envKey);
    
    if (!secret) {
      throw new Error(`${envKey} is required but not provided`);
    }

    // Validate secret strength (minimum 32 characters, contains special chars)
    if (secret.length < 32) {
      throw new Error(`${envKey} must be at least 32 characters long`);
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(secret)) {
      throw new Error(`${envKey} must contain at least one special character`);
    }

    return secret;
  }

  private validateExpiration(envKey: string, defaultValue: string): string {
    const expiration = this.configService.get<string>(envKey, defaultValue);
    
    // Validate that expiration is a valid number
    if (isNaN(Number(expiration))) {
      throw new Error(`${envKey} must be a valid number (seconds)`);
    }

    return expiration;
  }

  getAccessTokenConfig() {
    return {
      secret: this.accessTokenSecret,
      signOptions: {
        expiresIn: `${this.accessTokenExpiration}s`,
        algorithm: 'HS256' as const,
        issuer: 'wbs-kg-backend',
        audience: 'wbs-kg-frontend',
      },
    };
  }

  getRefreshTokenConfig() {
    return {
      secret: this.refreshTokenSecret,
      signOptions: {
        expiresIn: `${this.refreshTokenExpiration}s`,
        algorithm: 'HS256' as const,
        issuer: 'wbs-kg-backend',
        audience: 'wbs-kg-frontend',
      },
    };
  }

  // Generate secure random secret for development/testing
  static generateSecureSecret(): string {
    return crypto.randomBytes(64).toString('hex');
  }
}
