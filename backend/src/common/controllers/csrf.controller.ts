import { <PERSON>, Get, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { Public } from '../decorators/public.decorator';

interface CsrfRequest extends Request {
  csrfToken?: () => string;
}

@Controller('csrf')
export class CsrfController {
  @Public()
  @Get('token')
  getCsrfToken(@Req() req: CsrfRequest, @Res() res: Response) {
    const token = req.csrfToken ? req.csrfToken() : null;

    if (!token) {
      return res.status(500).json({
        success: false,
        message: 'CSRF token generation failed',
      });
    }

    return res.json({
      success: true,
      csrfToken: token,
      message: 'CSRF token generated successfully',
    });
  }
}
