/**
 * Input Sanitization Utilities
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html
 */

import { Transform } from 'class-transformer';
import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export function SanitizeHtml() {
  return Transform(({ value }) => {
    if (typeof value !== 'string') return value;

    // Remove all HTML tags using regex (basic sanitization)
    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#x27;/g, "'");
  });
}

/**
 * Sanitizes and trims string input
 */
export function SanitizeString() {
  return Transform(({ value }) => {
    if (typeof value !== 'string') return value;
    
    // Trim whitespace and remove null bytes
    return value.trim().replace(/\0/g, '');
  });
}

/**
 * Validates that string doesn't contain SQL injection patterns
 */
export function NoSqlInjection(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'noSqlInjection',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return true;
          
          // Check for common SQL injection patterns
          const sqlPatterns = [
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
            /(--|\/\*|\*\/|;|'|"|`)/,
            /(\bOR\b|\bAND\b).*[=<>]/i,
            /\b(WAITFOR|DELAY)\b/i,
            /\b(XP_|SP_)/i
          ];
          
          return !sqlPatterns.some(pattern => pattern.test(value));
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} contains potentially dangerous SQL patterns`;
        },
      },
    });
  };
}

/**
 * Validates that string doesn't contain XSS patterns
 */
export function NoXss(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'noXss',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return true;
          
          // Check for common XSS patterns
          const xssPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
            /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
            /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
            /expression\s*\(/gi,
            /vbscript:/gi,
            /data:text\/html/gi
          ];
          
          return !xssPatterns.some(pattern => pattern.test(value));
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} contains potentially dangerous XSS patterns`;
        },
      },
    });
  };
}

/**
 * Validates file upload security
 */
export function ValidateFileUpload(allowedTypes: string[], maxSize: number = 10 * 1024 * 1024) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'validateFileUpload',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [allowedTypes, maxSize],
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true; // Optional file
          
          const [allowedTypes, maxSize] = args.constraints;
          
          // Check file size
          if (value.size > maxSize) {
            return false;
          }
          
          // Check file type
          const fileExtension = value.originalname?.split('.').pop()?.toLowerCase();
          if (!allowedTypes.includes(fileExtension)) {
            return false;
          }
          
          // Check MIME type
          const allowedMimeTypes = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          };
          
          const expectedMimeType = allowedMimeTypes[fileExtension];
          if (expectedMimeType && value.mimetype !== expectedMimeType) {
            return false;
          }
          
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          const [allowedTypes, maxSize] = args.constraints;
          return `File must be one of: ${allowedTypes.join(', ')} and smaller than ${maxSize / (1024 * 1024)}MB`;
        },
      },
    });
  };
}

/**
 * Validates that string is safe for filename usage
 */
export function SafeFilename(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'safeFilename',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return true;
          
          // Check for dangerous filename patterns
          const dangerousPatterns = [
            /\.\./,  // Directory traversal
            /[<>:"|?*]/,  // Invalid filename characters
            /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Reserved Windows names
            /^\./,  // Hidden files
            /\.$/, // Ending with dot
          ];
          
          return !dangerousPatterns.some(pattern => pattern.test(value));
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} contains unsafe filename characters`;
        },
      },
    });
  };
}

/**
 * Utility class for input sanitization
 */
export class InputSanitizer {
  /**
   * Sanitize string for database storage
   */
  static sanitizeForDatabase(input: string): string {
    if (typeof input !== 'string') return input;
    
    return input
      .trim()
      .replace(/\0/g, '') // Remove null bytes
      .replace(/[\x00-\x1F\x7F]/g, ''); // Remove control characters
  }

  /**
   * Sanitize HTML content
   */
  static sanitizeHtml(input: string): string {
    if (typeof input !== 'string') return input;

    // Basic HTML sanitization - remove dangerous tags
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/vbscript:/gi, '');
  }

  /**
   * Generate safe filename
   */
  static generateSafeFilename(originalName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop()?.toLowerCase() || '';
    
    return `${timestamp}_${randomString}.${extension}`;
  }
}
