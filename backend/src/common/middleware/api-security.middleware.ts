/**
 * Comprehensive API Security Middleware
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/REST_Security_Cheat_Sheet.html
 */

import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { SecureLogger } from '../filters/secure-exception.filter';

interface SecurityConfig {
  enableRequestLogging: boolean;
  enableResponseLogging: boolean;
  maxRequestSize: number;
  allowedMethods: string[];
  sensitiveHeaders: string[];
  rateLimitByEndpoint: boolean;
}

@Injectable()
export class ApiSecurityMiddleware implements NestMiddleware {
  private readonly logger = new Logger(ApiSecurityMiddleware.name);
  
  private readonly config: SecurityConfig = {
    enableRequestLogging: process.env.NODE_ENV !== 'production',
    enableResponseLogging: process.env.NODE_ENV !== 'production',
    maxRequestSize: 10 * 1024 * 1024, // 10MB
    allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    sensitiveHeaders: ['authorization', 'cookie', 'x-api-key', 'x-auth-token'],
    rateLimitByEndpoint: true,
  };

  use(req: Request, res: Response, next: NextFunction) {
    // Generate request ID for tracking
    const requestId = this.generateRequestId();
    (req as any).requestId = requestId;

    // Set security headers
    this.setSecurityHeaders(res);

    // Validate HTTP method
    if (!this.config.allowedMethods.includes(req.method)) {
      return res.status(405).json({
        error: 'Method Not Allowed',
        message: `HTTP method ${req.method} is not allowed`,
      });
    }

    // Validate request size
    const contentLength = parseInt(req.headers['content-length'] || '0', 10);
    if (contentLength > this.config.maxRequestSize) {
      return res.status(413).json({
        error: 'Payload Too Large',
        message: 'Request payload exceeds maximum allowed size',
      });
    }

    // Validate Content-Type for POST/PUT/PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      if (!this.isValidContentType(req)) {
        return res.status(415).json({
          error: 'Unsupported Media Type',
          message: 'Content-Type not supported',
        });
      }
    }

    // Log security-relevant request information
    this.logSecureRequest(req);

    // Add response logging
    if (this.config.enableResponseLogging) {
      this.addResponseLogging(req, res);
    }

    // Detect and log suspicious patterns
    this.detectSuspiciousActivity(req);

    next();
  }

  private generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private setSecurityHeaders(res: Response): void {
    // API-specific security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // API versioning and rate limiting headers
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Rate-Limit-Policy', 'standard');
    
    // Cache control for API responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
  }

  private isValidContentType(req: Request): boolean {
    const contentType = req.headers['content-type'];
    if (!contentType) return false;

    const allowedTypes = [
      'application/json',
      'application/x-www-form-urlencoded',
      'multipart/form-data',
      'text/plain',
    ];

    return allowedTypes.some(type => contentType.includes(type));
  }

  private logSecureRequest(req: Request): void {
    if (!this.config.enableRequestLogging) return;

    const sanitizedHeaders = this.sanitizeHeaders(req.headers);
    const clientIp = this.getClientIp(req);
    
    const logData = {
      requestId: (req as any).requestId,
      method: req.method,
      url: req.url,
      ip: clientIp,
      userAgent: req.headers['user-agent'],
      headers: sanitizedHeaders,
      timestamp: new Date().toISOString(),
    };

    this.logger.log(`API Request: ${req.method} ${req.url}`, logData);
  }

  private addResponseLogging(req: Request, res: Response): void {
    const originalSend = res.send;
    const startTime = Date.now();

    res.send = function(body: any) {
      const duration = Date.now() - startTime;
      
      const logData = {
        requestId: (req as any).requestId,
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration,
        responseSize: Buffer.byteLength(body || ''),
        timestamp: new Date().toISOString(),
      };

      if (res.statusCode >= 400) {
        SecureLogger.logSecurityEvent(
          'API Error Response',
          logData,
          res.statusCode >= 500 ? 'high' : 'medium'
        );
      }

      return originalSend.call(this, body);
    };
  }

  private detectSuspiciousActivity(req: Request): void {
    const suspiciousPatterns = [
      // SQL Injection patterns
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      
      // XSS patterns
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      
      // Path traversal
      /\.\.[\/\\]/,
      
      // Command injection
      /[;&|`$()]/,
      
      // LDAP injection
      /[()&|!]/,
    ];

    const requestData = JSON.stringify({
      url: req.url,
      query: req.query,
      body: req.body,
      headers: req.headers,
    });

    const suspiciousActivity = suspiciousPatterns.some(pattern => 
      pattern.test(requestData)
    );

    if (suspiciousActivity) {
      SecureLogger.logSecurityEvent(
        'Suspicious Request Pattern Detected',
        {
          requestId: (req as any).requestId,
          method: req.method,
          url: req.url,
          ip: this.getClientIp(req),
          userAgent: req.headers['user-agent'],
          patterns: 'Multiple suspicious patterns detected',
        },
        'high'
      );
    }

    // Check for unusual request frequency from same IP
    this.checkRequestFrequency(req);
  }

  private checkRequestFrequency(req: Request): void {
    const ip = this.getClientIp(req);
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window
    const maxRequests = 100; // Max requests per minute

    // Simple in-memory tracking (use Redis in production)
    if (!global.requestTracker) {
      global.requestTracker = new Map();
    }

    const tracker = global.requestTracker;
    const key = `freq_${ip}`;
    const requests = tracker.get(key) || [];

    // Remove old requests outside the window
    const recentRequests = requests.filter((time: number) => now - time < windowMs);
    recentRequests.push(now);

    tracker.set(key, recentRequests);

    if (recentRequests.length > maxRequests) {
      SecureLogger.logSecurityEvent(
        'High Request Frequency Detected',
        {
          ip,
          requestCount: recentRequests.length,
          windowMs,
          url: req.url,
          method: req.method,
        },
        'medium'
      );
    }

    // Clean up old entries periodically
    if (Math.random() < 0.01) { // 1% chance
      this.cleanupRequestTracker(tracker, now, windowMs);
    }
  }

  private cleanupRequestTracker(tracker: Map<string, number[]>, now: number, windowMs: number): void {
    for (const [key, requests] of tracker.entries()) {
      const recentRequests = requests.filter((time: number) => now - time < windowMs);
      if (recentRequests.length === 0) {
        tracker.delete(key);
      } else {
        tracker.set(key, recentRequests);
      }
    }
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    this.config.sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private getClientIp(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    );
  }
}

/**
 * API Endpoint Security Decorator
 */
export function ApiSecurity(options: {
  requireAuth?: boolean;
  roles?: string[];
  rateLimit?: { windowMs: number; maxRequests: number };
  validateInput?: boolean;
  logAccess?: boolean;
}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // Store security metadata for the endpoint
    Reflect.defineMetadata('apiSecurity', options, descriptor.value);
    
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      // Security checks would be implemented here
      // This is a placeholder for the decorator pattern
      
      if (options.logAccess) {
        SecureLogger.logSecurityEvent(
          'API Endpoint Access',
          {
            endpoint: `${target.constructor.name}.${propertyKey}`,
            options,
          },
          'low'
        );
      }
      
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

/**
 * Global API security configuration
 */
export const API_SECURITY_CONFIG = {
  // Request validation
  MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_QUERY_PARAMS: 50,
  MAX_HEADER_SIZE: 8192, // 8KB
  
  // Rate limiting
  DEFAULT_RATE_LIMIT: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
  },
  
  // Authentication
  JWT_EXPIRY: 15 * 60, // 15 minutes
  REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60, // 7 days
  
  // Logging
  LOG_SENSITIVE_DATA: false,
  LOG_REQUEST_BODY: process.env.NODE_ENV === 'development',
  LOG_RESPONSE_BODY: false,
  
  // CORS
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  ALLOWED_METHODS: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  ALLOWED_HEADERS: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
};
