import { Injectable, NestMiddleware, ForbiddenException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { doubleCsrf } from 'csrf-csrf';

interface CsrfRequest extends Request {
  csrfToken?: () => string;
}

@Injectable()
export class CsrfMiddleware implements NestMiddleware {
  private doubleCsrfProtection;
  private csrfUtils;

  constructor() {
    this.csrfUtils = doubleCsrf({
      getSecret: () =>
        process.env.CSRF_SECRET || 'NW6CYezXlpemgOpWIJZ1HGFJy79A36FI',
      getSessionIdentifier: (req) => {
        // Use session ID from cookies, or fallback to a combination of IP and User-Agent
        return req.cookies?.session_id || `${req.ip}-${req.get('User-Agent')}`;
      },
      cookieName: '__Host-csrf-token',
      cookieOptions: {
        httpOnly: true,
        sameSite: 'strict',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 3600000, // 1 hour
        path: '/',
      },
      size: 64,
      ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
      getCsrfTokenFromRequest: (req) => {
        // Check multiple possible header names for flexibility
        return (
          req.headers['x-csrf-token'] ||
          req.headers['xsrf-token'] ||
          req.body?._csrf
        );
      },
    });

    this.doubleCsrfProtection = this.csrfUtils.doubleCsrfProtection;
  }

  use(req: CsrfRequest, res: Response, next: NextFunction) {
    try {
      // Add token generator function to request for easy access
      req.csrfToken = () => this.csrfUtils.generateToken(req, res);

      // For GET requests, generate token and add to response locals
      if (req.method === 'GET') {
        const token = this.csrfUtils.generateToken(req, res);
        res.locals.csrfToken = token;

        // Also set a header for SPA applications
        res.setHeader('X-CSRF-Token', token);
      }

      // Apply CSRF protection
      this.doubleCsrfProtection(req, res, (error) => {
        if (error) {
          throw new ForbiddenException('Invalid CSRF token');
        }
        next();
      });
    } catch (error) {
      if (error.code === 'EBADCSRFTOKEN') {
        throw new ForbiddenException('Invalid CSRF token');
      }
      throw error;
    }
  }
}
