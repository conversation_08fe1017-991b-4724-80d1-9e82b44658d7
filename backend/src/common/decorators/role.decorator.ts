import {
  createParamDecorator,
  ExecutionContext,
  SetMetadata,
} from '@nestjs/common';

export const IsRoleAccess = createParamDecorator(
  (data: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return data
      ? request.user &&
          request.user.role.name.toLowerCase() === data.toLowerCase()
      : request.user;
  },
);

export const IS_PUBLIC_KEY = 'RoleAccess';
export const IsRole = (role: string) => {
  return SetMetadata(IS_PUBLIC_KEY, role);
};
