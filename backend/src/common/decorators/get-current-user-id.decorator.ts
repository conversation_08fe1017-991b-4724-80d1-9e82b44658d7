import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';

export const GetCurrentUserId = createParamDecorator(
  (_: unknown, context: ExecutionContext): string => {
    const request = context.switchToHttp().getRequest();
    const { user } = request;

    return user.id;
  },
);

export const GetCurrentUsername = createParamDecorator(
  (_: unknown, context: ExecutionContext): string => {
    const request = context.switchToHttp().getRequest();
    const jwtCompile = jwt.verify(
      request.cookies.Authentication,
      process.env.JWT_ACCESS_TOKEN_SECRET,
    );
    return jwtCompile['username'];
  },
);

export const GetCurrentUserToken = createParamDecorator(
  (_: unknown, context: ExecutionContext): string => {
    const request = context.switchToHttp().getRequest();

    return request?.cookies?.Refresh;
  },
);

export const GetCurrentUserTokenN = createParamDecorator(
  (_: unknown, context: ExecutionContext): string => {
    const request = context.switchToHttp().getRequest();

    return request?.cookies?.Authentication;
  },
);
