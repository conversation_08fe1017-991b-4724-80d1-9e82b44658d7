import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

@Injectable()
export class EncryptionService {
  private algorithm: string = 'aes-256-cbc';
  private secretKey: Buffer;
  private iv: Buffer;

  constructor() {
    // Constant secret key, should be 32 bytes (256 bits) for aes-256-cbc
    const keyHex =
      process.env.ENCRYPTION_SECRET_KEY || 'MSLPAWQCOI1Q6M8KAKJF2DHHEA4VALTN'; // Replace with your key
    this.secretKey = Buffer.from(keyHex);
    // Constant IV, should be 16 bytes (128 bits) for aes-256-cbc
    const ivHex = process.env.ENCRYPTION_IVHEX || '6JB8C6UM5DV10ZAM'; // Replace with your IV
    this.iv = Buffer.from(ivHex);
  }

  encrypt(text: string): string {
    try {
      const cipher = crypto.createCipheriv(
        this.algorithm,
        this.secretKey,
        this.iv,
      );
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    } catch (error) {
      throw new Error('error');
    }
  }

  decrypt(encrypted: string): string {
    try {
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        this.secretKey,
        this.iv,
      );
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      // throw new Error('error decrypt');
    }
  }
}
