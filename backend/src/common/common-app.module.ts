import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { OfficeHoursService } from './services/office-hours.service';
import { EncryptionService } from './decorators/encrypt_decrypt';

@Global() // Make services available globally without importing CommonAppModule everywhere
@Module({
  imports: [
    ConfigModule,
    HttpModule.register({ timeout: 5000, maxRedirects: 5 }), // For holiday API calls
  ],
  providers: [OfficeHoursService, EncryptionService],
  exports: [OfficeHoursService, EncryptionService],
})
export class CommonAppModule {}
