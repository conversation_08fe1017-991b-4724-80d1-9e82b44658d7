/**
 * Secure Exception Filter
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/Error_Handling_Cheat_Sheet.html
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { DataProtectionUtils } from '../services/encryption.service';

interface ErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  timestamp: string;
  path: string;
  requestId?: string;
}

interface DetailedErrorResponse extends ErrorResponse {
  details?: any;
  stack?: string;
}

@Catch()
export class SecureExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(SecureExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = this.getHttpStatus(exception);
    const errorResponse = this.buildErrorResponse(exception, request, status);

    // Log the error securely
    this.logError(exception, request, status);

    // Send sanitized response to client
    response.status(status).json(errorResponse);
  }

  private getHttpStatus(exception: unknown): number {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }
    
    // Default to 500 for unknown errors
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  private buildErrorResponse(
    exception: unknown,
    request: Request,
    status: number,
  ): ErrorResponse | DetailedErrorResponse {
    const timestamp = new Date().toISOString();
    const path = request.url;
    const requestId = this.generateRequestId();

    // Base error response
    const baseResponse: ErrorResponse = {
      statusCode: status,
      message: this.getSafeErrorMessage(exception, status),
      error: this.getErrorName(status),
      timestamp,
      path,
      requestId,
    };

    // In development, provide more details
    if (process.env.NODE_ENV === 'development') {
      const detailedResponse: DetailedErrorResponse = {
        ...baseResponse,
        details: this.getErrorDetails(exception),
        stack: this.getErrorStack(exception),
      };
      return detailedResponse;
    }

    return baseResponse;
  }

  private getSafeErrorMessage(exception: unknown, status: number): string {
    // For client errors (4xx), we can be more specific
    if (status >= 400 && status < 500) {
      if (exception instanceof HttpException) {
        const response = exception.getResponse();
        if (typeof response === 'string') {
          return this.sanitizeErrorMessage(response);
        }
        if (typeof response === 'object' && response !== null) {
          const message = (response as any).message;
          if (Array.isArray(message)) {
            return message.map(msg => this.sanitizeErrorMessage(msg)).join(', ');
          }
          return this.sanitizeErrorMessage(message || 'Bad Request');
        }
      }
      return this.getGenericErrorMessage(status);
    }

    // For server errors (5xx), use generic messages
    return this.getGenericErrorMessage(status);
  }

  private sanitizeErrorMessage(message: string): string {
    if (!message || typeof message !== 'string') {
      return 'An error occurred';
    }

    // Remove sensitive information patterns
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /secret/gi,
      /key/gi,
      /database/gi,
      /connection/gi,
      /server/gi,
      /file system/gi,
      /path/gi,
      /directory/gi,
      /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/, // IP addresses
      /[a-zA-Z]:\\[^\\]+\\/, // Windows file paths
      /\/[^\/\s]+\/[^\/\s]+/, // Unix file paths
    ];

    let sanitized = message;
    sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });

    return sanitized;
  }

  private getGenericErrorMessage(status: number): string {
    const genericMessages = {
      400: 'Bad Request - The request could not be understood',
      401: 'Unauthorized - Authentication is required',
      403: 'Forbidden - Access to this resource is denied',
      404: 'Not Found - The requested resource was not found',
      405: 'Method Not Allowed - The request method is not supported',
      409: 'Conflict - The request conflicts with the current state',
      422: 'Unprocessable Entity - The request contains invalid data',
      429: 'Too Many Requests - Rate limit exceeded',
      500: 'Internal Server Error - An unexpected error occurred',
      502: 'Bad Gateway - Invalid response from upstream server',
      503: 'Service Unavailable - The service is temporarily unavailable',
      504: 'Gateway Timeout - The upstream server did not respond in time',
    };

    return genericMessages[status as keyof typeof genericMessages] || 'An error occurred';
  }

  private getErrorName(status: number): string {
    const errorNames = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      405: 'Method Not Allowed',
      409: 'Conflict',
      422: 'Unprocessable Entity',
      429: 'Too Many Requests',
      500: 'Internal Server Error',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
      504: 'Gateway Timeout',
    };

    return errorNames[status as keyof typeof errorNames] || 'Error';
  }

  private getErrorDetails(exception: unknown): any {
    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      if (typeof response === 'object') {
        return DataProtectionUtils.sanitizeForLogging(response);
      }
    }
    return null;
  }

  private getErrorStack(exception: unknown): string | undefined {
    if (exception instanceof Error) {
      // Sanitize stack trace to remove sensitive paths
      return exception.stack?.replace(/\/[^\/\s]+\/[^\/\s]+/g, '[PATH_REDACTED]');
    }
    return undefined;
  }

  private logError(exception: unknown, request: Request, status: number): void {
    const requestId = this.generateRequestId();
    const userId = (request as any).user?.id || 'anonymous';
    const userAgent = request.headers['user-agent'] || 'unknown';
    const ip = this.getClientIp(request);

    // Sanitize request data for logging
    const sanitizedRequest = {
      method: request.method,
      url: request.url,
      headers: this.sanitizeHeaders(request.headers),
      body: DataProtectionUtils.sanitizeForLogging(request.body),
      query: DataProtectionUtils.sanitizeForLogging(request.query),
      params: DataProtectionUtils.sanitizeForLogging(request.params),
    };

    const logData = {
      requestId,
      userId,
      ip,
      userAgent,
      status,
      request: sanitizedRequest,
      timestamp: new Date().toISOString(),
    };

    if (status >= 500) {
      // Server errors - log with full details
      this.logger.error(
        `Server Error: ${exception instanceof Error ? exception.message : 'Unknown error'}`,
        {
          ...logData,
          error: exception instanceof Error ? {
            name: exception.name,
            message: exception.message,
            stack: exception.stack,
          } : exception,
        },
      );
    } else if (status >= 400) {
      // Client errors - log with limited details
      this.logger.warn(
        `Client Error: ${this.getSafeErrorMessage(exception, status)}`,
        logData,
      );
    }
  }

  private sanitizeHeaders(headers: any): any {
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'x-api-key',
      'x-auth-token',
      'x-csrf-token',
    ];

    const sanitized = { ...headers };
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      'unknown'
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Security-focused logging service
 */
export class SecureLogger {
  private static readonly logger = new Logger('SecurityLogger');

  static logSecurityEvent(event: string, details: any, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'): void {
    const logData = {
      event,
      severity,
      timestamp: new Date().toISOString(),
      details: DataProtectionUtils.sanitizeForLogging(details),
    };

    switch (severity) {
      case 'critical':
        this.logger.error(`SECURITY CRITICAL: ${event}`, logData);
        break;
      case 'high':
        this.logger.error(`SECURITY HIGH: ${event}`, logData);
        break;
      case 'medium':
        this.logger.warn(`SECURITY MEDIUM: ${event}`, logData);
        break;
      case 'low':
        this.logger.log(`SECURITY LOW: ${event}`, logData);
        break;
    }
  }

  static logAuthenticationAttempt(success: boolean, username: string, ip: string, userAgent: string): void {
    this.logSecurityEvent(
      success ? 'Authentication Success' : 'Authentication Failure',
      {
        success,
        username: DataProtectionUtils.maskSensitiveData(username, 2),
        ip,
        userAgent,
      },
      success ? 'low' : 'medium',
    );
  }

  static logPrivilegeEscalation(userId: string, fromRole: string, toRole: string, ip: string): void {
    this.logSecurityEvent(
      'Privilege Escalation Attempt',
      {
        userId,
        fromRole,
        toRole,
        ip,
      },
      'high',
    );
  }

  static logDataAccess(userId: string, resource: string, action: string, ip: string): void {
    this.logSecurityEvent(
      'Data Access',
      {
        userId,
        resource,
        action,
        ip,
      },
      'low',
    );
  }
}
