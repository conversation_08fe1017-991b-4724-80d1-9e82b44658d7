/**
 * Secure File Upload Interceptor
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/File_Upload_Cheat_Sheet.html
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  BadRequestException,
  PayloadTooLargeException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import * as path from 'path';
import * as crypto from 'crypto';

interface FileValidationOptions {
  maxSize: number; // in bytes
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  maxFiles?: number;
  requireFileType?: boolean;
}

@Injectable()
export class SecureFileUploadInterceptor implements NestInterceptor {
  private readonly defaultOptions: FileValidationOptions = {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'text/plain',
    ],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt'],
    maxFiles: 5,
    requireFileType: true,
  };

  constructor(private options: Partial<FileValidationOptions> = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    
    try {
      this.validateFileUpload(request);
      return next.handle();
    } catch (error) {
      throw error;
    }
  }

  private validateFileUpload(request: Request): void {
    const files = request.files;
    const file = request.file;

    // Handle single file upload
    if (file) {
      this.validateSingleFile(file);
    }

    // Handle multiple file upload
    if (files) {
      if (Array.isArray(files)) {
        this.validateMultipleFiles(files);
      } else {
        // Handle files object (field-based upload)
        Object.values(files).forEach((fileArray) => {
          if (Array.isArray(fileArray)) {
            this.validateMultipleFiles(fileArray);
          } else {
            this.validateSingleFile(fileArray);
          }
        });
      }
    }
  }

  private validateSingleFile(file: Express.Multer.File): void {
    if (!file) {
      if (this.options.requireFileType) {
        throw new BadRequestException('File is required');
      }
      return;
    }

    // Validate file size
    if (file.size > this.options.maxSize!) {
      throw new PayloadTooLargeException(
        `File size exceeds maximum allowed size of ${this.options.maxSize! / (1024 * 1024)}MB`
      );
    }

    // Validate MIME type
    if (!this.options.allowedMimeTypes!.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type ${file.mimetype} is not allowed. Allowed types: ${this.options.allowedMimeTypes!.join(', ')}`
      );
    }

    // Validate file extension
    const fileExtension = path.extname(file.originalname).toLowerCase();
    if (!this.options.allowedExtensions!.includes(fileExtension)) {
      throw new BadRequestException(
        `File extension ${fileExtension} is not allowed. Allowed extensions: ${this.options.allowedExtensions!.join(', ')}`
      );
    }

    // Validate filename
    this.validateFilename(file.originalname);

    // Additional security checks
    this.performSecurityChecks(file);
  }

  private validateMultipleFiles(files: Express.Multer.File[]): void {
    if (files.length > this.options.maxFiles!) {
      throw new BadRequestException(
        `Too many files. Maximum allowed: ${this.options.maxFiles}`
      );
    }

    files.forEach((file) => this.validateSingleFile(file));
  }

  private validateFilename(filename: string): void {
    // Check for dangerous filename patterns
    const dangerousPatterns = [
      /\.\./,  // Directory traversal
      /[<>:"|?*]/,  // Invalid filename characters
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i,  // Reserved Windows names
      /^\./,  // Hidden files
      /\.$/, // Ending with dot
      /\x00/, // Null bytes
    ];

    if (dangerousPatterns.some(pattern => pattern.test(filename))) {
      throw new BadRequestException('Filename contains unsafe characters');
    }

    // Check filename length
    if (filename.length > 255) {
      throw new BadRequestException('Filename is too long');
    }

    // Check for executable extensions
    const executableExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
      '.jar', '.app', '.deb', '.pkg', '.dmg', '.sh', '.ps1'
    ];

    const fileExtension = path.extname(filename).toLowerCase();
    if (executableExtensions.includes(fileExtension)) {
      throw new BadRequestException('Executable files are not allowed');
    }
  }

  private performSecurityChecks(file: Express.Multer.File): void {
    // Check for embedded scripts in file content (basic check)
    if (file.buffer) {
      const content = file.buffer.toString('utf8', 0, Math.min(1024, file.buffer.length));
      
      // Check for script tags
      if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(content)) {
        throw new BadRequestException('File contains potentially malicious script content');
      }

      // Check for PHP tags
      if (/<\?php/gi.test(content)) {
        throw new BadRequestException('File contains potentially malicious PHP content');
      }
    }

    // Validate image files more strictly
    if (file.mimetype.startsWith('image/')) {
      this.validateImageFile(file);
    }
  }

  private validateImageFile(file: Express.Multer.File): void {
    // Basic image validation - check magic bytes
    if (!file.buffer) return;

    const magicBytes = file.buffer.slice(0, 10);
    const isValidImage = this.checkImageMagicBytes(magicBytes, file.mimetype);

    if (!isValidImage) {
      throw new BadRequestException('File is not a valid image or has been tampered with');
    }
  }

  private checkImageMagicBytes(buffer: Buffer, mimeType: string): boolean {
    const magicNumbers = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47],
      'image/gif': [0x47, 0x49, 0x46],
    };

    const expectedMagic = magicNumbers[mimeType as keyof typeof magicNumbers];
    if (!expectedMagic) return true; // Skip validation for unknown types

    return expectedMagic.every((byte, index) => buffer[index] === byte);
  }

  /**
   * Generate secure filename
   */
  static generateSecureFilename(originalName: string): string {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(16).toString('hex');
    const extension = path.extname(originalName).toLowerCase();
    
    return `${timestamp}_${randomString}${extension}`;
  }

  /**
   * Sanitize filename for storage
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace unsafe characters with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .substring(0, 100); // Limit length
  }
}

/**
 * Predefined file upload configurations
 */
export const FileUploadConfigs = {
  IMAGES: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif'],
    maxFiles: 10,
  },
  
  DOCUMENTS: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['application/pdf', 'text/plain', 'application/msword'],
    allowedExtensions: ['.pdf', '.txt', '.doc', '.docx'],
    maxFiles: 5,
  },
  
  AVATARS: {
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedMimeTypes: ['image/jpeg', 'image/png'],
    allowedExtensions: ['.jpg', '.jpeg', '.png'],
    maxFiles: 1,
  },
};
