import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ChatRoom } from '../modules/chat/entities/chat-room.entity';
import { Message } from '../modules/chat/entities/message.entity';
import { ChatRoomParticipant } from '../modules/chat/entities/chat-room-participant.entity';

@Injectable()
export class ChatPerformanceService {
  private readonly logger = new Logger(ChatPerformanceService.name);

  constructor(
    @InjectRepository(ChatRoom)
    private chatRoomRepository: Repository<ChatRoom>,
    
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    
    @InjectRepository(ChatRoomParticipant)
    private participantRepository: Repository<ChatRoomParticipant>,
    
    private dataSource: DataSource,
  ) {}

  // 🚀 OPTIMIZED CHAT ROOM QUERIES
  
  /**
   * Get active chat rooms with optimized query
   * Uses indexes: idx_chat_rooms_status_last_message, idx_chat_participants_room_guest
   */
  async getActiveChatRoomsOptimized(limit: number = 20): Promise<ChatRoom[]> {
    return await this.dataSource
      .createQueryBuilder(ChatRoom, 'room')
      .leftJoinAndSelect('room.participants', 'participant')
      .leftJoinAndSelect('participant.guest_user', 'guest_user')
      .leftJoinAndSelect('participant.system_user', 'system_user')
      .where('room.status = :status', { status: 'active' })
      .orderBy('room.last_message_at', 'DESC')
      .limit(limit)
      .getMany();
  }

  /**
   * Get chat room messages with pagination and optimized loading
   * Uses indexes: idx_chat_messages_room_timestamp, idx_chat_messages_active
   */
  async getChatRoomMessagesOptimized(
    chatRoomId: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<{ messages: Message[]; total: number; hasMore: boolean }> {
    
    // Use raw query for better performance with proper indexes
    const offset = (page - 1) * limit;
    
    const [messages, totalCount] = await Promise.all([
      this.dataSource.query(`
        SELECT 
          m.*,
          gu.name as guest_user_name,
          gu.email as guest_user_email,
          su.nama as system_user_name,
          su.email as system_user_email
        FROM chat_messages m
        LEFT JOIN chat_users gu ON m."senderGuestUserId" = gu.id
        LEFT JOIN users su ON m."senderSystemUserId" = su.id
        WHERE m."chatRoomId" = $1 
          AND m.is_deleted = false
        ORDER BY m.timestamp DESC
        LIMIT $2 OFFSET $3
      `, [chatRoomId, limit, offset]),
      
      this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM chat_messages 
        WHERE "chatRoomId" = $1 AND is_deleted = false
      `, [chatRoomId])
    ]);

    const total = parseInt(totalCount[0].count);
    const hasMore = offset + messages.length < total;

    return {
      messages,
      total,
      hasMore,
    };
  }

  /**
   * Get user's chat rooms with unread message counts
   * Optimized for real-time dashboard updates
   */
  async getUserChatRoomsWithUnreadCount(
    userId: string,
    userType: 'guest_user' | 'system_user',
  ): Promise<any[]> {
    
    const userColumn = userType === 'guest_user' ? 'guestUserId' : 'systemUserId';
    
    return await this.dataSource.query(`
      SELECT 
        r.*,
        COALESCE(unread.unread_count, 0) as unread_count,
        last_msg.content as last_message_content,
        last_msg.timestamp as last_message_time
      FROM chat_rooms r
      INNER JOIN chat_room_participants p ON r.id = p."chatRoomId"
      LEFT JOIN (
        SELECT 
          m."chatRoomId",
          COUNT(*) as unread_count
        FROM chat_messages m
        INNER JOIN chat_room_participants cp ON m."chatRoomId" = cp."chatRoomId"
        WHERE cp."${userColumn}" = $1
          AND m.timestamp > COALESCE(cp.last_read_message_id::timestamp, '1970-01-01'::timestamp)
          AND m.is_deleted = false
        GROUP BY m."chatRoomId"
      ) unread ON r.id = unread."chatRoomId"
      LEFT JOIN LATERAL (
        SELECT content, timestamp
        FROM chat_messages 
        WHERE "chatRoomId" = r.id AND is_deleted = false
        ORDER BY timestamp DESC 
        LIMIT 1
      ) last_msg ON true
      WHERE p."${userColumn}" = $1 
        AND p.status = 'active'
      ORDER BY r.last_message_at DESC
    `, [userId]);
  }

  /**
   * Optimized message insertion with room update
   * Updates last_message_at atomically
   */
  async insertMessageOptimized(messageData: Partial<Message>): Promise<Message> {
    return await this.dataSource.transaction(async manager => {
      // Insert message
      const message = await manager.save(Message, messageData);
      
      // Update room's last_message_at using optimized query
      await manager.query(`
        UPDATE chat_rooms 
        SET last_message_at = $1 
        WHERE id = $2
      `, [message.timestamp, messageData.chat_room]);
      
      return message;
    });
  }

  /**
   * Bulk mark messages as read (optimized for mobile apps)
   */
  async markMessagesAsReadOptimized(
    chatRoomId: string,
    userId: string,
    userType: 'guest_user' | 'system_user',
    lastMessageId: string,
  ): Promise<void> {
    
    const userColumn = userType === 'guest_user' ? 'guestUserId' : 'systemUserId';
    
    await this.dataSource.query(`
      UPDATE chat_room_participants 
      SET last_read_message_id = $1
      WHERE "chatRoomId" = $2 AND "${userColumn}" = $3
    `, [lastMessageId, chatRoomId, userId]);
  }

  /**
   * Get chat statistics for dashboard
   * Optimized with proper indexes
   */
  async getChatStatistics(): Promise<any> {
    const stats = await this.dataSource.query(`
      SELECT 
        (SELECT COUNT(*) FROM chat_rooms WHERE status = 'active') as active_rooms,
        (SELECT COUNT(*) FROM chat_users WHERE is_active = true) as active_users,
        (SELECT COUNT(*) FROM chat_messages WHERE created_at > NOW() - INTERVAL '1 hour') as messages_last_hour,
        (SELECT COUNT(*) FROM chat_messages WHERE DATE(created_at) = CURRENT_DATE) as messages_today,
        (SELECT COUNT(*) FROM chat_room_participants WHERE status = 'active') as active_participants,
        (SELECT AVG(EXTRACT(EPOCH FROM (NOW() - created_at))) FROM chat_messages WHERE created_at > NOW() - INTERVAL '1 day') as avg_response_time_seconds
    `);

    return stats[0];
  }

  /**
   * Clean up old chat data (maintenance task)
   * Removes messages older than specified days
   */
  async cleanupOldChatData(daysToKeep: number = 90): Promise<void> {
    this.logger.log(`Starting chat data cleanup, keeping ${daysToKeep} days`);
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    // Soft delete old messages
    const result = await this.dataSource.query(`
      UPDATE chat_messages 
      SET is_deleted = true, deleted_at = NOW()
      WHERE created_at < $1 AND is_deleted = false
    `, [cutoffDate]);
    
    this.logger.log(`Marked ${result.affectedRows} old messages as deleted`);
    
    // Clean up old inactive rooms
    await this.dataSource.query(`
      UPDATE chat_rooms 
      SET status = 'archived'
      WHERE last_message_at < $1 AND status = 'closed'
    `, [cutoffDate]);
  }

  /**
   * Optimize chat room participant queries
   * For operator assignment and room management
   */
  async getAvailableOperators(): Promise<any[]> {
    return await this.dataSource.query(`
      SELECT 
        co.*,
        COALESCE(active_rooms.room_count, 0) as current_room_count
      FROM chat_operators co
      LEFT JOIN (
        SELECT 
          p."systemUserId",
          COUNT(*) as room_count
        FROM chat_room_participants p
        INNER JOIN chat_rooms r ON p."chatRoomId" = r.id
        WHERE r.status = 'active' AND p.status = 'active'
        GROUP BY p."systemUserId"
      ) active_rooms ON co.id = active_rooms."systemUserId"
      WHERE co.is_active = true AND co.is_online = true
      ORDER BY current_room_count ASC, co.last_seen DESC
    `);
  }

  /**
   * Real-time room activity monitoring
   * For WebSocket updates and notifications
   */
  async getRoomActivity(roomId: string, sinceTimestamp: Date): Promise<any[]> {
    return await this.dataSource.query(`
      SELECT 
        m.*,
        CASE 
          WHEN m."senderGuestUserId" IS NOT NULL THEN 'guest'
          WHEN m."senderSystemUserId" IS NOT NULL THEN 'operator'
          ELSE 'system'
        END as sender_type_label,
        COALESCE(gu.name, su.nama, 'System') as sender_name
      FROM chat_messages m
      LEFT JOIN chat_users gu ON m."senderGuestUserId" = gu.id
      LEFT JOIN users su ON m."senderSystemUserId" = su.id
      WHERE m."chatRoomId" = $1 
        AND m.timestamp > $2
        AND m.is_deleted = false
      ORDER BY m.timestamp ASC
    `, [roomId, sinceTimestamp]);
  }

  /**
   * Performance monitoring for chat system
   */
  async getChatPerformanceMetrics(): Promise<any> {
    const metrics = await this.dataSource.query(`
      SELECT 
        'chat_rooms' as table_name,
        COUNT(*) as record_count,
        (SELECT COUNT(*) FROM chat_rooms WHERE status = 'active') as active_count,
        pg_size_pretty(pg_total_relation_size('chat_rooms')) as table_size
      FROM chat_rooms
      
      UNION ALL
      
      SELECT 
        'chat_messages' as table_name,
        COUNT(*) as record_count,
        (SELECT COUNT(*) FROM chat_messages WHERE is_deleted = false) as active_count,
        pg_size_pretty(pg_total_relation_size('chat_messages')) as table_size
      FROM chat_messages
      
      UNION ALL
      
      SELECT 
        'chat_users' as table_name,
        COUNT(*) as record_count,
        (SELECT COUNT(*) FROM chat_users WHERE is_active = true) as active_count,
        pg_size_pretty(pg_total_relation_size('chat_users')) as table_size
      FROM chat_users
    `);

    return metrics;
  }
}
