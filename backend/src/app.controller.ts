import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { Public } from './common/decorators';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  // @Controller('health')
  // export class HealthController {
  //@UseInterceptors(CacheInterceptor)
  @Public()
  @Get('health')
  async check() {
    return { status: 'ok' };
  }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }
}
