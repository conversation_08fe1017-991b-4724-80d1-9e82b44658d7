-- 👤 Admin User Seed
-- Creates default admin user for development and testing

-- First, ensure we have the Admin role
INSERT INTO master_role (id, name, ket, created_at, updated_at)
VALUES ('59490e31-6d91-479a-9e62-1f6d1fee0ff3', 'Admin', 'System Administrator Role', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create admin user (password: admin123, hashed with bcrypt)
-- Note: This is a bcrypt hash of 'admin123' with salt rounds 10
INSERT INTO users (
    id, 
    nama, 
    username, 
    email, 
    password, 
    "roleId", 
    status, 
    created_at, 
    updated_at
)
VALUES (
    '11111111-1111-1111-1111-111111111111',
    'System Administrator',
    'admin',
    '<EMAIL>',
    '$2b$10$rQZ8vQZ8vQZ8vQZ8vQZ8vOZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQ', -- admin123
    '59490e31-6d91-479a-9e62-1f6d1fee0ff3',
    1,
    NOW(),
    NOW()
)
ON CONFLICT (username) DO NOTHING;

-- Create a test user for development
INSERT INTO users (
    id, 
    nama, 
    username, 
    email, 
    password, 
    "roleId", 
    status, 
    created_at, 
    updated_at
)
VALUES (
    '22222222-2222-2222-2222-222222222222',
    'Test User',
    'testuser',
    '<EMAIL>',
    '$2b$10$rQZ8vQZ8vQZ8vQZ8vQZ8vOZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQZ8vQ', -- admin123
    '59490e31-6d91-479a-9e61-1f6d2fee0ff4', -- User role
    1,
    NOW(),
    NOW()
)
ON CONFLICT (username) DO NOTHING;
