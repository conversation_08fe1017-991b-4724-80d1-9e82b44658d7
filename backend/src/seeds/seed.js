require('dotenv').config({ path: '.env.local' });
const pgp = require('pg-promise')();

// Build connection config with SSL support
const connectionConfig = {
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
};

const db = pgp(connectionConfig);

async function runSeedScripts() {
  try {
    const sqlScripts = ['ticket-status.seed.sql', 'role-seed.sql']; // Add the names of your SQL seed scripts here
    for (const script of sqlScripts) {
      const sql = require('fs').readFileSync(`./src/seeds/${script}`, 'utf8');
      await db.none(sql);
      console.log(`Seed script ${script} executed successfully.`);
    }
  } catch (error) {
    console.error('Error executing seed scripts:', error);
  } finally {
    pgp.end();
  }
}

runSeedScripts();
