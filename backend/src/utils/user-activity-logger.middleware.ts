// user-activity-logger.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { UserService } from 'src/modules/user/user.service';
import * as jwt from 'jsonwebtoken';
import { EncryptionService } from 'src/common/decorators/encrypt_decrypt';

@Injectable()
export class UserActivityLoggerMiddleware implements NestMiddleware {
  constructor(
    private readonly usersService: UserService,
    private encryptionService: EncryptionService,
  ) {}
  async use(req, res, next) {
    try {
      if (req.method != 'GET') {
        var username = '-';
        try {
          if (req.cookies.Authentication) {
            const jwtCompile = jwt.verify(
              req.cookies.Authentication,
              process.env.JWT_ACCESS_TOKEN_SECRET,
            );
            const decryptedUser = this.encryptionService.decrypt(
              jwtCompile['username'],
            );
            username = decryptedUser;
          }
        } catch (error) {
          username = '-';
        }

        await this.usersService.saveActivity(
          req.ip,
          this.getUserOS(req.headers['user-agent']),
          req.method,
          req.originalUrl,
          username,
        );
      }
    } catch (error) {
      console.log('Error tracking activity');
    }
    next();
  }
  private getUserOS(userAgent: string): string {
    if (userAgent.includes('Linux')) {
      return 'Linux';
    } else if (userAgent.includes('Mac OS')) {
      return 'macOS';
    } else if (userAgent.includes('Windows')) {
      return 'Windows';
    } else {
      return 'Unknown';
    }
  }
}
