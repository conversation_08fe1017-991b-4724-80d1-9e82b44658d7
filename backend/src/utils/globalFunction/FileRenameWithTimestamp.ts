import { Injectable } from '@nestjs/common';

@Injectable()
export class FileService {
  getCurrentTimestamp(): number {
    return Date.now(); // Get the current timestamp in milliseconds
  }

  generateFilenameWithTimestamp(extension: string, tipe: string): string {
    const timestamp = this.getCurrentTimestamp();
    return `file_${tipe}_${timestamp}.${extension}`; // Customize the filename format as needed
  }
}
