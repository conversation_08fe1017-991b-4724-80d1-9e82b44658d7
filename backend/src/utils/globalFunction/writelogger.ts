// winston-logger.service.ts
import { Injectable, LoggerService } from '@nestjs/common';
import { createLogger, format, transports } from 'winston';

@Injectable()
export class WinstonLogger implements LoggerService {
  private readonly logger = createLogger({
    level: 'info', // Set the log level as needed
    format: format.combine(format.timestamp(), format.json()),
    transports: [
      new transports.File({ filename: 'error.log', level: 'error' }),
      new transports.File({ filename: 'combined.log' }),
    ],
  });

  log(message: string, context?: string) {
    console.log({
      level: 'info',
      message,
      context,
    });
  }

  error(message: string, trace: string, context?: string) {
    this.logger.error({
      level: 'error',
      message,
      trace,
      context,
    });
  }

  warn(message: string, context?: string) {
    this.logger.warn({
      level: 'warn',
      message,
      context,
    });
  }

  debug(message: string, context?: string) {
    this.logger.debug({
      level: 'debug',
      message,
      context,
    });
  }

  verbose(message: string, context?: string) {
    this.logger.verbose({
      level: 'verbose',
      message,
      context,
    });
  }
}
