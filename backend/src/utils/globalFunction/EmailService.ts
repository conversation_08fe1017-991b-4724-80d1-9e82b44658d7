import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs/promises';
import * as ejs from 'ejs';

@Injectable()
export class EmailService {
  generateUniqueTicketNumber(prefix: string = 'TICKET'): string {
    const timestamp = new Date().toISOString().replace(/[^0-9]/g, '');
    const uniqueId = this.generateUniqueId(); // Implement this function
    const ticketNumber = `${prefix}-${timestamp}-${uniqueId}`;

    return ticketNumber;
  }

  private generateUniqueId(): string {
    return Math.floor(1000 + Math.random() * 9000).toString();
  }
  async sendEmail(
    mailOptions: any,
    name: string,
    body: string,
    subject: string,
    ticket_id: string,
    ticket_status: string,
  ) {
    // Create a transporter object using your SMTP configuration
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
    const template = await fs.readFile(
      './dist/utils/globalFunction/templateEmail.ejs',
      'utf-8',
    );
    const renderedTemplate = ejs.render(template, {
      name,
      body,
      subject,
      ticket_id,
      ticket_status,
    });
    mailOptions['html'] = renderedTemplate;
    await transporter.sendMail(mailOptions);
  }
}
