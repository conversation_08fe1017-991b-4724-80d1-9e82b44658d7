import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'kontak' })
export class Kontak {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  alamat: string;

  @Column()
  no_telp: string;

  @Column()
  email: string;

  @Column()
  lat: string;

  @Column()
  long: string;

  @Column()
  facebook: string;

  @Column()
  instagram: string;

  @Column()
  twitter: string;

  @Column()
  website: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
