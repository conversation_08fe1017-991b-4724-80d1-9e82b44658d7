import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateKontakDto } from './dto/create-kontak.dto';
import { UpdateKontakDto } from './dto/update-kontak.dto';
import { Kontak } from './entities/kontak.entity';

@Injectable()
export class KontakService {
  constructor(
    @InjectRepository(Kontak)
    private repository: Repository<Kontak>,
  ) {}
  async create(data: CreateKontakDto) {
    const cekKontak = await this.repository.find({order: {created_at: 'DESC'}});
    let result;

    const request = new Kontak();

    request.alamat = data.alamat;
    request.email = data.email;
    request.no_telp = data.no_telp;
    request.lat = data.lat;
    request.long = data.long;
    request.facebook = data.facebook;
    request.twitter = data.twitter;
    request.instagram = data.instagram;
    request.website = data.website;

    if (cekKontak[0] == null) {
      result = this.repository.insert(request);
    } else {
      result = this.repository.update(cekKontak[0].id, request);
    }

    return result;
  }

  async findAll() {
    const data = await this.repository.find();
    return data;
  }

  async findOne(id: string) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  async update(id: string, data: UpdateKontakDto) {
    const request = new Kontak();
    request.alamat = data.alamat;
    request.email = data.email;
    request.no_telp = data.no_telp;
    request.lat = data.lat;
    request.long = data.long;
    request.facebook = data.facebook;
    request.twitter = data.twitter;
    request.instagram = data.instagram;

    request.website = data.website;
    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id: string) {
    const berita = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
  }
}
