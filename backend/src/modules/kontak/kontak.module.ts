import { Module } from '@nestjs/common';
import { KontakService } from './kontak.service';
import { KontakController } from './kontak.controller';
import { Kontak } from './entities/kontak.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([Kontak])],
  controllers: [KontakController],
  providers: [KontakService],
})
export class KontakModule {}
