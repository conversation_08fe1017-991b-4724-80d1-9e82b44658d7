import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreatePanduanPedomanDto } from './dto/create-panduan-pedoman.dto';
import { UpdatePanduanPedomanDto } from './dto/update-panduan-pedoman.dto';
import { PanduanPedoman } from './entities/panduan-pedoman.entity';

@Injectable()
export class PanduanPedomanService {
  constructor(
    @InjectRepository(PanduanPedoman)
    private repository: Repository<PanduanPedoman>,
  ) {}
  create(data: CreatePanduanPedomanDto, file_pdf: any) {
    const request = new PanduanPedoman();
    request.judul = data.judul;
    request.tanggal = data.tanggal;
    if (file_pdf) {
      request.file_pdf = file_pdf;
    }
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({ order: { tanggal: 'DESC' } });
    return data;
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  update(id: string, data: UpdatePanduanPedomanDto, file_pdf: any) {
    const request = new PanduanPedoman();
    request.judul = data.judul;
    request.tanggal = data.tanggal;
    if (file_pdf) {
      request.file_pdf = file_pdf;
    }
    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id: string) {
    try {
      const panduanpedoman = await this.repository.findOne({
        where: { id: id },
      });
      const data = await this.repository.delete(id);
    } catch (error) {}
    return `This action removes a #${id} artikel`;
  }
}
