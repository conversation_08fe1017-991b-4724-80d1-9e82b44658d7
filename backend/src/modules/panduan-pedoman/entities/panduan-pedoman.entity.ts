import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'panduan_pedoman' })
export class PanduanPedoman {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  judul: string;

  @Column()
  file_pdf: string;

  @Column({
    // type: 'datetime'
  })
  tanggal: Date;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
