import { Module } from '@nestjs/common';
import { PanduanPedomanService } from './panduan-pedoman.service';
import { PanduanPedomanController } from './panduan-pedoman.controller';
import { PanduanPedoman } from './entities/panduan-pedoman.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([PanduanPedoman])],
  controllers: [PanduanPedomanController],
  providers: [PanduanPedomanService],
})
export class PanduanPedomanModule {}
