import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'artikel' })
export class Artikel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  judul: string;

  @Column()
  slug: string;

  @Column()
  kategori: string;

  @Column()
  foto: string;

  @Column({ nullable: true })
  file_pdf: string;

  @Column({
    type: 'text',
  })
  isi: string;

  @Column({
    // type: 'datetime'
  })
  tgl_posting: Date;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
