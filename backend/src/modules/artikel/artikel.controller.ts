import {
  Controller,
  Request,
  Get,
  Post,
  Body,
  Param,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { createWriteStream } from 'fs';
import { ArtikelService } from './artikel.service';
import * as fs from 'fs';
import { CreateArtikelDto } from './dto/create-artikel.dto';
import { StorageConfig } from 'src/config/storage.config';

@Controller('v1/artikel')
@ApiTags('Modul Artikel')
export class ArtikelController {
  constructor(private readonly artikelService: ArtikelService) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        kategori: { type: 'string' },
        isi: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([{ name: 'foto' }, { name: 'file_pdf' }]),
  )
  @Post()
  async create(
    @Body() dataArtikel: CreateArtikelDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file_pdf']) {
        const pdfFile = files['file_pdf'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file artikel');
    }

    const data = await this.artikelService.create(
      dataArtikel,
      namaFile,
      namaFile_pdf,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Artikel Berhasil Di Tambah',
    };
  }

  @Get()
  async findAll() {
    const data = await this.artikelService.findAll();
    return {
      success: true,
      message: 'Data Artikel',
      data: data,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.artikelService.findOne(id);
    return {
      success: true,
      message: 'Data Artikel By ID',
      data: data,
    };
  }

  @Post(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        judul_en: { type: 'string' },
        kategori: { type: 'string' },
        kategori_en: { type: 'string' },
        isi: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([{ name: 'foto' }, { name: 'file_pdf' }]),
  )
  async update(
    @Param('id') id: string,
    @Body() dataArtikel: CreateArtikelDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file_pdf']) {
        const pdfFile = files['file_pdf'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file artikel');
    }

    const data = await this.artikelService.update(
      id,
      dataArtikel,
      namaFile,
      namaFile_pdf,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Artikel Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string, @Request() req) {
    const data = await this.artikelService.remove(id, req.headers.id_user);
    return {
      success: true,
      message: 'Data Artikel Berhasil Di Hapus',
    };
  }
}
