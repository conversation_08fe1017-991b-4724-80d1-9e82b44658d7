import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateArtikelDto } from './dto/create-artikel.dto';
import { UpdateArtikelDto } from './dto/update-artikel.dto';
import { Artikel } from './entities/artikel.entity';

@Injectable()
export class ArtikelService {
  constructor(
    @InjectRepository(Artikel)
    private repository: Repository<Artikel>,
  ) {}

  create(
    dataArtikel: CreateArtikelDto,
    foto: any,
    file_pdf: any,
    id_user: string,
  ) {
    const request = new Artikel();

    request.judul = dataArtikel.judul;

    request.kategori = dataArtikel.kategori;

    request.slug = dataArtikel.judul.toLowerCase().split(' ').join('-');

    request.isi = dataArtikel.isi;

    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file_pdf = file_pdf;
    }
    request.tgl_posting = dataArtikel.tgl_posting;
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({ order: { tgl_posting: 'DESC' } });
    return data;
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  update(
    id,
    dataArtikel: UpdateArtikelDto,
    foto: any,
    file_pdf: any,
    id_user: string,
  ) {
    const request = new Artikel();
    request.judul = dataArtikel.judul;
    request.kategori = dataArtikel.kategori;
    request.slug = dataArtikel.judul.toLowerCase().split(' ').join('-');

    request.isi = dataArtikel.isi;

    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file_pdf = file_pdf;
    }
    request.tgl_posting = dataArtikel.tgl_posting;

    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id, id_user: string) {
    const berita = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return `This action removes a #${id} artikel`;
  }
}
