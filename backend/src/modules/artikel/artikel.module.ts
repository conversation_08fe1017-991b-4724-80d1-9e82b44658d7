import { Module } from '@nestjs/common';
import { ArtikelService } from './artikel.service';
import { ArtikelController } from './artikel.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Artikel } from './entities/artikel.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Artikel])],
  controllers: [ArtikelController],
  providers: [ArtikelService],
})
export class ArtikelModule {}
