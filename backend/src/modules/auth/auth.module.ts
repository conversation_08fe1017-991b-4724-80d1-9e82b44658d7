import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { UserModule } from '../user/user.module';
import { JwtRefreshStrategy } from './strategies/jwt-refresh.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { Auth } from './entities/auth.entity';
import { HttpModule } from '@nestjs/axios';
import { MasterRole } from '../master_role/entities/master_role.entity';
import { Menu } from '../menu/entities/menu.entity';
import { SessionService } from './services/session.service';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([User, Auth, MasterRole, Menu]),
    UserModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: process.env.JWT_ACCESS_TOKEN_SECRET,
        signOptions: {
          expiresIn: `${process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME}s`,
        },
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [
    SessionService,
    AuthService,
    LocalStrategy,
    JwtStrategy,
    ConfigService,
    JwtRefreshStrategy,
  ],
  exports: [AuthService, SessionService],
})
export class AuthModule {}
