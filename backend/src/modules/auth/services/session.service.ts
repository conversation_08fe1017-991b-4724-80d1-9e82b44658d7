import { Injectable } from '@nestjs/common';

interface SessionData {
  username?: string;
  loginAttempts?: number;
  lastAttempt?: Date;
  isDisabled?: boolean;
  disabledUntil?: Date;
}

interface CaptchaData {
  text: string;
  sessionId: string;
  createdAt: Date;
}

@Injectable()
export class SessionService {
  private sessions = new Map<string, SessionData>();
  private captchaStore = new Map<string, CaptchaData>();
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly DISABLE_DURATION = 20 * 60 * 1000; // 20 minutes
  private readonly CAPTCHA_EXPIRY = 5 * 60 * 1000; // 5 minutes

  constructor() {
    // Clean up expired sessions and captcha every 10 minutes
    setInterval(
      () => {
        this.cleanupExpiredData();
      },
      10 * 60 * 1000,
    );
  }

  /**
   * Save user login session
   */
  async saveSessionLogin(username: string): Promise<void> {
    const session = this.sessions.get(username) || {};
    session.username = username;
    session.loginAttempts = 0;
    this.sessions.set(username, session);
  }

  /**
   * Record login error and potentially disable user
   */
  async saveSessionLoginError(username: string): Promise<void> {
    const session = this.sessions.get(username) || {};
    session.loginAttempts = (session.loginAttempts || 0) + 1;
    session.lastAttempt = new Date();

    if (session.loginAttempts >= this.MAX_LOGIN_ATTEMPTS) {
      session.isDisabled = true;
      session.disabledUntil = new Date(Date.now() + this.DISABLE_DURATION);
    }

    this.sessions.set(username, session);
  }

  /**
   * Check if user is currently logged in
   */
  async checkUserLogin(username: string): Promise<boolean> {
    const session = this.sessions.get(username);
    return session?.username === username && !session.isDisabled;
  }

  /**
   * Check if user is disabled due to too many failed attempts
   */
  async checkUserDisable(username: string): Promise<boolean> {
    const session = this.sessions.get(username);
    if (!session?.isDisabled) return false;

    // Check if disable period has expired
    if (session.disabledUntil && new Date() > session.disabledUntil) {
      session.isDisabled = false;
      session.loginAttempts = 0;
      session.disabledUntil = undefined;
      this.sessions.set(username, session);
      return false;
    }

    return true;
  }

  /**
   * Get number of login errors for user
   */
  async checkUserLoginError(username: string): Promise<number> {
    const session = this.sessions.get(username);
    return session?.loginAttempts || 0;
  }

  /**
   * Clear user session on logout
   */
  async logoutUser(username: string): Promise<void> {
    this.sessions.delete(username);
  }

  /**
   * Store captcha data
   */
  async storeCaptcha(sessionId: string, captchaText: string): Promise<void> {
    this.captchaStore.set(sessionId, {
      text: captchaText,
      sessionId,
      createdAt: new Date(),
    });
  }

  /**
   * Verify captcha
   */
  async verifyCaptcha(sessionId: string, userInput: string): Promise<boolean> {
    const captchaData = this.captchaStore.get(sessionId);

    if (!captchaData) return false;

    // Check if captcha has expired
    const now = new Date();
    if (now.getTime() - captchaData.createdAt.getTime() > this.CAPTCHA_EXPIRY) {
      this.captchaStore.delete(sessionId);
      return false;
    }

    // Verify captcha text (case-insensitive)
    const isValid = captchaData.text.toLowerCase() === userInput.toLowerCase();

    // Remove captcha after verification (one-time use)
    this.captchaStore.delete(sessionId);

    return isValid;
  }

  /**
   * Generate new session ID
   */
  generateSessionId(): string {
    return (
      'sess_' +
      Math.random().toString(36).substr(2, 16) +
      Date.now().toString(36)
    );
  }

  /**
   * Clean up expired data
   */
  private cleanupExpiredData(): void {
    const now = new Date();

    // Clean up expired captcha
    for (const [sessionId, captchaData] of this.captchaStore.entries()) {
      if (
        now.getTime() - captchaData.createdAt.getTime() >
        this.CAPTCHA_EXPIRY
      ) {
        this.captchaStore.delete(sessionId);
      }
    }

    // Clean up expired disabled users
    for (const [username, session] of this.sessions.entries()) {
      if (
        session.isDisabled &&
        session.disabledUntil &&
        now > session.disabledUntil
      ) {
        session.isDisabled = false;
        session.loginAttempts = 0;
        session.disabledUntil = undefined;
        this.sessions.set(username, session);
      }
    }
  }

  /**
   * Get session statistics (for monitoring)
   */
  getSessionStats(): {
    activeSessions: number;
    activeCaptchas: number;
    disabledUsers: number;
  } {
    const disabledUsers = Array.from(this.sessions.values()).filter(
      (session) => session.isDisabled,
    ).length;

    return {
      activeSessions: this.sessions.size,
      activeCaptchas: this.captchaStore.size,
      disabledUsers,
    };
  }
}
