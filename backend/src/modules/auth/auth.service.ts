import {
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { ResetPasswordByUserDto } from './dto/reset-password-by-user.dto';
import { User } from '../user/entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Auth } from './entities/auth.entity';
import { MasterRole } from '../master_role/entities/master_role.entity';
import { Menu } from '../menu/entities/menu.entity';
import { EncryptionService } from 'src/common/decorators/encrypt_decrypt';
import { SessionService } from './services/session.service';
import { PasswordValidator } from 'src/common/validators/password.validator';
import { TokenPayload } from './interface/tokenPayload.interface';

@Injectable()
export class AuthService {
  constructor(
    private encryptionService: EncryptionService,
    private sessionService: SessionService,
    private userService: UserService,
    private jwtService: JwtService,
    @InjectRepository(User)
    private repositoryUser: Repository<User>,
    @InjectRepository(MasterRole)
    private repo_role: Repository<MasterRole>,
    @InjectRepository(Auth)
    private repositoryAuth: Repository<Auth>,
    @InjectRepository(Menu)
    private repo_menu: Repository<Menu>,
  ) {}
  async validateUser2(profile: any, ss: string): Promise<any> {
    // Here, you can validate the user and extract the needed info
    return profile;
  }

  async validateUser(username: string, password: string): Promise<any> {
    try {
      const user = await this.userService.findOne(username);

      if (!user) {
        return null;
      }
      const isPasswordMatch = await this.comparePassword(
        password,
        user.password,
        // decryptData(user.password).toString(),
      );
      if (isPasswordMatch) {
        const { password, ...result } = user;
        return result;
      }
      await this.sessionService.saveSessionLoginError(username);

      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async checkuserauth(username, password) {
    try {
      const user = await this.userService.findOne(username);
      if (user && (await bcrypt.compare(password, user.password))) {
        return user;
      } else {
        throw new ForbiddenException('Access Denied');
      }
    } catch (error) {
      console.log('error auth checkuserauth');
    }
  }

  async login(user: any, password: string) {
    try {
      if (!user) {
        throw new ForbiddenException('Access Denied');
      }
      console.log(user);

      const userRet = await this.repositoryUser.findOne({
        where: { id: user.id },
        relations: ['role.hak_akses'],
      });

      var payload: TokenPayload = {
        username: this.encryptionService.encrypt(userRet.username),
        id: this.encryptionService.encrypt(userRet.id),
        role: this.encryptionService.encrypt(userRet.role.name),
      };

      const tokens = await this.getToken(payload);
      // await this.setCurrentRefreshToken(payload.id, tokens.refresh_token);
      if (userRet) {
        const re = await this.repo_menu.find();
        userRet.role.hak_akses.map((d) => {
          var dataret = {};
          const cc = re.map((dd) => {
            if (dd.id == d.id_menu) dataret = dd;
            else return null;
          });
          d['menu'] = dataret;
        });

        // return { token: tokens, user: userRet };
      }
      return {
        token: tokens,
        user: userRet,
      };
    } catch (error) {
      console.log('error login password');
      console.log(error);
    }
  }

  async create(request: any, namaFile: any) {
    try {
      const role = new MasterRole();
      role.id = request.roleId;
      request.role = role;
      const result = await this.userService.create(request, namaFile);
      return result;
    } catch (error) {
      // console.log(error);
      new ForbiddenException('Error');
    }
  }

  async removeRefreshToken(id: string) {
    await this.userService.updateToken(id, { currentHashedRefreshToken: null });
    return true;
  }

  async getToken(payload: any) {
    const [access_token, refresh_token] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: process.env.JWT_ACCESS_TOKEN_SECRET,
        expiresIn: `${process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME}s`,
      }),
      this.jwtService.signAsync(payload, {
        secret: process.env.JWT_REFRESH_TOKEN_SECRET,
        expiresIn: `${process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME}s`,
      }),
    ]);
    return {
      access_token,
      refresh_token,
    };
  }

  async comparePassword(password: string, hashPasword: string) {
    return bcrypt.compare(password, hashPasword);
  }

  async refreshToken(userId: string, refreshToken: string) {
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new HttpException(
        'Refresh token : user tidak ditemukan',
        HttpStatus.NOT_FOUND,
      );
    }
    const isRefreshTokenMatching = await bcrypt.compare(
      refreshToken,
      user.currentHashedRefreshToken,
    );
    if (!isRefreshTokenMatching) {
      throw new ForbiddenException('Access Denied');
    }
    const { password, ...result } = user;

    const token = await this.getToken({
      id: user.id,
      username: user.username,
      role: user.role.name,
    });
    return {
      user: result,
      token,
    };
  }

  async setCurrentRefreshToken(id: string, refreshToken: string) {
    const currentHashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    const result = await this.userService.updateToken(id, {
      currentHashedRefreshToken,
    });
    return result;
  }

  public getCookieWithJwtAccessToken(access_token: string) {
    return `Authentication=${access_token}; HttpOnly; Secure; SameSite=Lax; Path=/; Max-Age=${process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME}`;
  }

  public getCookieWithJwtRefreshToken(token) {
    return `Refresh=${token}; HttpOnly; Secure; SameSite=Lax; Path=/; Max-Age=${process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME}`;
  }

  public getCookieWithUsername(Username) {
    return `Username=${Username}; HttpOnly; Secure; SameSite=Lax; Path=/; Max-Age=${process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME}`;
  }

  public getCookieForLogOut() {
    return `Authentication=; HttpOnly; Secure; SameSite=Lax; Path=/; Max-Age=0`;
  }
  public getCookieForLogOutRefresh() {
    return `Refresh=; HttpOnly; Secure; SameSite=Lax; Path=/; Max-Age=0`;
  }

  async resetPassword(id, request) {
    if (!id) {
      throw new HttpException('User tidak dikenali', HttpStatus.BAD_REQUEST);
    }
    const { password, password_confirm } = request;
    if (password !== password_confirm) {
      throw new HttpException(
        'password konfirmasi tidak sama',
        HttpStatus.BAD_REQUEST,
      );
    }
    const hashPassword = await this.userService.encryptPassword(password);
    const result = await this.userService.updateToken(id, {
      password: hashPassword,
    });
    return result;
  }

  validatePassword(password: string, username?: string): { isValid: boolean; errors: string[] } {
    const validation = PasswordValidator.validate(password, username);
    return {
      isValid: validation.isValid,
      errors: validation.errors
    };
  }

  isStrongPassword(password: string, username?: string): boolean {
    return PasswordValidator.validate(password, username).isValid;
  }

  async resetPasswordByUser(
    usernameEncrypt: string,
    resetPasswordByUser: ResetPasswordByUserDto,
  ) {
    try {
      const username = this.encryptionService.decrypt(usernameEncrypt);
      const user = await this.repositoryUser.findOne({
        where: { username: username },
      });
      if (!user) {
        return {
          status: true,
          message: 'Success',
        };
        throw new HttpException('Reset Password Error', HttpStatus.BAD_REQUEST);
      }
      // const {password, password_confirm} = request;
      if (
        resetPasswordByUser.password_baru !==
        resetPasswordByUser.password_baru_confirm
      ) {
        return {
          status: true,
          message: 'Success',
        };
        throw new HttpException('Reset Password Error', HttpStatus.BAD_REQUEST);
      } else {
        const isPasswordMatch = await this.comparePassword(
          resetPasswordByUser.password_lama,
          user.password,
          // decryptData(user.password).toString(),
        );
        if (isPasswordMatch) {
          const hashPassword = await this.userService.encryptPassword(
            resetPasswordByUser.password_baru,
          );
          // const get = getManager();

          // Use parameterized query to prevent SQL injection
          const data = await this.repositoryUser.update(
            { username },
            { password: hashPassword }
          );
        } else {
          return {
            status: true,
            message: 'Success',
          };
        }
      }
    } catch (error) {
      console.log('error reset password');
      return {
        status: true,
        message: 'Success',
      };
    }

    return {
      success: true,
      pesan: 'Success',
    };
  }

  async saveSessionLogin(username: string): Promise<void> {
    await this.sessionService.saveSessionLogin(username);
  }

  async saveSessionLoginError(username: string): Promise<void> {
    await this.sessionService.saveSessionLoginError(username);
  }

  async logoutWithSession(username: string): Promise<void> {
    await this.sessionService.logoutUser(username);
  }

  async checkUserLogin(username: string): Promise<boolean> {
    return await this.sessionService.checkUserLogin(username);
  }

  async checkUserDisable(username: string): Promise<boolean> {
    return await this.sessionService.checkUserDisable(username);
  }

  async checkUserLoginError(username: string): Promise<number> {
    return await this.sessionService.checkUserLoginError(username);
  }

  generateNewSessionId(): string {
    return this.sessionService.generateSessionId();
  }

  async storeCaptcha(sessionId: string, captchaText: string): Promise<void> {
    await this.sessionService.storeCaptcha(sessionId, captchaText);
  }

  async verifyCaptcha(sessionId: string, userInput: string): Promise<boolean> {
    return await this.sessionService.verifyCaptcha(sessionId, userInput);
  }

  getCookieOptions() {
    return {
      httpOnly: true,
      secure: true, // Set true if using HTTPS
      sameSite: 'Strict',
      maxAge: 24 * 60 * 60 * 1000, // Example: 1 day
    };
  }
}
