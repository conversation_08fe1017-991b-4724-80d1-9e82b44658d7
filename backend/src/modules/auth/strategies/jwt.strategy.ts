import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
// import { jwtConstants } from './constants';
import { UserService } from '../../user/user.service';
import { Request } from 'express';
import { isCookieJwt } from 'src/utils';
import { TokenPayload } from '../interface/tokenPayload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly usersService: UserService) {
    super({
      jwtFromRequest: isCookieJwt()
        ? ExtractJwt.fromExtractors([
            (request: Request) => {
              return request?.cookies?.Authentication;
            },
          ])
        : ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_ACCESS_TOKEN_SECRET,
    });
  }

  async validate(payload: TokenPayload) {
    return await this.usersService.findOne(payload.username);
  }
}
