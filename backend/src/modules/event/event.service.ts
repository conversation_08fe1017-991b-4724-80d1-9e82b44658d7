import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as fs from 'fs';
import { Repository } from 'typeorm';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { Event } from './entities/event.entity';


@Injectable()
export class EventService {
  constructor(
    @InjectRepository(Event)
    private repository: Repository<Event>,
  ) { }

  create(data: CreateEventDto, file: any) {
    const request = new Event();
    request.judul = data.judul;
    request.tempat = data.tempat;
    request.tgl_awal = data.tgl_awal.toString();
    request.tgl_akhir = data.tgl_akhir.toString();
    request.files = file;
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({ order: { created_at: 'DESC' } });
    return data;
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  async update(id, data: UpdateEventDto, file: any) {
    const request = new Event();
    request.judul = data.judul;
    request.tempat = data.tempat;
    request.tgl_awal = data.tgl_awal.toString();
    request.tgl_akhir = data.tgl_akhir.toString();

    request.files = file; // Langsung assign file baru, tidak perlu pemeriksaan

    const result = this.repository.update(id, request);
    return result;
  }



  async remove(id, id_user: string) {
    const berita = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return `This action removes a #${id} artikel`;
  }

  async deleteFileOne(id, name: string) {
    const event = await this.repository.findOne({ where: { id: id } });
    var filenow = [];
    try {
      event.files.forEach(async (file) => {
        if (file == name) {
          fs.unlinkSync(`uploads/event/${name}`);
        } else {
          filenow.push(file);
        }
      });
    } catch (error) {
      console.log('error in event delete' + error);
    }
    event.files = filenow;
    const result = this.repository.update(id, event);

    return `This action removes a file event`;
  }


}
