import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEmpty } from 'class-validator';

// export class CreateEventDto {
//   @ApiProperty()
//   judul: string;

//   // @IsNotEmpty()
//   @ApiProperty()
//   kategori: string;

//   @ApiProperty()
//   isi: string;

//   @ApiProperty({nullable:true})
//   tgl_posting: Date;

//   @ApiProperty()
//   foto: string;

//   @ApiProperty()
//   file_pdf: string;

//   @ApiProperty({
//     type: 'array',
//     items: { type: 'string', format: 'binary' },
//     description: 'Attachment Files',
//   })
//   @IsArray()
//   attachments: string[];
// }

// import { ApiProperty } from '@nestjs/swagger';

export class CreateEventDto {
  @ApiProperty()
  judul: string;

  // @IsNotEmpty()
  // @ApiProperty()
  // kategori: string;

  @ApiProperty()
  @IsEmpty()
  tempat: string;

  @ApiProperty()
  tgl_awal: Date;
  @ApiProperty()
  tgl_akhir: Date;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    description: 'Attachment Files',
  })
  @IsArray()
  files: string[];
}
