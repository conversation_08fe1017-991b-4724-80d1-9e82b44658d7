import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateBeritaDto } from './dto/create-berita.dto';
import { UpdateBeritaDto } from './dto/update-berita.dto';
import { Berita } from './entities/berita.entity';

@Injectable()
export class BeritaService {
  constructor(
    @InjectRepository(Berita)
    private repository: Repository<Berita>,
  ) {}

  create(createBeritaDto: CreateBeritaDto, foto: any, id_user: string) {
    const request = new Berita();

    request.judul = createBeritaDto.judul;

    request.kategori = createBeritaDto.kategori;

    request.slug = createBeritaDto.judul.toLowerCase().split(' ').join('-');

    request.isi = createBeritaDto.isi;

    if (foto) {
      request.foto = foto;
    }
    request.tgl_posting = createBeritaDto.tgl_posting;
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({ order: { tgl_posting: 'DESC' } });
    return data;
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  update(id, updateBeritaDto: UpdateBeritaDto, foto: any, id_user: string) {
    const request = new Berita();
    request.judul = updateBeritaDto.judul;
    request.kategori = updateBeritaDto.kategori;
    request.slug = updateBeritaDto.judul.toLowerCase().split(' ').join('-');

    request.isi = updateBeritaDto.isi;

    if (foto) {
      request.foto = foto;
    }
    request.tgl_posting = updateBeritaDto.tgl_posting;

    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id, id_user: string) {
    const berita = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return `This action removes a #${id} berita`;
  }

  async removeSelected(deleteSelected: any, id_user: string) {
    let berita;
    let log;

    // console.log(deleteSelected);
    // deleteSelected.id.forEach(async (element) => {
    //   berita = await this.repository.findOne({ where: {id: element}});
    //   log = new LogHistory();
    //   log.id_user = `${id_user}`;
    //   log.aksi = 'Delete';
    //   log.menu = 'Berita';
    //   log.detail = `Hapus Data Berita '${berita.judul}'`;

    //   await this.repositoryLog.insert(log);

    //   await this.repository.delete(element);
    // });
    return `This action removes a berita`;
  }
}
