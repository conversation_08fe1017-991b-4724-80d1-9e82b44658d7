import { Module } from '@nestjs/common';
import { BeritaService } from './berita.service';
import { BeritaController } from './berita.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Berita } from './entities/berita.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Berita])],
  exports: [BeritaService],
  controllers: [BeritaController],
  providers: [BeritaService],
})
export class BeritaModule {}
