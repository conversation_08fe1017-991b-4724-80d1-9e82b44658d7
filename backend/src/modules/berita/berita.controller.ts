import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseInterceptors,
  UploadedFile,
  Request,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { createWriteStream } from 'fs';
import { DeleteSelected } from 'src/dto/deleteSelected.dto';
import { BeritaService } from './berita.service';
import { CreateBeritaDto } from './dto/create-berita.dto';
import { UpdateBeritaDto } from './dto/update-berita.dto';
import * as fs from 'fs';
import { StorageConfig } from 'src/config/storage.config';

@ApiTags('Modul Berita')
@Controller('v1/berita')
export class BeritaController {
  constructor(private readonly beritaService: BeritaService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        kategori: { type: 'string' },
        isi: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  async create(
    @Body() createBeritaDto: CreateBeritaDto,
    @UploadedFile() foto: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile;

    try {
      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file berita');
    }

    const data = await this.beritaService.create(
      createBeritaDto,
      namaFile,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Berita Berhasil Di Tambah',
    };
  }

  //@UseInterceptors(CacheInterceptor)
  @Get()
  async findAll() {
    const data = await this.beritaService.findAll();
    return {
      success: true,
      message: 'Data Berita',
      data: data,
    };
  }

  //@UseInterceptors(CacheInterceptor)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.beritaService.findOne(id);
    return {
      success: true,
      message: 'Data Berita By ID',
      data: data,
    };
  }

  @Post(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        judul_en: { type: 'string' },
        kategori: { type: 'string' },
        kategori_en: { type: 'string' },
        isi: { type: 'text' },
        isi_en: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  async update(
    @Param('id') id: string,
    @Body() updateBeritaDto: UpdateBeritaDto,
    @UploadedFile() foto: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile;

    try {
      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file berita');
    }

    const data = await this.beritaService.update(
      id,
      updateBeritaDto,
      namaFile,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Berita Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string, @Request() req) {
    const data = await this.beritaService.remove(id, req.headers.id_user);
    return {
      success: true,
      message: 'Data Berita Berhasil Di Hapus',
    };
  }

  @Post('deleteSelected')
  async removeSelected(@Body() deleteSelected: DeleteSelected, @Request() req) {
    console.log(deleteSelected.id);
    // const data = await this.beritaService.removeSelected(deleteSelected, req.user.id);
    return {
      success: true,
      message: 'Data Berita Berhasil Di Hapus',
    };
  }
}
