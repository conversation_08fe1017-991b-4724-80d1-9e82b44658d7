import { ApiProperty } from '@nestjs/swagger';
import { IsArray } from 'class-validator';

export class CreateTicketingDto {
  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  subject: string;

  @ApiProperty()
  user_input: string;

  @ApiProperty()
  body: string;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'binary' },
    description: 'Attachment Files',
  })
  @IsArray()
  files: string[];
}

export class UpdateProgressTicketingDto {
  @ApiProperty()
  ket: string;
  @ApiProperty()
  id_progres: string;
}
