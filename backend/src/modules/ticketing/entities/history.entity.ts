import { MasterProgresTicketEntity } from 'src/modules/master_progres_ticket/entities/master_progres_ticket.entity';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TicketingEntity } from './ticketing.entity';

@Entity({ name: 'riwayat_ticket' })
export class HistoryTicketingEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => TicketingEntity, (data) => data.id, {
    nullable: false,
  })
  @JoinColumn()
  ticket: TicketingEntity;

  @ManyToOne(() => MasterProgresTicketEntity, (progres) => progres.id, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  progres: MasterProgresTicketEntity;

  @Column({ nullable: true })
  ket: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
