import { MasterProgresTicketEntity } from 'src/modules/master_progres_ticket/entities/master_progres_ticket.entity';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'ticketing' })
export class TicketingEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => MasterProgresTicketEntity, (progres) => progres.id, {
    nullable: true,
  })
  @JoinColumn()
  progres: MasterProgresTicketEntity;

  @Column({ nullable: true })
  number: string;

  @Column({ nullable: true })
  ket: string;

  @Column()
  name: string;

  @Column()
  email: string;

  @Column()
  subject: string;

  @Column()
  body: string;

  @Column()
  attachment: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
