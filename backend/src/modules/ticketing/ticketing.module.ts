import { Modu<PERSON> } from '@nestjs/common';
import { TicketingService } from './ticketing.service';
import { TicketingController } from './ticketing.controller';
import { EmailService } from 'src/utils/globalFunction/EmailService';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { TicketingEntity } from './entities/ticketing.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MasterProgresTicketEntity } from '../master_progres_ticket/entities/master_progres_ticket.entity';
import { HistoryTicketingEntity } from './entities/history.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TicketingEntity,
      MasterProgresTicketEntity,
      HistoryTicketingEntity,
    ]),
  ],
  controllers: [TicketingController],
  providers: [TicketingService, EmailService, FileService],
})
export class TicketingModule {}
