import { Injectable } from '@nestjs/common';
import {
  CreateTicketingDto,
  UpdateProgressTicketingDto,
} from './dto/create-ticketing.dto';
import { UpdateTicketingDto } from './dto/update-ticketing.dto';
import { TicketingEntity } from './entities/ticketing.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { EmailService } from 'src/utils/globalFunction/EmailService';
import * as fs from 'fs';
import * as util from 'util';
import { MasterProgresTicketEntity } from '../master_progres_ticket/entities/master_progres_ticket.entity';
import { HistoryTicketingEntity } from './entities/history.entity';

@Injectable()
export class TicketingService {
  constructor(
    @InjectRepository(TicketingEntity)
    private repository: Repository<TicketingEntity>,
    @InjectRepository(HistoryTicketingEntity)
    private repo_history: Repository<HistoryTicketingEntity>,
    @InjectRepository(MasterProgresTicketEntity)
    private d: Repository<MasterProgresTicketEntity>,
    private readonly emailService: EmailService,
  ) {}
  async create(data: CreateTicketingDto, attachment: any) {
    const request = new TicketingEntity();
    const master = new MasterProgresTicketEntity();
    const history = new HistoryTicketingEntity();
    request.number = this.emailService.generateUniqueTicketNumber();
    console.log(request.number);

    master.id = '59490e31-6d91-479a-9e62-1f6d8fee0ff3';
    try {
      const mailOptions = {
        from: process.env.SMTP_USER,
        to: data.email,
        subject: data.subject,
      };
      if (attachment) {
        request.attachment = JSON.stringify(
          attachment.map((file, i) => {
            return {
              filename: file.filename,
              mime: file.mime,
            };
          }),
        );
        mailOptions['attachments'] = attachment.map((file, i) => {
          return {
            filename: file.filename, // Name of the attachment
            content: file.data, // Buffer containing the attachment data
          };
        });
      }
      await this.emailService.sendEmail(
        mailOptions,
        data.name,
        data.body,
        data.subject,
        request.number,
        'Open',
      );
      request.progres = master;
      request.name = data.name;
      request.email = data.email;
      request.subject = data.subject;
      request.body = data.body;
      const result = await this.repository.insert(request);
      if (result) {
        history.progres = master;
        history.ticket = request;
        await this.repo_history.insert(history);
      }
      return true;
    } catch (error) {
      console.log('ticketing', error);

      return false;
    }
  }

  async findAll() {
    const data = await this.repository.find({
      order: { created_at: 'DESC' },
      relations: ['progres'],
    });
    return data;
  }

  async findOne(id: string) {
    const data = await this.repository.findOne({
      where: { id: id },
      relations: ['progres'],
    });
    return data;
  }

  async findNumberOne(number: string) {
    try {
      const data = await this.repository.findOne({
        where: { number: number },
        relations: ['progres'],
      });
      return data;
    } catch (error) {}
  }

  async findHistoryTicketOne(id: string) {
    try {
      const data = await this.repo_history.find({
        order: { created_at: 'ASC' },
        where: {
          ticket: {
            id: id,
          },
        },
        relations: ['progres', 'ticket'],
      });
      return data;
    } catch (error) {}
  }

  update(id: number, updateTicketingDto: UpdateTicketingDto) {
    return `This action updates a #${id} ticketing`;
  }

  async remove(id: string) {
    const berita = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return true;
  }

  async updateStatus(id: string, data: UpdateProgressTicketingDto) {
    const request = new TicketingEntity();
    const master = new MasterProgresTicketEntity();
    const history = new HistoryTicketingEntity();

    master.id = data.id_progres;
    try {
      const getData = await this.repository.findOne({
        where: { id: id },
        relations: ['progres'],
      });
      if (getData) {
        const mailOptions = {
          from: process.env.SMTP_USER,
          to: getData.email,
          subject: '[Update] ' + getData.subject,
        };
        history.progres = master;
        history.ticket = getData;
        history.ket = data.ket;
        await this.repo_history.insert(history);
        request.progres = master;
        request.ket = data.ket;

        await this.emailService.sendEmail(
          mailOptions,
          getData.name,
          getData.body,
          getData.subject,
          getData.number,
          '[Update] ' + getData.subject,
        );

        const result = this.repository.update(id, request);
      } else return false;

      return true;
    } catch (error) {
      console.log(error);

      return false;
    }
  }

  async readFileAsBase64(filePath: string): Promise<string> {
    const readFile = util.promisify(fs.readFile);
    try {
      const fileContent = await readFile(filePath);
      const base64Content = fileContent.toString('base64');
      return base64Content;
    } catch (error) {
      throw new Error(`Error reading file: ${error.message}`);
    }
  }
}
