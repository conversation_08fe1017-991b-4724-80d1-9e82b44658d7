import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseInterceptors,
  UploadedFiles,
  Request,
  Res,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { TicketingService } from './ticketing.service';
import {
  CreateTicketingDto,
  UpdateProgressTicketingDto,
} from './dto/create-ticketing.dto';
import { UpdateTicketingDto } from './dto/update-ticketing.dto';
import { EmailService } from 'src/utils/globalFunction/EmailService';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';
import { FilesInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { createWriteStream } from 'fs';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { StorageConfig } from 'src/config/storage.config';

@ApiTags('Modul Master Ticket')
@Controller('v1/ticketing')
export class TicketingController {
  constructor(
    private readonly ticketingService: TicketingService,
    private readonly emailService: EmailService,
    private readonly fileService: FileService,
  ) {}

  @Public()
  @Post()
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  async create(
    @Request() req,
    @Res() res,
    @Body() dataPost: CreateTicketingDto,
    @UploadedFiles() files,
  ) {
    var dataFileBase;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      dataFileBase = files.map((file, i) => {
        // Validate file size
        if (file.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            `File ${file.originalname} terlalu besar. Maksimal 10MB`,
          );
        }

        // Validate file extension
        const extension = file.originalname.substring(
          file.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException(
            `Tipe file ${file.originalname} tidak diizinkan`,
          );
        }

        // Generate secure filename
        const namaFile = StorageConfig.generateSecureFilename(
          file.originalname,
        );

        return {
          filename: namaFile,
          mime: file.mimetype,
          data: file.buffer,
        };
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: false,
          message: error.message,
        });
      }
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Error saat memproses file ticketing',
      });
    }

    const data = await this.ticketingService.create(dataPost, dataFileBase);

    if (data) {
      dataFileBase.map((file, i) => {
        const secureFilePath = StorageConfig.getSecureFilePath(file.filename);
        const ws = createWriteStream(secureFilePath);
        ws.write(file.data);
      });
      return res.status(HttpStatus.ACCEPTED).json({
        status: true,
        message: 'Sukses',
      });
    } else {
      return res.status(HttpStatus.ACCEPTED).json({
        success: false,
        message: 'Gagal mengirim email',
      });
    }
  }

  @Public()
  @Get()
  async findAll() {
    const data = await this.ticketingService.findAll();
    data.map((dd) => {
      dd['attachment'] = JSON.parse(dd.attachment);
    });
    return {
      success: true,
      message: 'Data Ticket',
      data: data,
    };
  }

  @Public()
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.ticketingService.findOne(id);

    if (data.attachment != '[]') {
      const dataJson = JSON.parse(data.attachment);
      data['attachment'] = JSON.parse(data.attachment);
      const promises = [];
      dataJson.map((data) => {
        const promise = this.ticketingService
          .readFileAsBase64('./uploads/ticketing/' + data.filename)
          .then((d) => {
            return d;
          });
        promises.push(promise);
      });

      await Promise.all(promises)
        .then((results) => {
          data['base64'] = results;
        })
        .catch((error) => {
          console.error('Error:', error);
        });
    }

    return {
      success: true,
      message: 'Data Ticket By ID',
      data: data,
    };
  }

  @Public()
  @Get('bynumber/:number')
  async findNumberOne(@Param('number') number: string) {
    try {
      const data = await this.ticketingService.findNumberOne(number);
      data['attachment2'] = JSON.parse(data.attachment);
      if (data.attachment != '[]') {
        const dataJson = JSON.parse(data.attachment);

        const promises = [];
        dataJson.map((data) => {
          const promise = this.ticketingService
            .readFileAsBase64('./uploads/ticketing/' + data.filename)
            .then((d) => {
              return d;
            });
          promises.push(promise);
        });

        await Promise.all(promises)
          .then((results) => {
            data['base64'] = results;
          })
          .catch((error) => {
            console.error('Error:', error);
          });
      }

      return {
        success: true,
        message: 'Data Ticket By ID',
        data: data,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error',
      };
    }
  }

  @Public()
  @Get('history/:id_ticket')
  async findHistoryOne(@Param('id_ticket') id_ticket: string) {
    try {
      const data = await this.ticketingService.findHistoryTicketOne(id_ticket);

      return {
        success: true,
        message: 'Data Ticket By ID',
        data: data,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error',
      };
    }
  }

  @Post(':id')
  update(
    @Param('id') id: string,
    @Body() updateTicketingDto: UpdateTicketingDto,
  ) {
    return this.ticketingService.update(+id, updateTicketingDto);
  }

  @Post('update/status/:id')
  async updateStatus(
    @Param('id') id: string,
    @Body() updateTicketingDto: UpdateProgressTicketingDto,
  ) {
    const data = await this.ticketingService.updateStatus(
      id,
      updateTicketingDto,
    );
    if (!data) {
      return {
        success: false,
        message: 'Data Ticket Gagal Di Perbaharui',
      };
    }
    return {
      success: true,
      message: 'Data Ticket Berhasil Di Perbaharui',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string) {
    await this.ticketingService.remove(id);
    return {
      success: true,
      message: 'Data Ticket Berhasil Di Hapus',
    };
  }
}
