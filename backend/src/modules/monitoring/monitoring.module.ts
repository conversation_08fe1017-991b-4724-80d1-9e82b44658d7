import { Modu<PERSON> } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { MonitoringEntity } from './entities/monitoring.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tahun } from '../bulan_tahun/entities/bulan_tahun.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MonitoringEntity, Tahun])],
  controllers: [MonitoringController],
  providers: [MonitoringService],
})
export class MonitoringModule {}
