import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,
} from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { CreateMonitoringDto } from './dto/create-monitoring.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { createWriteStream } from 'fs';
import { StorageConfig } from 'src/config/storage.config';

@Controller('v1/monitoring')
@ApiTags('Modul Monitoring')
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        jenis: { type: 'string' },
        tahun: { type: 'string' },
        bulan: { type: 'string' },
        nama: { type: 'string' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileFieldsInterceptor([{ name: 'foto' }, { name: 'file' }]))
  @Post()
  async create(@Body() dataMoni: CreateMonitoringDto, @UploadedFiles() files) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file']) {
        const pdfFile = files['file'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file monitoring');
    }

    const data = await this.monitoringService.create(
      dataMoni,
      namaFile,
      namaFile_pdf,
    );
    return {
      success: true,
      message: 'Data Monitoring Berhasil Di Tambah',
    };
  }

  @Get()
  async findAll() {
    const data = await this.monitoringService.findAll();
    try {
      // for (let filee of data) {
      //   filee['base64'] = await this.monitoringService.readFileAsBase64(
      //     './uploads/monitoring/' + filee.file,
      //   );
      // }
    } catch (error) {}

    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.monitoringService.findOne(id);
    try {
      // data['base64'] = await this.monitoringService.readFileAsBase64(
      //   './uploads/monitoring/' + data.file,
      // );
    } catch (error) {}
    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        jenis: { type: 'string' },
        tahun: { type: 'string' },
        bulan: { type: 'string' },
        nama: { type: 'string' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileFieldsInterceptor([{ name: 'foto' }, { name: 'file' }]))
  @Post(':id')
  async update(
    @Param('id') id: string,
    @Body() dataMoni: CreateMonitoringDto,
    @UploadedFiles() files,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file']) {
        const pdfFile = files['file'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file monitoring');
    }

    const data = await this.monitoringService.update(
      id,
      dataMoni,
      namaFile,
      namaFile_pdf,
    );

    return {
      success: true,
      message: 'Data Monitoring Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string) {
    const data = await this.monitoringService.remove(id);
    return {
      success: true,
      message: 'Data Monitoring Berhasil Di Hapus',
    };
  }
}
