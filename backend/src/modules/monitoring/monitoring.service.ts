import { Injectable } from '@nestjs/common';
import { CreateMonitoringDto } from './dto/create-monitoring.dto';
import * as fs from 'fs';
import * as util from 'util';
import { MonitoringEntity } from './entities/monitoring.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Tahun } from '../bulan_tahun/entities/bulan_tahun.entity';

@Injectable()
export class MonitoringService {
  constructor(
    @InjectRepository(MonitoringEntity)
    private repo: Repository<MonitoringEntity>,
    @InjectRepository(Tahun)
    private repo_thn: Repository<Tahun>,
  ) {}
  async create(data: CreateMonitoringDto, foto: any, file_pdf: any) {
    const request = new MonitoringEntity();
    const req_tahun = new Tahun();
    req_tahun.id = data.tahun;
    request.nama = data.nama;
    request.jenis = data.jenis;
    request.tahun = req_tahun;
    request.bulan = data.bulan;

    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file = file_pdf;
    }
    const result = await this.repo.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repo.find({
      order: { created_at: 'DESC' },
      relations: ['tahun'],
    });
    return data;
  }

  async findOne(id: string) {
    const data = await this.repo.findOne({
      where: { id: id },
      relations: ['tahun'],
    });
    return data;
  }

  async update(id, data: CreateMonitoringDto, foto: any, file_pdf: any) {
    const request = new MonitoringEntity();
    const req_tahun = new Tahun();
    req_tahun.id = data.tahun;
    request.nama = data.nama;
    request.jenis = data.jenis;
    request.tahun = req_tahun;
    request.bulan = data.bulan;
    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file = file_pdf;
    }
    const result = await this.repo.update(id, request);
    return result;
  }

  async remove(id: string) {
    const berita = await this.repo.findOne({ where: { id: id } });
    const data = await this.repo.delete(id);
    return `This`;
  }

  async readFileAsBase64(filePath: string): Promise<string> {
    const readFile = util.promisify(fs.readFile);
    try {
      const fileContent = await readFile(filePath);
      const base64Content = fileContent.toString('base64');
      return base64Content;
    } catch (error) {
      throw new Error(`Error reading file: ${error.message}`);
    }
  }
}
