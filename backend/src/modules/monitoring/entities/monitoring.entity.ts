import { Tahun } from 'src/modules/bulan_tahun/entities/bulan_tahun.entity';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'monitoring' })
export class MonitoringEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  file: string;

  @Column()
  foto: string;

  // @Column({
  //   type: 'text',
  //   nullable: true,
  // })
  // tahun: string;

  @ManyToOne(() => Tahun, (data) => data.id, {})
  @JoinColumn()
  tahun: Tahun;
  @Column({
    type: 'text',
    nullable: true,
  })
  bulan: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  nama: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  jenis: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
