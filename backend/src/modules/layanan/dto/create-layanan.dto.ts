import { ApiProperty } from '@nestjs/swagger';

export class Create<PERSON>ayananDto {
  @ApiProperty()
  judul: string;

  // @IsNotEmpty()
  @ApiProperty()
  kategori: string;

  @ApiProperty()
  isi: string;

  @ApiProperty()
  tgl_posting: Date;

  @ApiProperty()
  foto: string;
}

export class CreateHeldeskMailDto {
  @ApiProperty()
  title: string;

  @ApiProperty()
  group: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  subject: string;

  @ApiProperty()
  type: string;

  @ApiProperty()
  body: string;

  @ApiProperty()
  attachment: any[];
}
