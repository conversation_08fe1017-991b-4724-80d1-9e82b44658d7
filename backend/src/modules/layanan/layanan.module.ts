import { Modu<PERSON> } from '@nestjs/common';
import { LayananService } from './layanan.service';
import { LayananController } from './layanan.controller';
import { Layanan } from './entities/layanan.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';

import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';

@Module({
  imports: [TypeOrmModule.forFeature([Layanan]), HttpModule],
  controllers: [LayananController],
  providers: [LayananService, FileService],
  exports: [LayananService],
})
export class LayananModule {}
