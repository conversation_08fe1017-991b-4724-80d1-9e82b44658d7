import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateLayananDto,
} from './dto/create-layanan.dto';
import { UpdateLayananDto } from './dto/update-layanan.dto';
import { Layanan } from './entities/layanan.entity';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class LayananService {
  constructor(
    @InjectRepository(Layanan)
    private repository: Repository<Layanan>,

    private readonly httpService: HttpService,
  ) {}
  create(dataLayanan: CreateLayananDto, foto: any, id_user: string) {
    const request = new Layanan();

    request.judul = dataLayanan.judul;

    request.kategori = dataLayanan.kategori;

    request.slug = dataLayanan.judul.toLowerCase().split(' ').join('-');

    request.isi = dataLayanan.isi;

    if (foto) {
      request.foto = foto;
    }
    request.tgl_posting = dataLayanan.tgl_posting;
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({ order: { tgl_posting: 'DESC' } });
    return data;
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  update(id, dataLayanan: UpdateLayananDto, foto: any, id_user: string) {
    const request = new Layanan();
    request.judul = dataLayanan.judul;
    request.kategori = dataLayanan.kategori;
    request.slug = dataLayanan.judul.toLowerCase().split(' ').join('-');

    request.isi = dataLayanan.isi;

    if (foto) {
      request.foto = foto;
    }
    request.tgl_posting = dataLayanan.tgl_posting;

    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id, id_user: string) {
    const berita = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return `This action removes a #${id} artikel`;
  }
}
