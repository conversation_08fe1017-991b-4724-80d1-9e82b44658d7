import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Request,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,
} from '@nestjs/common';
import { KeamananService } from './keamanan.service';
import {
  CreatePanduanKeamananDto,
  CreatePeringatanKeamananDto,
} from './dto/create-keamanan.dto';
import * as fs from 'fs';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { createWriteStream } from 'fs';
import { Public } from 'src/common/decorators';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { StorageConfig } from 'src/config/storage.config';

@Controller('v1/keamanan')
@ApiTags('Modul Keamanan')
export class KeamananController {
  constructor(
    private readonly keamananService: KeamananService,
    private readonly fileService: FileService,
  ) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama: { type: 'string' },
        desc: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileFieldsInterceptor([{ name: 'foto' }, { name: 'file' }]))
  @Post('panduan')
  async createPeringatan(
    @Body() dataPanduan: CreatePanduanKeamananDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file']) {
        const pdfFile = files['file'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file keamanan');
    }

    const data = await this.keamananService.createPanduan(
      dataPanduan,
      namaFile,
      namaFile_pdf,
    );
    return {
      success: true,
      message: 'Data Panduan Keamanan Berhasil Di Tambah',
    };
  }

  @Get('panduan')
  async findAllPeringatan() {
    const data = await this.keamananService.findAllPanduan();
    try {
      // for (let filee of data) {
      //   filee['base64'] = await this.keamananService.readFileAsBase64(
      //     './uploads/keamanan/' + filee.file,
      //   );
      // }
    } catch (error) {}

    return {
      success: true,
      message: 'Data Panduan Keamanan',
      data: data,
    };
  }

  @Get('panduan/:id')
  async findOnePeringatan(@Param('id') id: string) {
    const data = await this.keamananService.findOnePanduan(id);
    try {
      data['base64'] = await this.keamananService.readFileAsBase64(
        './uploads/keamanan/' + data.file,
      );
    } catch (error) {}
    return {
      success: true,
      message: 'Data Panduan Keamanan',
      data: data,
    };
  }

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama: { type: 'string' },
        desc: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileFieldsInterceptor([{ name: 'foto' }, { name: 'file' }]))
  @Post('panduan/:id')
  async updatePeringatan(
    @Param('id') id: string,
    @Body() dataPanduan: CreatePanduanKeamananDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file']) {
        const pdfFile = files['file'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file keamanan');
    }

    const data = await this.keamananService.updatePanduan(
      id,
      dataPanduan,
      namaFile,
      namaFile_pdf,
    );

    return {
      success: true,
      message: 'Data Panduan Keamanan Berhasil Di Update',
    };
  }

  @Post('panduan/delete/:id')
  async removePeringatan(@Param('id') id: string) {
    const data = await this.keamananService.removePanduan(id);
    return {
      success: true,
      message: 'Data Panduan Keamanan Berhasil Di Hapus',
    };
  }

  //peringatan
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        isi: { type: 'text' },
        link_sumber: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([{ name: 'foto' }, { name: 'file_pdf' }]),
  )
  @Post('peringatan')
  async createPanduan(
    @Body() dataP: CreatePeringatanKeamananDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file_pdf']) {
        const pdfFile = files['file_pdf'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file keamanan');
    }

    const data = await this.keamananService.createPeringatan(
      dataP,
      namaFile,
      namaFile_pdf,
    );
    return {
      success: true,
      message: 'Data Peringatan Keamanan Berhasil Di Tambah',
    };
  }

  @Get('peringatan')
  async findAllPanduan() {
    const data = await this.keamananService.findAllPeringatan();
    try {
      // for (let filee of data) {
      //   filee['base64'] = await this.keamananService.readFileAsBase64(
      //     './uploads/keamanan/' + filee.file_pdf,
      //   );
      // }
    } catch (error) {}

    return {
      success: true,
      message: 'Data Peringatan Keamanan',
      data: data,
    };
  }

  @Get('peringatan/:id')
  async findOnePanduan(@Param('id') id: string) {
    const data = await this.keamananService.findOnePeringatan(id);
    try {
      data['base64'] = await this.keamananService.readFileAsBase64(
        './uploads/keamanan/' + data.file_pdf,
      );
    } catch (error) {}
    return {
      success: true,
      message: 'Data Peringatan Keamanan',
      data: data,
    };
  }

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        isi: { type: 'text' },
        link_sumber: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([{ name: 'foto' }, { name: 'file_pdf' }]),
  )
  @Public()
  @Post('peringatan/:id')
  async updatePanduan(
    @Param('id') id: string,
    @Body() dataP: CreatePeringatanKeamananDto,
    @UploadedFiles() files,
  ) {
    let namaFile, namaFile_pdf;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files['foto']) {
        const fotoFile = files['foto'][0];

        // Validate file size
        if (fotoFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = fotoFile.originalname.substring(
          fotoFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(fotoFile.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(fotoFile.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(fotoFile.buffer);
      }

      if (files['file_pdf']) {
        const pdfFile = files['file_pdf'][0];

        // Validate file size
        if (pdfFile.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = pdfFile.originalname.substring(
          pdfFile.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file PDF tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (pdfFile.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          pdfFile.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = fs.createWriteStream(secureFilePath);
        ws.write(pdfFile.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file keamanan');
    }

    const data = await this.keamananService.updatePeringatan(
      id,
      dataP,
      namaFile,
      namaFile_pdf,
    );
    return {
      success: true,
      message: 'Data Peringatan Keamanan Berhasil Di Perbaharui',
    };
  }

  @Post('peringatan/delete/:id')
  async removePanduan(@Param('id') id: string) {
    const data = await this.keamananService.removePeringatan(id);
    return {
      success: true,
      message: 'Data Peringatan Keamanan Berhasil Di Hapus',
    };
  }
}
