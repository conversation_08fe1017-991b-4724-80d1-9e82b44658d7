import { Modu<PERSON> } from '@nestjs/common';
import { KeamananService } from './keamanan.service';
import { KeamananController } from './keamanan.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PanduanKeamanan } from './entities/keamanan_panduan.entity';
import { PeringatanKeamanan } from './entities/keamanan_peringatan.entity';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';

@Module({
  imports: [TypeOrmModule.forFeature([PanduanKeamanan, PeringatanKeamanan])],
  controllers: [KeamananController],
  providers: [KeamananService, FileService],
})
export class KeamananModule {}
