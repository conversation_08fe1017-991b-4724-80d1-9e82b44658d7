import { Injectable } from '@nestjs/common';
import {
  CreatePeringatanKeamananDto,
  CreatePanduanKeamananDto,
} from './dto/create-keamanan.dto';
import * as fs from 'fs';
import * as util from 'util';
import { PanduanKeamanan } from './entities/keamanan_panduan.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PeringatanKeamanan } from './entities/keamanan_peringatan.entity';

@Injectable()
export class KeamananService {
  constructor(
    @InjectRepository(PanduanKeamanan)
    private panduanRepo: Repository<PanduanKeamanan>,
    @InjectRepository(PeringatanKeamanan)
    private peringatanRepo: Repository<PeringatanKeamanan>,
  ) {}
  //panduan
  async createPanduan(
    data: CreatePanduanKeamananDto,
    foto: any,
    file_pdf: any,
  ) {
    const request = new PanduanKeamanan();
    request.nama = data.nama;

    request.desc = data.desc;
    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file = file_pdf;
    }
    const result = await this.panduanRepo.insert(request);
    return result;
  }

  async findAllPanduan() {
    const data = await this.panduanRepo.find({ order: { created_at: 'DESC' } });
    return data;
  }

  async findOnePanduan(id: string) {
    const data = await this.panduanRepo.findOne({ where: { id: id } });
    return data;
  }

  async updatePanduan(
    id,
    data: CreatePanduanKeamananDto,
    foto: any,
    file_pdf: any,
  ) {
    const request = new PanduanKeamanan();
    request.nama = data.nama;

    request.desc = data.desc;
    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file = file_pdf;
    }
    const result = await this.panduanRepo.update(id, request);
    return result;
  }

  async removePanduan(id: string) {
    try {
      const berita = await this.panduanRepo.findOne({ where: { id: id } });
      const data = await this.panduanRepo.delete(id);
    } catch (error) {}
    return `This`;
  }

  //peringatan
  async createPeringatan(
    data: CreatePeringatanKeamananDto,
    foto: any,
    file_pdf: any,
  ) {
    const request = new PeringatanKeamanan();
    request.judul = data.judul;
    request.link_sumber = data.link_sumber;

    request.isi = data.isi;
    if (foto) {
      request.foto = foto;
    }
    if (file_pdf) {
      request.file_pdf = file_pdf;
    }
    request.tgl_posting = data.tgl_posting;
    const result = await this.peringatanRepo.insert(request);
    return result;
  }

  async findAllPeringatan() {
    const data = await this.peringatanRepo.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findOnePeringatan(id: string) {
    const data = await this.peringatanRepo.findOne({ where: { id: id } });
    return data;
  }

  async updatePeringatan(
    id,
    data: CreatePeringatanKeamananDto,
    foto: any,
    file_pdf: any,
  ) {
    try {
      const request = new PeringatanKeamanan();
      request.judul = data.judul;
      request.isi = data.isi;
      if (foto) {
        request.foto = foto;
      }
      if (file_pdf) {
        request.file_pdf = file_pdf;
      }

      request.tgl_posting = data.tgl_posting;
      request.link_sumber = data.link_sumber;
      const result = await this.peringatanRepo.update(id, request);
      return result;
    } catch (error) {
      console.log('error,', error);
    }
  }

  async removePeringatan(id: string) {
    try {
      const berita = await this.peringatanRepo.findOne({ where: { id: id } });
      const data = await this.peringatanRepo.delete(id);
    } catch (error) {}
    return `This`;
  }

  async readFileAsBase64(filePath: string): Promise<string> {
    const readFile = util.promisify(fs.readFile);
    try {
      const fileContent = await readFile(filePath);
      const base64Content = fileContent.toString('base64');
      return base64Content;
    } catch (error) {
      throw new Error(`Error reading file: ${error.message}`);
    }
  }
}
