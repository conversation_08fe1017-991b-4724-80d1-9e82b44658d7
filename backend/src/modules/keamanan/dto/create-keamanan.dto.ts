import { ApiProperty } from '@nestjs/swagger';

export class CreatePeringatanKeamananDto {
  @ApiProperty()
  file_pdf: string;

  @ApiProperty()
  foto: string;

  @ApiProperty()
  isi: string;

  @ApiProperty()
  tgl_posting: Date;

  @ApiProperty()
  judul: string;
  @ApiProperty()
  link_sumber: string;
}

export class CreatePanduanKeamananDto {
  @ApiProperty()
  file: string;

  @ApiProperty()
  foto: string;

  @ApiProperty()
  desc: string;
  // @IsNotEmpty()

  @ApiProperty()
  nama: string;
}
