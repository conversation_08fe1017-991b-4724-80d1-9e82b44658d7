import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'peringatan_keamanan' })
export class PeringatanKeamanan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  file_pdf: string;

  @Column()
  foto: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  isi: string;
  @Column({
    type: 'text',
    nullable: true,
  })
  link_sumber: string;

  @Column({
    // type: 'datetime'
  })
  tgl_posting: Date;

  @Column()
  judul: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
