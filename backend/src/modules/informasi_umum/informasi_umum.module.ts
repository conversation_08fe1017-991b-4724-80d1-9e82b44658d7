import { Module } from '@nestjs/common';
import { InformasiUmumService } from './informasi_umum.service';
import { InformasiUmumController } from './informasi_umum.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InformasiUmum } from './entities/informasi_umum.entity';

@Module({
  imports: [TypeOrmModule.forFeature([InformasiUmum])],
  controllers: [InformasiUmumController],
  providers: [InformasiUmumService],
})
export class InformasiUmumModule {}
