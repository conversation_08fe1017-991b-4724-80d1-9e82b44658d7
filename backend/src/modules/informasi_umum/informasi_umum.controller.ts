import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Request,
  UseInterceptors,
  UploadedFiles,
  BadRequestException,
} from '@nestjs/common';
import { InformasiUmumService } from './informasi_umum.service';
import { CreateInformasiUmumDto } from './dto/create-informasi_umum.dto';
import { UpdateInformasiUmumDto } from './dto/update-informasi_umum.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { createWriteStream } from 'fs';
import { StorageConfig } from 'src/config/storage.config';

@Controller('v1/informasi-umum')
@ApiTags('Modul Informasi-Umum')
export class InformasiUmumController {
  constructor(private readonly informasiUmumService: InformasiUmumService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        deskripsi: { type: 'string' },
        visi: { type: 'text' },
        misi: { type: 'text' },
        logo: {
          type: 'string',
          format: 'binary',
        },
        gambar_header: {
          type: 'string',
          format: 'binary',
        },
        tentang: {
          type: 'string',
          format: 'binary',
        },
        foto_visi: {
          type: 'string',
          format: 'binary',
        },
        foto_misi: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo' },
      { name: 'tentang' },
      { name: 'foto_visi' },
      { name: 'foto_misi' },
      { name: 'gambar_header' },
    ]),
  )
  async create(
    @Body() data: CreateInformasiUmumDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaLogo = '',
      namaFotoTentang = '',
      namaFotoVisi = '',
      namaFotoMisi = '',
      namaGambarHeader = '';

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      // Helper function to validate and process image files
      const processImageFile = (
        file: Express.Multer.File,
        fieldName: string,
      ) => {
        // Validate file size
        if (file.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            `File ${fieldName} terlalu besar. Maksimal 10MB`,
          );
        }

        // Validate file extension
        const extension = file.originalname.substring(
          file.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException(
            `Tipe file ${fieldName} tidak diizinkan`,
          );
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(file.mimetype)) {
          throw new BadRequestException(`Tipe file ${fieldName} tidak valid`);
        }

        // Generate secure filename
        const secureFilename = StorageConfig.generateSecureFilename(
          file.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(secureFilename);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file.buffer);

        return secureFilename;
      };

      if (files['logo']) {
        namaLogo = processImageFile(files['logo'][0], 'logo');
      }

      if (files['gambar_header']) {
        namaGambarHeader = processImageFile(
          files['gambar_header'][0],
          'gambar header',
        );
      }

      if (files['tentang']) {
        namaFotoTentang = processImageFile(files['tentang'][0], 'foto tentang');
      }

      if (files['foto_visi']) {
        namaFotoVisi = processImageFile(files['foto_visi'][0], 'foto visi');
      }

      if (files['foto_misi']) {
        namaFotoMisi = processImageFile(files['foto_misi'][0], 'foto misi');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file informasi umum');
    }

    const dataRet = await this.informasiUmumService.create(
      data,
      namaLogo,
      namaFotoTentang,
      namaFotoVisi,
      namaFotoMisi,
      namaGambarHeader,
    );
    return {
      success: true,
      message: 'Data Informasi Umum Berhasil Di Tambah',
    };
  }

  @Get()
  async findAll() {
    const data = await this.informasiUmumService.findAll();
    return {
      success: true,
      data: data,
      message: 'Data Informasi Umum',
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.informasiUmumService.findOne(id);
    return {
      success: true,
      data: data,
      message: 'Data Informasi Umum By ID',
    };
  }

  @Post(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        deskripsi: { type: 'string' },
        visi: { type: 'text' },
        misi: { type: 'text' },
        logo: {
          type: 'string',
          format: 'binary',
        },
        tentang: {
          type: 'string',
          format: 'binary',
        },
        foto_visi: {
          type: 'string',
          format: 'binary',
        },
        gambar_header: {
          type: 'string',
          format: 'binary',
        },
        foto_misi: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo' },
      { name: 'tentang' },
      { name: 'foto_visi' },
      { name: 'foto_misi' },
      { name: 'gambar_header' },
    ]),
  )
  async update(
    @Param('id') id: string,
    @Body() data: UpdateInformasiUmumDto,
    @UploadedFiles() files,
    @Request() req,
  ) {
    let namaLogo, namaFotoTentang, namaFotoVisi, namaFotoMisi, namaGambarHeader;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      // Helper function to validate and process image files
      const processImageFile = (
        file: Express.Multer.File,
        fieldName: string,
      ) => {
        // Validate file size
        if (file.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            `File ${fieldName} terlalu besar. Maksimal 10MB`,
          );
        }

        // Validate file extension
        const extension = file.originalname.substring(
          file.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException(
            `Tipe file ${fieldName} tidak diizinkan`,
          );
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(file.mimetype)) {
          throw new BadRequestException(`Tipe file ${fieldName} tidak valid`);
        }

        // Generate secure filename
        const secureFilename = StorageConfig.generateSecureFilename(
          file.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(secureFilename);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file.buffer);

        return secureFilename;
      };

      if (files['logo'] && files['logo'][0]) {
        namaLogo = processImageFile(files['logo'][0], 'logo');
      }

      if (files['gambar_header']) {
        namaGambarHeader = processImageFile(
          files['gambar_header'][0],
          'gambar header',
        );
      }

      if (files['tentang'] && files['tentang'][0]) {
        namaFotoTentang = processImageFile(files['tentang'][0], 'foto tentang');
      }

      if (files['foto_visi'] && files['foto_visi'][0]) {
        namaFotoVisi = processImageFile(files['foto_visi'][0], 'foto visi');
      }

      if (files['foto_misi'] && files['foto_misi'][0]) {
        namaFotoMisi = processImageFile(files['foto_misi'][0], 'foto misi');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file informasi umum');
    }

    const dataRet = await this.informasiUmumService.update(
      id,
      data,
      namaLogo,
      namaFotoTentang,
      namaFotoVisi,
      namaFotoMisi,
      namaGambarHeader,
    );
    return {
      success: true,
      message: 'Data Informasi Umum Berhasil Diupdate',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string) {
    await this.informasiUmumService.remove(id);
    return {
      success: true,
      message: 'Data Informasi Umum Berhasil Dihapus',
    };
  }
}
