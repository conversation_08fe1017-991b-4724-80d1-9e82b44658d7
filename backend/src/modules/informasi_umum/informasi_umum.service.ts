import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateInformasiUmumDto } from './dto/create-informasi_umum.dto';
import { UpdateInformasiUmumDto } from './dto/update-informasi_umum.dto';
import { InformasiUmum } from './entities/informasi_umum.entity';

@Injectable()
export class InformasiUmumService {
  constructor(
    @InjectRepository(InformasiUmum)
    private repository: Repository<InformasiUmum>,
  ) {}
  async create(
    createInformasiUmumDto: CreateInformasiUmumDto,
    fileLogo: any,
    fileFotoTentang: any,
    fileMisi: any,
    fileVisi: any,
    fileGambarHeader: any,
  ) {
    const cekInfo = await this.repository.find({
      order: { created_at: 'DESC' },
    });
    let result;
    const request = new InformasiUmum();
    request.deskripsi = createInformasiUmumDto.deskripsi;
    request.visi = createInformasiUmumDto.visi;
    request.misi = createInformasiUmumDto.misi;
    if (fileLogo) {
      request.logo = fileLogo;
    }
    if (fileFotoTentang) {
      request.tentang = fileFotoTentang;
    }
    if (fileGambarHeader) {
      request.gambar_header = fileGambarHeader;
    }
    if (fileVisi) {
      request.foto_visi = fileVisi;
    }
    if (fileMisi) {
      request.foto_misi = fileMisi;
    }

    if (cekInfo[0] == null) {
      await this.repository.insert(request);
    } else {
      await this.repository.update(cekInfo[0].id, request);
    }

    return '';
  }

  async findAll() {
    const data = await this.repository.find({ order: { created_at: 'DESC' } });
    // console.log(data);
    return data[0];
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  async update(
    id: string,
    createInformasiUmumDto: UpdateInformasiUmumDto,
    fileLogo: any,
    fileFotoTentang: any,
    fileMisi: any,
    fileVisi: any,
    fileGambarHeader: any,
  ) {
    const request = new InformasiUmum();
    request.deskripsi = createInformasiUmumDto.deskripsi;
    request.visi = createInformasiUmumDto.visi;
    request.misi = createInformasiUmumDto.misi;
    if (fileLogo) {
      request.logo = fileLogo;
    }
    if (fileFotoTentang) {
      request.tentang = fileFotoTentang;
    }
    if (fileVisi) {
      request.foto_visi = fileVisi;
    }
    if (fileGambarHeader) {
      request.gambar_header = fileGambarHeader;
    }
    if (fileMisi) {
      request.foto_misi = fileMisi;
    }
    const result = await this.repository.update(id, request);
    return result;
  }

  async remove(id) {
    const iUmum = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return `This action removes a #${id} artikel`;
  }
}
