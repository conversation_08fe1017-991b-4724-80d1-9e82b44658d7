import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'informasi_umum' })
export class InformasiUmum {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  tentang: string;

  @Column()
  deskripsi: string;

  @Column({ nullable: true })
  foto_visi: string;

  @Column()
  visi: string;

  @Column({ nullable: true })
  foto_misi: string;
  @Column({ nullable: true })
  gambar_header: string;

  @Column()
  misi: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
