import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CreateMenuDto } from './dto/create-menu.dto';
import { UpdateMenuDto } from './dto/update-menu.dto';
import { Menu } from './entities/menu.entity';


@Injectable()
export class MenuService {
  constructor(
    @InjectRepository(Menu)
    private repository: Repository<Menu>, // @InjectRepository(HakAkses) // private repHakAkses: Repository<HakAkses>,
  ) {}

  create(createMenuDto: CreateMenuDto, id_user: string) {
    const request = new Menu();
    request.id_menu = createMenuDto.id_menu;
    request.jenis_menu = createMenuDto.jenis_menu;
    request.nama_menu = createMenuDto.nama_menu;
    request.link = createMenuDto.link;
    request.posisi = createMenuDto.posisi;
    request.icon = createMenuDto.icon;
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.query(`
      select 
        menu.id,
        menu.jenis_menu,
        menu.nama_menu,
        menu.link,
        menu.icon,
        menu.posisi,
        sub_menu.id as id_menu_induk,
        sub_menu.nama_menu as menu_induk
      from menu
      left join menu as sub_menu on menu.id_menu = sub_menu.id
    `);

    // console.log(Res.)
    return data;
  }

  async findOne(id: string) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  update(id: string, updateMenuDto: UpdateMenuDto, id_user: string) {
    const request = new Menu();
    request.id_menu = updateMenuDto.id_menu;
    request.jenis_menu = updateMenuDto.jenis_menu;
    request.nama_menu = updateMenuDto.nama_menu;
    request.link = updateMenuDto.link;
    request.posisi = updateMenuDto.posisi;
    request.icon = updateMenuDto.icon;
    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id: string, id_user: string) {
    const menu = await this.repository.findOne({ where: { id: id } });
    const data = await this.repository.delete(id);
    return data;
  }

  async sidebar(id_user: string) {
    // Use parameterized query to prevent SQL injection
    const data = await this.repository.query(`
    SELECT
      hak_akses.id_user,
      users.nama,
      hak_akses.id_menu,
      menu.nama_menu,
      menu.jenis_menu,
      menu.link,
      menu.posisi
    FROM
      hak_akses
      LEFT JOIN users ON hak_akses.id_user = users.ID
      LEFT JOIN menu ON hak_akses.id_menu = menu.ID
    WHERE
      users.ID = $1
    `, [id_user]);

    return data;
  }
}
