import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Request,
} from '@nestjs/common';
import { MenuService } from './menu.service';
import { CreateMenuDto } from './dto/create-menu.dto';
import { UpdateMenuDto } from './dto/update-menu.dto';
import { ApiTags } from '@nestjs/swagger';

import { Public } from 'src/common/decorators';

@ApiTags('Modul Menu')
@Controller('v1/menu')
export class MenuController {
  constructor(private readonly menuService: MenuService) {}

  @Public()
  @Post()
  create(@Body() createMenuDto: CreateMenuDto, @Request() req) {
    const data = this.menuService.create(createMenuDto, req.headers.id_user);
    return {
      success: true,
      message: 'Data Menu Berhasil Di Tambah',
    };
  }

  //@UseInterceptors(CacheInterceptor)
  @Get()
  async findAll() {
    const data = await this.menuService.findAll();
    return {
      succes: true,
      message: 'Data Menu',
      data: data,
    };
  }

  //@UseInterceptors(CacheInterceptor)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.menuService.findOne(id);
    return {
      succes: true,
      message: 'Data Menu',
      data: data,
    };
  }

  @Post(':id')
  update(
    @Param('id') id: string,
    @Body() updateMenuDto: UpdateMenuDto,
    @Request() req,
  ) {
    const data = this.menuService.update(
      id,
      updateMenuDto,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Menu Berhasil Di Ubah',
    };
  }

  @Post('delete/:id')
  remove(@Param('id') id: string, @Request() req) {
    const data = this.menuService.remove(id, req.headers.id_user);
    return {
      success: true,
      message: 'Data Menu Berhasil Di Hapus',
    };
  }

  //@UseInterceptors(CacheInterceptor)
  @Get('sidebar/:id_user')
  async findSidebar(@Param('id_user') id: string) {
    const data = await this.menuService.sidebar(id);

    return {
      succes: true,
      message: 'Data Menu Sidebar',
      data: data,
    };
  }
}
