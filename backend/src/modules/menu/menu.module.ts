import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MenuService } from './menu.service';
import { MenuController } from './menu.controller';
import { Menu } from './entities/menu.entity';
import { TypeOrmModule } from '@nestjs/typeorm';


@Module({
  imports: [TypeOrmModule.forFeature([Menu])],
  exports: [TypeOrmModule, MenuService],
  controllers: [MenuController],
  providers: [MenuService],
})
export class MenuModule {}
