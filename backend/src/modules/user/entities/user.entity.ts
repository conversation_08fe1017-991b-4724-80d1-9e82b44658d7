import { MasterRole } from 'src/modules/master_role/entities/master_role.entity';
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
  JoinColumn,
  ManyToOne,
} from 'typeorm';

@Entity({ name: 'users' })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  nama: string;

  @Column({
    unique: true,
  })
  username: string;

  @Column()
  password: string;

  @Column({
    unique: true,
  })
  email: string;

  @ManyToOne(() => MasterRole, (data) => data.id, {
    nullable: true,
  })
  @JoinColumn()
  role: MasterRole;

  @Column({
    nullable: true,
  })
  foto: string;

  @Column({
    nullable: true,
  })
  currentHashedRefreshToken?: string;

  @Column({
    nullable: true,
  })
  forgetPasswordToken?: string;

  @Column({
    default: 1,
    comment: '1 aktif, 0 tidak aktif',
  })
  status: number;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
