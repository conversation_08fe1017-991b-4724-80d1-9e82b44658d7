import {
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import * as bcrypt from 'bcrypt';
import { HttpService } from '@nestjs/axios';

import { encryptData } from '../../utils/encrypt';
import { UserActivity } from './entities/user-activity.entity';
import { MasterRole } from '../master_role/entities/master_role.entity';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserActivity)
    private readonly userAc: Repository<UserActivity>,
    @InjectRepository(MasterRole)
    private readonly masterRoleRepo: Repository<MasterRole>,

    private readonly httpService: HttpService,
  ) {}

  async create(createUserDto: any, namaFile: any) {
    const menu = createUserDto.id_menu;

    createUserDto.password = await this.encryptPassword(createUserDto.password);
    if (namaFile) {
      createUserDto.foto = namaFile;
    }
    const user = this.userRepository.create(createUserDto);
    const insertResult = await this.userRepository.insert(user);
    // console.log(insertResult);

    // const newUser = await this.userRepository.findOne(
    //   insertResult.identifiers[0].id,
    // );
    return insertResult;
  }

  async saveActivity(
    ip: string,
    dev: string,
    action: string,
    route: string,
    username: string,
  ) {
    try {
      var modelUserAc = new UserActivity();
      modelUserAc.action = action;
      modelUserAc.ip = ip;
      modelUserAc.device = dev;
      modelUserAc.route = route;
      modelUserAc.username = username;
      const result = await this.userAc.insert(modelUserAc);
    } catch (error) {}
  }

  async updateUser(id: string, createUserDto: any, namaFile: any) {
    let userGet = await this.userRepository.findOne({ where: { id: id } });
    if (!userGet) {
      throw new HttpException('User tidak dikenali', HttpStatus.BAD_REQUEST);
    }
    //
    try {
      if (
        createUserDto.email != userGet.email &&
        createUserDto.email != userGet.email
      ) {
        const datadd = await this.userRepository.findOne({
          where: { email: createUserDto.email },
        });
        if (datadd != null)
          return { status: false, message: 'Email Telah Digunakan' };
      }
    } catch (error) {}
    if (namaFile) {
      createUserDto.foto = namaFile;
    }
    const role = new MasterRole();
    role.id = createUserDto.roleId;
    userGet.role = role;
    userGet.nama = createUserDto.nama;
    userGet.username = createUserDto.username;
    userGet.foto = createUserDto.foto;
    userGet.foto = createUserDto.foto;
    if (createUserDto.email != userGet.email)
      userGet.email = createUserDto.email;

    const result = await this.userRepository.update(id, userGet);

    return { success: true, message: 'Data User Berhasil Di Update' };
  }

  async encryptPassword(password): Promise<string> {
    const HASH_SALT = await bcrypt.genSalt(10);
    const hashPassword = await bcrypt.hash(password, HASH_SALT);
    return hashPassword;
    const encryptedMessage = encryptData(hashPassword);
    return encryptedMessage.toString();
  }

  async findAll() {
    const data = await this.userRepository.find({ relations: ['role'] });
    const modifiedData = data.map((user) => {
      const {
        password,
        currentHashedRefreshToken,
        forgetPasswordToken,
        ...userWithoutSensitive
      } = user;
      return userWithoutSensitive;
    });

    return modifiedData;
  }

  async findAllUserAc() {
    const data = await this.userAc.find();
    return data;
  }

  async findById(id: string) {
    const data = await this.userRepository.findOne({
      where: { id },
      relations: ['role'],
    });
    // const akses = await this.repositoryHakAkses.find({ where: { id_user : id } });
    return data;
  }

  async findByIdUser(id: string) {
    try {
      const data = await this.userRepository.findOne({ where: { id: id } });
      return data;
    } catch (error) {
      return 'Error';
    }
    // const akses = await this.repositoryHakAkses.find({
    //   where: { id_user: id },
    // });
  }

  async checkEmail(email: string) {
    try {
      const data = await this.userRepository.findOne({
        where: { email: email },
      });
      if (data == null)
        return { status: true, message: 'Email Belum Digunakan' };

      return { status: false, message: 'Email Telah Digunakan' };
    } catch (error) {
      return { status: true, message: 'Email Belum Digunakan' };
    }
  }

  async findOne(user: string) {
    const data = await this.userRepository.findOne({
      where: { username: user },
      relations: ['role'],
    });
    return data;
  }

  async find(param: {}) {
    if (!param) {
      throw new HttpException('Parameter harus diisi', HttpStatus.BAD_REQUEST);
    }
    const user = await this.userRepository.findOne({ where: { ...param } });
    return user;
  }

  async update(id: string, updateUserDto: any, namaFile: any) {
    let hakAkses;
    const menu = updateUserDto.id_menu;

    const user = new User();
    user.nama = updateUserDto.nama;

    if (namaFile) {
      user.foto = updateUserDto.namaFile;
    }

    return 'result';
  }

  async updateToken(id: string, updateUserDto: any) {
    try {
      const result = await this.userRepository.update(id, {
        ...updateUserDto,
      });
      return result;
    } catch (error) {
      console.log('log from update token=>', error);
    }
  }

  async getUserIfRefreshTokenMatches(refreshToken: string, id: string) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new HttpException(
        'Refresh token : user tidak ditemukan',
        HttpStatus.NOT_FOUND,
      );
    }
    const isRefreshTokenMatching = await bcrypt.compare(
      refreshToken,
      user.currentHashedRefreshToken,
    );
    const { password, ...result } = user;
    if (isRefreshTokenMatching) {
      return {
        user: result,
        refreshToken,
      };
    }
    return;
  }

  async removeRefreshToken(id: string) {
    return this.userRepository.update(id, {
      currentHashedRefreshToken: null,
    });
  }

  async remove(id: string) {
    return await this.userRepository.delete(id);
  }
  // async newHistoryActivityuser(
  //   id_user: string,
  //   aksi: string,
  //   menu: string,
  //   detail: string,
  // ) {
  //   const log = new LogHistory();
  //   log.id_user = id_user;
  //   log.aksi = aksi;
  //   log.menu = menu;
  //   log.detail = detail;

  //   this.repositoryLog.insert(log);
  // }
}
