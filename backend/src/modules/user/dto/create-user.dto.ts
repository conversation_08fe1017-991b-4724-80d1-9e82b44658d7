import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsEmail, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateUserDto {
  @ApiProperty()
  @IsNotEmpty()
  nama: string;

  @ApiProperty()
  @IsNotEmpty()
  username: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    // example: 'Super Admin / Admin'
  })
  role: string;

  @ApiProperty({
    isArray: true,
  })
  @IsArray()
  id_menu: string[];

  @ApiProperty()
  foto: string;

  @ApiProperty()
  master: string;

  @IsOptional()
  currentHashedRefreshToken?: string;

  @IsOptional()
  forgetPasswordToken?: string;

  @IsOptional()
  status?: number;
}
