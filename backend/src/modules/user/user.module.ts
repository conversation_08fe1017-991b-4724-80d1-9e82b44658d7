import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';

import { HttpModule } from '@nestjs/axios';
import { UserActivity } from './entities/user-activity.entity';
import { MasterRole } from '../master_role/entities/master_role.entity';

@Module({
  controllers: [UserController],
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([User, UserActivity, MasterRole]),
  ],
  providers: [UserService],
  exports: [TypeOrmModule, UserService],
})
export class UserModule {}
