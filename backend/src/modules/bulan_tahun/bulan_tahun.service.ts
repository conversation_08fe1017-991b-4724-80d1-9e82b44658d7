import { Injectable } from '@nestjs/common';
import {
  CreateBulanTahunDto,
  CreateTahunTahunDto,
} from './dto/create-bulan_tahun.dto';
import { UpdateBulanTahunDto } from './dto/update-bulan_tahun.dto';
import { <PERSON><PERSON><PERSON>, <PERSON>hun } from './entities/bulan_tahun.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class BulanTahunService {
  constructor(
    @InjectRepository(Bulan)
    private r_bulan: Repository<Bulan>,
    @InjectRepository(Tahun)
    private r_tahun: Repository<Tahun>,
  ) {}
  create(createBulanTahunDto: CreateBulanTahunDto) {
    const request = new Bulan();
    request.nama_bulan = createBulanTahunDto.nama_bulan;
    const result = this.r_bulan.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.r_bulan.find({ order: { created_at: 'DESC' } });
    return data;
  }

  async findOne(id: string) {
    const data = await this.r_bulan.findOne({ where: { id: id } });
    return data;
  }

  update(id: string, updateBulanTahunDto: UpdateBulanTahunDto) {
    const request = new Bulan();
    request.nama_bulan = updateBulanTahunDto.nama_bulan;
    const result = this.r_bulan.update(id, request);
    return result;
  }

  async remove(id: string) {
    const bb = await this.r_bulan.findOne({ where: { id: id } });
    const data = await this.r_bulan.delete(id);
    return `This action removes a #${id} bulan`;
  }

  createTahun(data: CreateTahunTahunDto) {
    const request = new Tahun();
    request.nama_tahun = data.nama_tahun;
    const result = this.r_tahun.insert(request);
    return result;
  }

  async findAllTahun() {
    const data = await this.r_tahun.find({ order: { created_at: 'DESC' } });
    return data;
  }

  async findOneTahun(id: string) {
    const data = await this.r_tahun.findOne({ where: { id: id } });
    return data;
  }

  updateTahun(id: string, data: any) {
    const request = new Tahun();
    request.nama_tahun = data.nama_tahun;
    const result = this.r_tahun.update(id, request);
    return result;
  }

  async removeTahun(id: string) {
    const bb = await this.r_tahun.findOne({ where: { id: id } });
    const data = await this.r_tahun.delete(id);
    return `This action removes a #${id} tahun`;
  }
}
