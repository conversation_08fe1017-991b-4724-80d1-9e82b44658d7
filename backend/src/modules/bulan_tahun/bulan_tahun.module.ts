import { Module } from '@nestjs/common';
import { BulanTahunService } from './bulan_tahun.service';
import { BulanTahunController } from './bulan_tahun.controller';
import { <PERSON><PERSON><PERSON>, Tahun } from './entities/bulan_tahun.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([Tahun, Bulan])],
  controllers: [BulanTahunController],
  providers: [BulanTahunService],
})
export class BulanTahunModule {}
