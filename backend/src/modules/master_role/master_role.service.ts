import { Injectable } from '@nestjs/common';
import { CreateMasterRoleDto } from './dto/create-master_role.dto';
import { UpdateMasterRoleDto } from './dto/update-master_role.dto';
import { MasterRole } from './entities/master_role.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { HakAkses } from './entities/hak-akses.entity';
import { CreateHakAksesDto } from './dto/create-hak_akses.dto';
import { Menu } from '../menu/entities/menu.entity';
import { User } from '../user/entities/user.entity';

@Injectable()
export class MasterRoleService {
  constructor(
    @InjectRepository(MasterRole)
    private repository: Repository<MasterRole>,
    @InjectRepository(HakAkses)
    private repo_hak: Repository<HakAkses>,
    @InjectRepository(User)
    private repo_user: Repository<User>,
    @InjectRepository(Menu)
    private repo_menu: Repository<Menu>,
  ) {}
  async create(data: CreateMasterRoleDto) {
    const request = new MasterRole();
    request.name = data.name;
    request.ket = data.ket;
    const result = await this.repository.insert(request);
    return result;
  }

  async createHak(data: CreateHakAksesDto) {
    const reqRole = new MasterRole();
    reqRole.id = data.id_role;
    const reqHak = new HakAkses();
    const reqMenu = new Menu();
    reqMenu.id = data.id_menu;
    reqHak.role = reqRole;
    // reqHak.menu = reqMenu;
    reqHak.id_menu = data.id_menu;
    const result = await this.repo_hak.insert(reqHak);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findAllHak() {
    const data = await this.repo_hak.find({
      order: { created_at: 'DESC' },
      relations: ['role'],
    });
    if (data) {
      const re = await this.repo_menu.find();
      data.map((d) => {
        var dataret = {};
        const cc = re.map((dd) => {
          if (dd.id == d.id_menu) dataret = dd;
          else return null;
        });
        d['menu'] = dataret;
      });

      return data;
    }
    return data;
  }

  async findAllHakUser(id: string) {
    const data = await this.repo_user.findOne({
      where: { id: id },
      order: { created_at: 'DESC' },
      relations: ['role.hak_akses'],
    });
    if (data) {
      const re = await this.repo_menu.find();
      data.role.hak_akses.map((d) => {
        var dataret = {};
        const cc = re.map((dd) => {
          if (dd.id == d.id_menu) dataret = dd;
          else return null;
        });
        d['menu'] = dataret;
      });

      return data;
    }
    return data;
  }

  async findOne(id: string) {
    const data = await this.repository.find({
      where: { id: id },
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async update(id: string, data: UpdateMasterRoleDto) {
    try {
      const request = new MasterRole();
      request.name = data.name;
      request.ket = data.ket;
      const result = await this.repository.update(id, request);
      return result;
    } catch (error) {
      console.log(error);
      return;
    }
  }

  async updateUserRole(id: string, id_role: string) {
    const user = await this.repo_user.find({ where: { id: id } });
    const masterUser = new User();
    const masterRole = new MasterRole();
    masterRole.id = id_role;
    masterUser.role = masterRole;

    const result = await this.repo_user.update(id, masterUser);
    return result;
  }

  async updateRoleUser(id_role: string, id_user: string) {
    try {
      const request = new User();
      const reqRole = new MasterRole();
      reqRole.id = id_role;
      request.role = reqRole;
      const result = await this.repo_user.update(id_user, request);
      return result;
    } catch (er) {
      console.log(er);
      return;
    }
  }

  async remove(id: string) {
    try {
      await Promise.all([
        this.repo_hak
          .createQueryBuilder()
          .delete()
          .from(HakAkses)
          .where('roleId = :id', { id })
          .execute(),
        // this.repo_user
        //   .createQueryBuilder()
        //   .update()
        //   .set({ role: null }) // Adjust this according to your schema
        //   .where('roleId = :id', { id })
        //   .execute(),
      ]);
      // const user = await this.repo_user.findOne({
      //   where: { role: { id: id } },
      // });
      // user.role = null;
      // const result = await this.repo_user.update(user.id, user);

      const data = await this.repository.delete(id);
      return { status: true, message: 'Berhasil Menghapus' };
    } catch (error) {
      return { status: false, message: 'Gagal Menghapus' };
    }
  }

  async removeHak(id: string) {
    const menu = await this.repo_hak.findOne({ where: { id: id } });
    const data = await this.repo_hak.delete(id);
    return data;
  }
}
