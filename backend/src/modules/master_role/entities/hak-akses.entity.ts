import { MasterRole } from 'src/modules/master_role/entities/master_role.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity({ name: 'hak_akses' })
export class HakAkses {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => MasterRole, (data) => data.id, {
    nullable: true,
  })
  @JoinColumn()
  role: MasterRole;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  id_menu: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
