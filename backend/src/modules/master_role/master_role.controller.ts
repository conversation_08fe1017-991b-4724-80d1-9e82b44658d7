import {
  Controller,
  Get,
  Post,
  Body,
  Param,
} from '@nestjs/common';
import { MasterRoleService } from './master_role.service';
import { CreateMasterRoleDto } from './dto/create-master_role.dto';
import { UpdateMasterRoleDto } from './dto/update-master_role.dto';
import { ApiTags } from '@nestjs/swagger';
import { CreateHakAksesDto } from './dto/create-hak_akses.dto';

import { IsRole } from 'src/common/decorators/role.decorator';

@ApiTags('Modul Master Role')
@Controller('v1/master-role')
export class MasterRoleController {
  constructor(private readonly masterRoleService: MasterRoleService) {}

  @IsRole('admin')
  @IsRole('user')
  @Post()
  async create(@Body() createMasterRoleDto: CreateMasterRoleDto) {
    const data = await this.masterRoleService.create(createMasterRoleDto);
    return {
      success: true,
      message: 'Data Master Role Berhasil Di Tambah',
    };
  }

  @IsRole('admin')
  @Get()
  async findAll() {
    const data = await this.masterRoleService.findAll();
    return {
      data: data,
      success: true,
      message: 'Data Master Role',
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.masterRoleService.findOne(id);
    return {
      data: data,
      success: true,
      message: 'Data Master Role',
    };
  }
  @IsRole('admin')
  @IsRole('user')
  @Post('hak-akses/add')
  async createHak(@Body() data: CreateHakAksesDto) {
    const rr = await this.masterRoleService.createHak(data);
    return {
      success: true,
      message: 'Data  Hak Akses Berhasil Di Tambah',
    };
  }

  @Post(':id')
  async update(
    @Param('id') id: string,
    @Body() updateMasterRoleDto: UpdateMasterRoleDto,
  ) {
    const data = await this.masterRoleService.update(id, updateMasterRoleDto);
    return {
      success: true,
      message: 'Data Master Role Berhasil Di Perbaharui',
    };
  }

  @Post('update/user/role/:id_user/:id_role')
  @IsRole('admin')
  async updateRoleUser(
    @Param('id_user') id: string,
    @Param('id_role') id_role: string,
  ) {
    const data = await this.masterRoleService.updateUserRole(id, id_role);
    return {
      success: true,
      message: 'Data Master Role Berhasil Di Perbaharui',
    };
  }

  @IsRole('admin')
  @IsRole('user')
  @Post('delete/:id')
  async remove(@Param('id') id: string) {
    const data = await this.masterRoleService.remove(id);
    return {
      data,
    };
  }

  @IsRole('admin')
  @IsRole('user')
  @Post('hak-akses/delete/:id')
  removeHak(@Param('id') id: string) {
    const data = this.masterRoleService.removeHak(id);
    return {
      success: true,
      message: 'Data  Hak Akses Berhasil Di Hapus',
    };
  }

  @Get('data/hak-akses')
  async findAllHak() {
    const data = await this.masterRoleService.findAllHak();
    return {
      data: data,
      success: true,
      message: 'Data Hak Akses',
    };
  }

  // @Public()
  @Get('data/hak-akses/user/:id_user')
  async findAllHakUser(@Param('id_user') id: string) {
    const data = await this.masterRoleService.findAllHakUser(id);
    return {
      data: data,
      success: true,
      message: 'Data Hak Akses',
    };
  }
  @Get('update/user/role/:id_user/:id_role')
  async updateUserRole(
    @Param('id_role') id_role: string,
    @Param('id_user') id_user: string,
  ) {
    const data = await this.masterRoleService.updateRoleUser(id_role, id_user);
    return {
      data: data,
      success: true,
      message: 'Data Update Role User',
    };
  }
}
