import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MasterRoleService } from './master_role.service';
import { MasterRoleController } from './master_role.controller';
import { MasterRole } from './entities/master_role.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HakAkses } from './entities/hak-akses.entity';
import { User } from '../user/entities/user.entity';
import { Menu } from '../menu/entities/menu.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MasterRole, HakAkses, User, Menu])],
  controllers: [MasterRoleController],
  providers: [MasterRoleService],
})
export class MasterRoleModule {}
