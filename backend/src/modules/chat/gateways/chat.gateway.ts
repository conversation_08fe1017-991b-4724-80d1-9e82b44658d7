import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger } from '@nestjs/common';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { JwtService } from '@nestjs/jwt';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userType?: 'guest_user' | 'system_user';
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/chat',
})
export class ChatGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);
  private connectedUsers = new Map<string, string>(); // userId -> socketId
  private guestConnectionCounts = new Map<string, number>(); // IP -> connection count

  constructor(
    private readonly chatService: ChatService,
    private readonly jwtService: JwtService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('Chat WebSocket Gateway initialized');
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token =
        client.handshake.auth?.token ||
        client.handshake.headers?.authorization?.replace('Bearer ', '');

      if (token) {
        // Authenticated user (system user/operator)
        try {
          const payload = this.jwtService.verify(token, {
            algorithms: ['HS256'],
            issuer: 'wbs-kg-backend',
            audience: 'wbs-kg-frontend',
          });

          // Validate payload structure
          if (!payload.id || !payload.username) {
            throw new Error('Invalid token payload');
          }

          client.userId = payload.id;
          client.userType = 'system_user';

          this.connectedUsers.set(client.userId, client.id);

          // Update operator online status
          await this.chatService.updateOperatorOnlineStatus(client.userId, true);

          this.logger.log(
            `Authenticated client ${client.id} connected as ${client.userType}: ${client.userId}`,
          );
        } catch (tokenError) {
          this.logger.error(`Invalid token for client ${client.id}:`, tokenError.message);
          client.disconnect(true);
          return;
        }
      } else {
        // Guest user - allow connection but with restrictions
        client.userType = 'guest_user';

        // Rate limit guest connections
        const clientIp = client.handshake.address;
        if (await this.isRateLimited(clientIp)) {
          this.logger.warn(`Rate limited connection from IP: ${clientIp}`);
          client.disconnect(true);
          return;
        }

        this.logger.log(`Guest client ${client.id} connected from IP: ${clientIp}`);
      }
    } catch (error) {
      this.logger.error(
        `Connection error for client ${client.id}:`,
        error.message,
      );
      client.disconnect(true);
    }
  }

  private async isRateLimited(ip: string): Promise<boolean> {
    // Simple in-memory rate limiting for guest connections
    // In production, use Redis or similar
    const key = `guest_connections:${ip}`;
    const connections = this.guestConnectionCounts.get(key) || 0;

    if (connections > 10) { // Max 10 connections per IP
      return true;
    }

    this.guestConnectionCounts.set(key, connections + 1);

    // Clean up after 1 hour
    setTimeout(() => {
      this.guestConnectionCounts.delete(key);
    }, 3600000);

    return false;
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      this.connectedUsers.delete(client.userId);

      // Update user offline status for system users
      if (client.userType === 'system_user') {
        await this.chatService.updateOperatorOnlineStatus(client.userId, false);
      }

      this.logger.log(`Client ${client.id} disconnected: ${client.userId}`);
    }
  }

  @SubscribeMessage('authenticate')
  async handleAuthenticate(
    @MessageBody()
    data: { userId: string; userType: 'guest_user' | 'system_user' },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      client.userId = data.userId;
      client.userType = data.userType;

      this.connectedUsers.set(client.userId, client.id);

      // Join user to their chat rooms
      const chatRooms = await this.chatService.getUserChatRooms(
        client.userId,
        client.userType,
      );
      for (const room of chatRooms) {
        client.join(`room:${room.id}`);
      }

      client.emit('authenticated', { success: true });
      this.logger.log(
        `Client ${client.id} authenticated as ${client.userType}: ${client.userId}`,
      );
    } catch (error) {
      this.logger.error('Authentication failed:', error);
      client.emit('error', { message: 'Authentication failed' });
    }
  }

  @SubscribeMessage('sendMessage')
  async handleSendMessage(
    @MessageBody() data: SendMessageDto,
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      const message = await this.chatService.sendMessage({
        ...data,
        senderId: client.userId,
        senderType: client.userType,
      });

      // Emit to all participants in the chat room
      this.server.to(`room:${data.chatRoomId}`).emit('newMessage', {
        id: message.id,
        chatRoomId: message.chat_room.id,
        content: message.content,
        senderType: message.sender_type,
        senderId: client.userId,
        messageType: message.message_type,
        timestamp: message.timestamp,
        attachmentUrl: message.attachment_url,
        attachmentName: message.attachment_name,
        replyToMessageId: message.reply_to_message_id,
      });

      // Update last message timestamp for the room
      await this.chatService.updateChatRoomLastMessage(data.chatRoomId);
    } catch (error) {
      this.logger.error('Error sending message:', error);
      client.emit('error', { message: 'Failed to send message' });
    }
  }

  @SubscribeMessage('joinRoom')
  async handleJoinRoom(
    @MessageBody() data: { chatRoomId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      // Verify user has access to this room
      const hasAccess = await this.chatService.userHasAccessToRoom(
        client.userId,
        client.userType,
        data.chatRoomId,
      );

      if (!hasAccess) {
        client.emit('error', { message: 'Access denied to chat room' });
        return;
      }

      client.join(`room:${data.chatRoomId}`);
      client.emit('joinedRoom', { chatRoomId: data.chatRoomId });
    } catch (error) {
      this.logger.error('Error joining room:', error);
      client.emit('error', { message: 'Failed to join room' });
    }
  }

  @SubscribeMessage('leaveRoom')
  async handleLeaveRoom(
    @MessageBody() data: { chatRoomId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    client.leave(`room:${data.chatRoomId}`);
    client.emit('leftRoom', { chatRoomId: data.chatRoomId });
  }

  @SubscribeMessage('markAsRead')
  async handleMarkAsRead(
    @MessageBody() data: { chatRoomId: string; messageId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      await this.chatService.markMessageAsRead(
        data.chatRoomId,
        data.messageId,
        client.userId,
        client.userType,
      );

      // Notify other participants that message was read
      client.to(`room:${data.chatRoomId}`).emit('messageRead', {
        chatRoomId: data.chatRoomId,
        messageId: data.messageId,
        readBy: client.userId,
        readByType: client.userType,
      });
    } catch (error) {
      this.logger.error('Error marking message as read:', error);
      client.emit('error', { message: 'Failed to mark message as read' });
    }
  }

  @SubscribeMessage('typing')
  async handleTyping(
    @MessageBody() data: { chatRoomId: string; isTyping: boolean },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) return;

    client.to(`room:${data.chatRoomId}`).emit('userTyping', {
      chatRoomId: data.chatRoomId,
      userId: client.userId,
      userType: client.userType,
      isTyping: data.isTyping,
    });
  }

  // Method to send notifications to specific users
  async notifyUser(userId: string, event: string, data: any) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.server.to(socketId).emit(event, data);
    }
  }

  // Method to broadcast to a chat room
  async broadcastToRoom(chatRoomId: string, event: string, data: any) {
    this.server.to(`room:${chatRoomId}`).emit(event, data);
  }
}
