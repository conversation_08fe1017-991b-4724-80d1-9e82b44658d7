import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';

// Entities
import { ChatUser } from './entities/user.entity';
import { ChatOperator } from './entities/operator.entity';
import { ChatRoom } from './entities/chat-room.entity';
import { Message } from './entities/message.entity';
import { ChatRoomParticipant } from './entities/chat-room-participant.entity';
import { User } from '../user/entities/user.entity';

// Services
import { ChatService } from './services/chat.service';
import { EncryptionService } from './services/encryption.service';

// Controllers
import { ChatController } from './controllers/chat.controller';
import { ChatOperatorController } from './controllers/chat-operator.controller';

// Gateways
import { ChatGateway } from './gateways/chat.gateway';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Chat<PERSON><PERSON>,
      ChatOperator,
      ChatRoom,
      Message,
      ChatRoomParticipant,
      User,
    ]),
    JwtModule.register({
      secret: process.env.JWT_ACCESS_TOKEN_SECRET,
      signOptions: {
        expiresIn: process.env.JWT_ACCESS_TOKEN_EXPIRATION_TIME + 's',
      },
    }),
    // N8nIntegrationModule is imported via forwardRef in N8nIntegrationModule
    // CommonAppModule is Global, so OfficeHoursService is available
  ],
  controllers: [ChatController, ChatOperatorController],
  providers: [ChatService, EncryptionService, ChatGateway],
  exports: [ChatService, EncryptionService], // Export ChatService for N8nIntegrationModule
})
export class ChatModule {}
