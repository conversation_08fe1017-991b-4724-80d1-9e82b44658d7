import { IsString, IsOptional, IsIn, IsUUID } from 'class-validator';

export class SendMessageDto {
  @IsUUID()
  chatRoomId: string;

  @IsString()
  content: string;

  @IsOptional()
  @IsIn(['text', 'image', 'file'])
  messageType?: string = 'text';

  @IsOptional()
  @IsString()
  attachmentUrl?: string;

  @IsOptional()
  @IsString()
  attachmentName?: string;

  @IsOptional()
  attachmentSize?: number;

  @IsOptional()
  @IsUUID()
  replyToMessageId?: string;
}
