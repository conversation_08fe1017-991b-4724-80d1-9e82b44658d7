import { Is<PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, Is<PERSON>hone<PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { SanitizeString, SanitizeHtml, NoXss, NoSqlInjection } from '../../../common/validators/input-sanitizer';

export class InitiateChatDto {
  @IsOptional()
  @IsEmail()
  @MaxLength(254) // RFC 5321 email length limit
  @SanitizeString()
  @NoXss()
  @NoSqlInjection()
  email?: string;

  @IsOptional()
  @IsPhoneNumber()
  @MaxLength(20)
  @SanitizeString()
  @Matches(/^[\+]?[1-9][\d]{0,15}$/, { message: 'Invalid phone number format' })
  phone?: string;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  @SanitizeString()
  @NoXss()
  @NoSqlInjection()
  @Matches(/^[a-zA-Z\s\-\.\']+$/, { message: 'Name can only contain letters, spaces, hyphens, dots, and apostrophes' })
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  @SanitizeHtml()
  @NoXss()
  @NoSqlInjection()
  initialMessage?: string;
}
