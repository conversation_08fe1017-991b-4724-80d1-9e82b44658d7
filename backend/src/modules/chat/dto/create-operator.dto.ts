import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsIn,
} from 'class-validator';

export class CreateOperatorDto {
  @IsString()
  username: string;

  @IsString()
  @MinLength(6)
  password: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsIn(['operator', 'admin', 'supervisor'])
  role?: string = 'operator';
}
