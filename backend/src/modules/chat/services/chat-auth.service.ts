import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ChatService } from './chat.service';
import { ChatOperator } from '../entities/operator.entity';

@Injectable()
export class ChatAuthService {
  constructor(
    private readonly chatService: ChatService,
    private readonly jwtService: JwtService,
  ) {}

  // Only operator authentication is needed since chat is public for users
  async validateOperator(
    username: string,
    password: string,
  ): Promise<ChatOperator> {
    const operator = await this.chatService.validateOperator(
      username,
      password,
    );
    if (!operator) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return operator;
  }

  async loginOperator(operator: ChatOperator) {
    const payload = {
      sub: operator.id,
      username: operator.username,
      userType: 'operator',
      role: operator.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      operator: {
        id: operator.id,
        username: operator.username,
        name: operator.name,
        role: operator.role,
      },
    };
  }

  async registerOperator(operatorData: {
    username: string;
    password: string;
    name?: string;
    email?: string;
    role?: string;
  }) {
    const operator = await this.chatService.createOperator(operatorData);

    // Auto-login after registration
    return this.loginOperator(operator);
  }
}
