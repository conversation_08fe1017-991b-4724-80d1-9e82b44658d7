import { Injectable } from '@nestjs/common';
import * as CryptoJS from 'crypto-js';

@Injectable()
export class EncryptionService {
  private readonly secretKey: string;

  constructor() {
    // In production, this should come from environment variables
    this.secretKey =
      process.env.CHAT_ENCRYPTION_KEY ||
      'default-secret-key-change-in-production';
  }

  /**
   * Encrypt message content using AES-256
   */
  encrypt(text: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(text, this.secretKey).toString();
      return encrypted;
    } catch (error) {
      throw new Error('Failed to encrypt message content');
    }
  }

  /**
   * Decrypt message content
   */
  decrypt(encryptedText: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedText, this.secretKey);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);

      if (!decrypted) {
        throw new Error('Failed to decrypt message - invalid data');
      }

      return decrypted;
    } catch (error) {
      throw new Error('Failed to decrypt message content');
    }
  }

  /**
   * Generate a hash for integrity verification
   */
  generateHash(text: string): string {
    return CryptoJS.SHA256(text).toString();
  }

  /**
   * Verify hash integrity
   */
  verifyHash(text: string, hash: string): boolean {
    const generatedHash = this.generateHash(text);
    return generatedHash === hash;
  }
}
