import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ChatService } from '../services/chat.service';
import { InitiateChatDto } from '../dto/initiate-chat.dto';
import { Public } from '../../../common/decorators/public.decorator';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';

@Controller('chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Public()
  @Post('initiate')
  async initiateChat(@Body() initiateChatDto: InitiateChatDto) {
    const result = await this.chatService.initiateChatSession(initiateChatDto);

    return {
      success: true,
      data: {
        userId: result.user.id,
        userName: result.user.name,
        chatRoomId: result.chatRoom.id,
        messageId: result.messageId,
      },
    };
  }

  @Public()
  @Get('rooms/:chatRoomId/messages')
  async getChatRoomMessages(
    @Param('chatRoomId') chatRoomId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 50,
  ) {
    const result = await this.chatService.getChatRoomMessages(
      chatRoomId,
      page,
      limit,
    );

    return {
      success: true,
      data: result,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get('rooms/:chatRoomId/access')
  async checkRoomAccess(
    @Param('chatRoomId') chatRoomId: string,
    @Request() req: any,
  ) {
    const hasAccess = await this.chatService.userHasAccessToRoom(
      req.user.id,
      'system_user', // Authenticated users are system users
      chatRoomId,
    );

    return {
      success: true,
      data: { hasAccess },
    };
  }
}
