import {
  Controller,
  Get,
  Param,
  UseGuards,
  Request,
  Post,
} from '@nestjs/common';
import { ChatService } from '../services/chat.service';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';

@Controller('chat/operator')
@UseGuards(JwtAuthGuard)
export class ChatOperatorController {
  constructor(private readonly chatService: ChatService) {}

  @Get('chat-rooms')
  async getActiveChatRooms() {
    const chatRooms = await this.chatService.getActiveChatRooms();

    return {
      success: true,
      data: chatRooms,
    };
  }

  @Post('chat-rooms/:chatRoomId/assign')
  async assignOperatorToRoom(
    @Param('chatRoomId') chatRoomId: string,
    @Request() req: any,
  ) {
    // Get operator ID from JWT token (existing auth system)
    const operatorId = req.user.id;
    await this.chatService.assignOperatorToRoom(chatRoomId, operatorId);

    return {
      success: true,
      message: 'Operator assigned to chat room successfully',
    };
  }

  @Get('online')
  async getOnlineOperators() {
    const operators = await this.chatService.getOnlineOperators();

    return {
      success: true,
      data: operators,
    };
  }

  @Get('my-rooms')
  async getMyRooms(@Request() req: any) {
    const operatorId = req.user.id;
    const chatRooms = await this.chatService.getUserChatRooms(
      operatorId,
      'system_user',
    );

    return {
      success: true,
      data: chatRooms,
    };
  }
}
