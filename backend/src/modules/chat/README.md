# Chat System Documentation

## Overview

This is a real-time chat system built with NestJS, WebSockets, and encrypted messaging. The system supports public chat initiation without authentication, and operator management through the existing user system.

## Features

### Core Features

- **Public Chat Initiation**: Users can start chatting without creating accounts
- **Real-time Messaging**: WebSocket-based real-time communication
- **Message Encryption**: All messages are encrypted at rest using AES-256
- **Operator Assignment**: System users can be assigned as chat operators
- **Message History**: Persistent message storage with pagination
- **Typing Indicators**: Real-time typing status
- **Read Receipts**: Message read status tracking

### Security Features

- **End-to-end Encryption**: Messages encrypted before storage
- **JWT Authentication**: For system operators
- **Input Validation**: All inputs validated using class-validator
- **CORS Protection**: Configurable CORS settings

## API Endpoints

### Public Endpoints

#### Initiate Chat

```
POST /chat/initiate
```

**Body:**

```json
{
  "email": "<EMAIL>", // optional
  "phone": "+**********", // optional
  "name": "User Name", // required
  "initialMessage": "Hello, I need help" // optional
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "userId": "uuid",
    "userName": "User Name",
    "chatRoomId": "uuid",
    "messageId": "uuid"
  }
}
```

#### Get Chat Messages

```
GET /chat/rooms/{chatRoomId}/messages?page=1&limit=50
```

### Operator Endpoints (Requires Authentication)

#### Get Active Chat Rooms

```
GET /chat/operator/chat-rooms
```

#### Assign Operator to Room

```
POST /chat/operator/chat-rooms/{chatRoomId}/assign
```

#### Get Online Operators

```
GET /chat/operator/online
```

## WebSocket Events

### Connection

```javascript
const socket = io('/chat', {
  auth: {
    token: 'jwt-token', // for operators only
  },
});
```

### Events

#### Authentication (for guest users)

```javascript
socket.emit('authenticate', {
  userId: 'guest-user-id',
  userType: 'guest_user',
});
```

#### Send Message

```javascript
socket.emit('sendMessage', {
  chatRoomId: 'room-id',
  content: 'message content',
  messageType: 'text', // 'text', 'image', 'file'
  replyToMessageId: 'message-id', // optional
});
```

#### Join Room

```javascript
socket.emit('joinRoom', {
  chatRoomId: 'room-id',
});
```

#### Typing Indicator

```javascript
socket.emit('typing', {
  chatRoomId: 'room-id',
  isTyping: true,
});
```

#### Mark as Read

```javascript
socket.emit('markAsRead', {
  chatRoomId: 'room-id',
  messageId: 'message-id',
});
```

### Received Events

#### New Message

```javascript
socket.on('newMessage', (data) => {
  // Handle new message
});
```

#### User Typing

```javascript
socket.on('userTyping', (data) => {
  // Handle typing indicator
});
```

#### Message Read

```javascript
socket.on('messageRead', (data) => {
  // Handle read receipt
});
```

## Database Schema

### chat_users (Guest Users)

- `id` (UUID, Primary Key)
- `email` (String, Unique, Optional)
- `phone` (String, Unique, Optional)
- `name` (String)
- `password_hash` (String, Empty for guests)
- `verification_status` (Enum: 'guest', 'pending', 'verified')
- `is_active` (Boolean)
- `last_seen` (DateTime)
- `created_at`, `updated_at` (DateTime)

### chat_rooms

- `id` (UUID, Primary Key)
- `type` (Enum: 'user-operator', 'group', 'system')
- `title` (String, Optional)
- `status` (Enum: 'active', 'closed', 'archived')
- `last_message_at` (DateTime)
- `closed_at` (DateTime, Optional)
- `closed_by_type` (String, Optional)
- `closed_by_id` (UUID, Optional)
- `created_at`, `updated_at` (DateTime)

### chat_messages

- `id` (UUID, Primary Key)
- `chat_room_id` (UUID, Foreign Key)
- `sender_type` (Enum: 'guest_user', 'system_user', 'system')
- `sender_guest_user_id` (UUID, Foreign Key, Optional)
- `sender_system_user_id` (UUID, Foreign Key, Optional)
- `content` (Text, Encrypted)
- `message_type` (Enum: 'text', 'image', 'file', 'system')
- `attachment_url` (String, Optional)
- `attachment_name` (String, Optional)
- `attachment_size` (Integer, Optional)
- `status` (Enum: 'sent', 'delivered', 'read', 'failed')
- `is_edited` (Boolean)
- `edited_at` (DateTime, Optional)
- `reply_to_message_id` (UUID, Optional)
- `is_deleted` (Boolean)
- `deleted_at` (DateTime, Optional)
- `metadata` (JSON, Optional)
- `timestamp` (DateTime)
- `created_at`, `updated_at` (DateTime)

### chat_room_participants

- `id` (UUID, Primary Key)
- `chat_room_id` (UUID, Foreign Key)
- `participant_type` (Enum: 'guest_user', 'system_user')
- `guest_user_id` (UUID, Foreign Key, Optional)
- `system_user_id` (UUID, Foreign Key, Optional)
- `status` (Enum: 'active', 'left', 'kicked')
- `joined_at` (DateTime)
- `left_at` (DateTime, Optional)
- `last_read_message_id` (UUID, Optional)
- `created_at`, `updated_at` (DateTime)

## Environment Variables

```env
# Chat-specific environment variables
CHAT_ENCRYPTION_KEY=your-secret-encryption-key
FRONTEND_URL=http://localhost:3000,http://localhost:5173

# JWT (inherited from existing system)
JWT_ACCESS_TOKEN_SECRET=your-jwt-secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
```

## Integration with Existing System

### User Roles

To make a system user a chat operator, assign them a role with chat permissions through the existing role management system.

### Authentication

The chat system uses the existing JWT authentication for operators and allows public access for guest users.

### Deployment

The chat system is integrated with the main application and will be deployed together.

## Usage Example

### Frontend Integration (JavaScript)

```javascript
// Initialize chat for guest user
const initializeChat = async (userInfo) => {
  const response = await fetch('/api/chat/initiate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userInfo),
  });

  const { data } = await response.json();

  // Connect to WebSocket
  const socket = io('/chat');

  // Authenticate as guest user
  socket.emit('authenticate', {
    userId: data.userId,
    userType: 'guest_user',
  });

  // Join chat room
  socket.emit('joinRoom', {
    chatRoomId: data.chatRoomId,
  });

  return { socket, chatData: data };
};

// Send message
const sendMessage = (socket, chatRoomId, content) => {
  socket.emit('sendMessage', {
    chatRoomId,
    content,
    messageType: 'text',
  });
};
```

## Performance Considerations

- Messages are paginated to avoid loading large chat histories
- WebSocket connections are managed efficiently
- Encryption is done server-side to reduce client-side complexity
- Database queries are optimized with proper indexing

## Security Considerations

- All messages are encrypted at rest
- Input validation prevents XSS and injection attacks
- CORS is properly configured
- WebSocket connections are monitored for abuse
- Rate limiting should be implemented for message sending
