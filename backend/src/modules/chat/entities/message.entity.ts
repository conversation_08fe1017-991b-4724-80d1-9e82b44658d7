import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ChatRoom } from './chat-room.entity';
import { ChatUser } from './user.entity';
import { User } from '../../user/entities/user.entity';

@Entity({ name: 'chat_messages' })
export class Message {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ChatRoom, (chatRoom) => chatRoom.messages)
  @JoinColumn()
  chat_room: ChatRoom;

  @Column({
    comment: 'guest_user, system_user, system',
  })
  sender_type: string;

  @ManyToOne(() => ChatUser, (user) => user.messages, {
    nullable: true,
  })
  @JoinColumn()
  sender_guest_user: ChatUser;

  @ManyToOne(() => User, {
    nullable: true,
  })
  @JoinColumn()
  sender_system_user: User;

  @Column('text')
  content: string; // This will be encrypted

  @Column({
    default: 'text',
    comment: 'text, image, file, system',
  })
  message_type: string;

  @Column({
    nullable: true,
  })
  attachment_url: string;

  @Column({
    nullable: true,
  })
  attachment_name: string;

  @Column({
    nullable: true,
  })
  attachment_size: number;

  @Column({
    default: 'sent',
    comment: 'sent, delivered, read, failed',
  })
  status: string;

  @Column({
    default: false,
  })
  is_edited: boolean;

  @Column({
    nullable: true,
  })
  edited_at: Date;

  @Column({
    nullable: true,
  })
  reply_to_message_id: string;

  @Column({
    default: false,
  })
  is_deleted: boolean;

  @Column({
    nullable: true,
  })
  deleted_at: Date;

  @Column({
    nullable: true,
  })
  metadata: string; // JSON string for additional data

  @Column()
  timestamp: Date;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
    this.timestamp = new Date();
  }
}
