import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
  OneToMany,
} from 'typeorm';
import { Message } from './message.entity';
import { ChatRoomParticipant } from './chat-room-participant.entity';

@Entity({ name: 'chat_rooms' })
export class ChatRoom {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    default: 'user-operator',
    comment: 'user-operator, group, system',
  })
  type: string;

  @Column({
    nullable: true,
  })
  title: string;

  @Column({
    default: 'active',
    comment: 'active, closed, archived',
  })
  status: string;

  @OneToMany(() => Message, (message) => message.chat_room)
  messages: Message[];

  @OneToMany(() => ChatRoomParticipant, (participant) => participant.chat_room)
  participants: ChatRoomParticipant[];

  @Column({
    nullable: true,
  })
  last_message_at: Date;

  @Column({
    nullable: true,
  })
  closed_at: Date;

  @Column({
    nullable: true,
  })
  closed_by_type: string; // 'user' or 'operator'

  @Column({
    nullable: true,
  })
  closed_by_id: string;

  @Column({
    default: 'unassigned',
    comment: 'operator, bot, unassigned, pending_assignment',
  })
  current_handler: string;

  @Column({
    nullable: true,
  })
  handler_assigned_at: Date;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
