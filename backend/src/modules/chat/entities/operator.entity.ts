import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
} from 'typeorm';

@Entity({ name: 'chat_operators' })
export class ChatOperator {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    unique: true,
  })
  username: string;

  @Column()
  password_hash: string;

  @Column({
    default: 'operator',
    comment: 'operator, admin, supervisor',
  })
  role: string;

  @Column({
    nullable: true,
  })
  name: string;

  @Column({
    nullable: true,
  })
  email: string;

  @Column({
    default: true,
  })
  is_active: boolean;

  @Column({
    nullable: true,
  })
  last_seen: Date;

  @Column({
    default: false,
  })
  is_online: boolean;

  @Column({
    nullable: true,
  })
  currentHashedRefreshToken?: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
