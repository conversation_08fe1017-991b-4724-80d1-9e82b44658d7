import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
  OneToMany,
} from 'typeorm';
import { Message } from './message.entity';
import { ChatRoomParticipant } from './chat-room-participant.entity';

@Entity({ name: 'chat_users' })
export class ChatUser {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    unique: true,
  })
  email: string;

  @Column({
    unique: true,
    nullable: true,
  })
  phone: string;

  @Column()
  password_hash: string;

  @Column({
    default: 'pending',
    comment: 'pending, verified, suspended',
  })
  verification_status: string;

  @Column({
    nullable: true,
  })
  verification_token: string;

  @Column({
    nullable: true,
  })
  verification_expires_at: Date;

  @Column({
    nullable: true,
  })
  name: string;

  @Column({
    default: true,
  })
  is_active: boolean;

  @OneToMany(() => Message, (message) => message.sender_guest_user)
  messages: Message[];

  @OneToMany(() => ChatRoomParticipant, (participant) => participant.guest_user)
  chat_rooms: ChatRoomParticipant[];

  @Column({
    nullable: true,
  })
  last_seen: Date;

  @Column({
    nullable: true,
  })
  currentHashedRefreshToken?: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
