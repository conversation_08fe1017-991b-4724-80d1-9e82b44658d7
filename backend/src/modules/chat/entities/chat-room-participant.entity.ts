import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  BeforeUpdate,
  BeforeInsert,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ChatRoom } from './chat-room.entity';
import { ChatUser } from './user.entity';
import { User } from '../../user/entities/user.entity';

@Entity({ name: 'chat_room_participants' })
export class ChatRoomParticipant {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ChatRoom, (chatRoom) => chatRoom.participants)
  @JoinColumn()
  chat_room: ChatRoom;

  @Column({
    comment: 'guest_user, system_user',
  })
  participant_type: string;

  @ManyToOne(() => ChatUser, (user) => user.chat_rooms, {
    nullable: true,
  })
  @JoinColumn()
  guest_user: ChatUser;

  @ManyToOne(() => User, {
    nullable: true,
  })
  @JoinColumn()
  system_user: User;

  @Column({
    default: 'active',
    comment: 'active, left, kicked',
  })
  status: string;

  @Column({
    nullable: true,
  })
  joined_at: Date;

  @Column({
    nullable: true,
  })
  left_at: Date;

  @Column({
    nullable: true,
  })
  last_read_message_id: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
    this.joined_at = new Date();
  }
}
