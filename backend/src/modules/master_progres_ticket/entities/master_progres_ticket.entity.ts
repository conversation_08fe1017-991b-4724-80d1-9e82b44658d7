import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'master_progres_ticket' })
export class MasterProgresTicketEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  color: string;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
