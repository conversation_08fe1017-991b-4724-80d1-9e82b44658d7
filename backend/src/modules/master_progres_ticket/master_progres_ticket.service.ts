import { Injectable } from '@nestjs/common';
import { CreateMasterProgresTicketDto } from './dto/create-master_progres_ticket.dto';
import { UpdateMasterProgresTicketDto } from './dto/update-master_progres_ticket.dto';
import { MasterProgresTicketEntity } from './entities/master_progres_ticket.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TicketingEntity } from '../ticketing/entities/ticketing.entity';

@Injectable()
export class MasterProgresTicketService {
  constructor(
    @InjectRepository(MasterProgresTicketEntity)
    private repository: Repository<MasterProgresTicketEntity>,
    @InjectRepository(TicketingEntity)
    private repo_ticket: Repository<TicketingEntity>,
  ) {}
  create(data: CreateMasterProgresTicketDto) {
    const request = new MasterProgresTicketEntity();
    request.name = data.name;
    request.color = data.color;
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findOne(id: string) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  async update(id: string, data: UpdateMasterProgresTicketDto) {
    const request = new MasterProgresTicketEntity();
    request.name = data.name;
    request.color = data.color;
    const result = this.repository.update(id, request);
    return result;
  }

  async remove(id: string) {
    try {
      // const menu = await this.repository.findOne({ where: { id: id } });
      await this.repo_ticket
        .createQueryBuilder()
        .delete()
        .from(TicketingEntity)
        .where('progresId = :id', { id })
        .execute();
      const data = await this.repository.delete(id);
      return { status: true, message: 'Berhasil Menghapus' };
    } catch (error) {
      return { status: false, message: 'Gagal Menghapus' };
    }
  }
}
