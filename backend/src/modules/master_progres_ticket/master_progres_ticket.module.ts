import { Modu<PERSON> } from '@nestjs/common';
import { MasterProgresTicketService } from './master_progres_ticket.service';
import { MasterProgresTicketController } from './master_progres_ticket.controller';
import { MasterProgresTicketEntity } from './entities/master_progres_ticket.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TicketingEntity } from '../ticketing/entities/ticketing.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([MasterProgresTicketEntity, TicketingEntity]),
  ],
  controllers: [MasterProgresTicketController],
  providers: [MasterProgresTicketService],
})
export class MasterProgresTicketModule {}
