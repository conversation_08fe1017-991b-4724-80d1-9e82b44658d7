import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
} from '@nestjs/common';
import { MasterProgresTicketService } from './master_progres_ticket.service';
import { CreateMasterProgresTicketDto } from './dto/create-master_progres_ticket.dto';
import { UpdateMasterProgresTicketDto } from './dto/update-master_progres_ticket.dto';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';

@ApiTags('Modul Master Ticket')
@Controller('v1/master_progres_ticket')
export class MasterProgresTicketController {
  constructor(
    private readonly masterProgresTicketService: MasterProgresTicketService,
  ) {}

  @Public()
  @Post()
  create(@Body() createMasterProgresTicketDto: CreateMasterProgresTicketDto) {
    const data = this.masterProgresTicketService.create(
      createMasterProgresTicketDto,
    );
    return {
      success: true,
      message: 'Data Master Progres Berhasil Di Tambah',
    };
  }

  @Public()
  @Get()
  async findAll() {
    const data = await this.masterProgresTicketService.findAll();
    return {
      data: data,
      success: true,
      message: 'Data Master Progres',
    };
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    const data = this.masterProgresTicketService.findOne(id);
    return {
      data: data,
      success: true,
      message: 'Data Master Progres',
    };
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateMasterProgresTicketDto: UpdateMasterProgresTicketDto,
  ) {
    const data = this.masterProgresTicketService.update(
      id,
      updateMasterProgresTicketDto,
    );
    return {
      success: true,
      message: 'Data Master Progres Berhasil Di Perbaharui',
    };
  }

  @Post('delete/:id')
  @Public()
  async remove(@Param('id') id: string) {
    const data = await this.masterProgresTicketService.remove(id);
    return {
      data,
    };
  }
}
