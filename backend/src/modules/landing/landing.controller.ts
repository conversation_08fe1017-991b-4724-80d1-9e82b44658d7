import { Controller, Get, Param } from '@nestjs/common';
import { LandingService } from './landing.service';

import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators';

@Controller('v1/landing')
@ApiTags('ENDPOINT LANDING PAGE')
export class LandingController {
  constructor(private readonly landingService: LandingService) {}

  @Public()
  @Get('event')
  async findAllEvent() {
    const data = await this.landingService.findAllEvent();
    // try {
    //   for (let filee of data) {
    //     let arrayFile = [];
    //     for (let oneFile of filee.files) {
    //       arrayFile.push(
    //         await this.landingService.readFileAsBase64(
    //           './uploads/event/' + oneFile,
    //         ),
    //       );
    //     }

    //     filee['base64'] = arrayFile;
    //   }
    // } catch (error) {}

    return {
      success: true,
      message: 'Data Event',
      data: data,
    };
  }

  @Public()
  @Get('event/:id')
  async findEventById(@Param('id') id: string) {
    const data = await this.landingService.findEventById(id);
    try {
      for (let filee of data) {
        let arrayFile = [];
        for (let oneFile of filee.files) {
          arrayFile.push(
            await this.landingService.readFileAsBase64(
              './uploads/event/' + oneFile,
            ),
          );
        }

        filee['base64'] = arrayFile;
      }
    } catch (error) {}

    return {
      success: true,
      message: 'Data Event',
      data: data,
    };
  }

  @Public()
  @Get('berita')
  async findAllBerita() {
    const data = await this.landingService.findAllBerita();
    return {
      success: true,
      message: 'Data Berita',
      data: data,
    };
  }

  @Public()
  @Get('berita/:id')
  async findBeritaById(@Param('id') id: string) {
    const data = await this.landingService.findBeritaById(id);
    return {
      success: true,
      message: 'Data Berita',
      data: data,
    };
  }

  @Public()
  @Get('informasiumum')
  async findAllInUm() {
    const data = await this.landingService.findAllInformasiUmum();
    return {
      success: true,
      message: 'Data Informasi Umum',
      data: data,
    };
  }

  @Public()
  @Get('kontak')
  async findAllKontak() {
    const data = await this.landingService.findAllKontak();
    return {
      success: true,
      message: 'Data Kontak',
      data: data,
    };
  }

  @Public()
  @Get('kontak/:id')
  async findKontakById(@Param('id') id: string) {
    const data = await this.landingService.findKontakById(id);
    return {
      success: true,
      message: 'Data Kontak',
      data: data,
    };
  }

  @Public()
  @Get('artikel')
  async findAllArtikel() {
    const data = await this.landingService.findAllArtikel();
    return {
      success: true,
      message: 'Data Artikel',
      data: data,
    };
  }

  @Public()
  @Get('artikel/:id')
  async findArtikelById(@Param('id') id: string) {
    const data = await this.landingService.findArtikelById(id);
    return {
      success: true,
      message: 'Data Artikel',
      data: data,
    };
  }

  @Public()
  @Get('layanan')
  async findAllLayanan() {
    const data = await this.landingService.findAllLayanan();
    return {
      success: true,
      message: 'Data Layanan',
      data: data,
    };
  }

  @Public()
  @Get('layanan/:id')
  async findLayananById(@Param('id') id: string) {
    const data = await this.landingService.findLayananById(id);
    return {
      success: true,
      message: 'Data Layanan',
      data: data,
    };
  }

  @Public()
  @Get('rfc')
  async findAllRfc() {
    const data = await this.landingService.findAllRFC();
    return {
      success: true,
      message: 'Data RFC',
      data: data,
    };
  }

  @Public()
  @Get('rfc/:id')
  async findRfcById(@Param('id') id: string) {
    const data = await this.landingService.findRFCById(id);
    try {
      for (let filee of data) {
        filee['base64'] = await this.landingService.readFileAsBase64(
          './uploads/rfc/' + filee.file_pdf,
        );
      }
    } catch (error) {}
    return {
      success: true,
      message: 'Data RFC',
      data: data,
    };
  }

  @Public()
  @Get('paduan-pedoman')
  async findAllPedoman() {
    const data = await this.landingService.findAllPedoman();
    return {
      success: true,
      message: 'Data Paduan Pedoman',
      data: data,
    };
  }

  @Public()
  @Get('paduan-pedoman/:id')
  async findPedomanById(@Param('id') id: string) {
    const data = await this.landingService.findPedomanyId(id);
    return {
      success: true,
      message: 'Data Paduan Pedoman',
      data: data,
    };
  }

  @Public()
  @Get('paduan-keamanan')
  async findAllPanduKeamanan() {
    const data = await this.landingService.findAllPanduanKeamanan();
    // try {
    //   for (let filee of data) {
    //     filee['base64'] = await this.landingService.readFileAsBase64(
    //       './uploads/keamanan/' + filee.file,
    //     );
    //   }
    // } catch (error) {}

    return {
      success: true,
      message: 'Data Panduan Keamanan',
      data: data,
    };
  }

  @Public()
  @Get('paduan-keamanan/:id')
  async findAllPanduKeamananById(@Param('id') id: string) {
    const data = await this.landingService.findPanduanKeamananId(id);
    try {
      data['base64'] = await this.landingService.readFileAsBase64(
        './uploads/keamanan/' + data.file,
      );
    } catch (error) {}
    return {
      success: true,
      message: 'Data Panduan Keamanan',
      data: data,
    };
  }

  @Public()
  @Get('peringatan-keamanan')
  async findAllPeringatanKeamanan() {
    const data = await this.landingService.findAllPeringatanKeamanan();
    // try {
    //   for (let filee of data) {
    //     filee['base64'] = await this.landingService.readFileAsBase64(
    //       './uploads/keamanan/' + filee.file_pdf,
    //     );
    //   }
    // } catch (error) {}
    return {
      success: true,
      message: 'Data Peringatan Keamanan',
      data: data,
    };
  }

  @Public()
  @Get('peringatan-keamanan/:id')
  async findAllPeringatanKeamananById(@Param('id') id: string) {
    const data = await this.landingService.findPeringatanKeamananId(id);
    try {
      data['base64'] = await this.landingService.readFileAsBase64(
        './uploads/keamanan/' + data.file_pdf,
      );
    } catch (error) {}
    return {
      success: true,
      message: 'Data Peringatan Keamanan',
      data: data,
    };
  }

  @Public()
  @Get('monitoring/:id')
  async findAllMonitoringId(@Param('id') id: string) {
    const data = await this.landingService.findPMonitoringId(id);
    try {
      data['base64'] = await this.landingService.readFileAsBase64(
        './uploads/monitoring/' + data.file,
      );
    } catch (error) {}
    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }

  @Public()
  @Get('monitoring')
  async findAllMonitoring() {
    const data = await this.landingService.findAllMonitoring();
    // try {
    //   for (let filee of data) {
    //     filee['base64'] = await this.landingService.readFileAsBase64(
    //       './uploads/monitoring/' + filee.file,
    //     );
    //   }
    // } catch (error) {}
    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }

  @Public()
  @Get('monitoring/bulan/:bulan')
  async findAllMonitoringBybulan(@Param('bulan') bulan: string) {
    const data = await this.landingService.findAllMonitoringByBulan(bulan);
    try {
      for (let filee of data) {
        filee['base64'] = await this.landingService.readFileAsBase64(
          './uploads/monitoring/' + filee.file,
        );
      }
    } catch (error) {}

    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }
  @Public()
  @Get('monitoring/tahun/:tahun')
  async findAllMonitoringBytahun(@Param('tahun') tahun: string) {
    const data = await this.landingService.findAllMonitoringByTahun(tahun);
    try {
      for (let filee of data) {
        filee['base64'] = await this.landingService.readFileAsBase64(
          './uploads/monitoring/' + filee.file,
        );
      }
    } catch (error) {}

    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }

  @Public()
  @Get('monitoring/dropdown/tahun')
  async findAllMonitoringDropdowntahun() {
    const data = await this.landingService.findAllMonitoringDropdownTahun();

    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }
  @Public()
  @Get('monitoring/dropdown/bulan')
  async findAllMonitoringDropdownbulan() {
    const data = await this.landingService.findAllMonitoringDropdownBulan();

    return {
      success: true,
      message: 'Data Monitoring',
      data: data,
    };
  }
}
