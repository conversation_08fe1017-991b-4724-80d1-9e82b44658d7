import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Artikel } from '../artikel/entities/artikel.entity';
import { Berita } from '../berita/entities/berita.entity';
import { Event as eventEntity } from '../event/entities/event.entity';
import * as fs from 'fs';
import * as util from 'util';
import { InformasiUmum } from '../informasi_umum/entities/informasi_umum.entity';
import { Kontak } from '../kontak/entities/kontak.entity';
import { Layanan } from '../layanan/entities/layanan.entity';
import { Rfc235 } from '../rfc235/entities/rfc235.entity';
import { PanduanPedoman } from '../panduan-pedoman/entities/panduan-pedoman.entity';
import { PanduanKeamanan } from '../keamanan/entities/keamanan_panduan.entity';
import { PeringatanKeamanan } from '../keamanan/entities/keamanan_peringatan.entity';
import { MonitoringEntity } from '../monitoring/entities/monitoring.entity';

@Injectable()
export class LandingService {
  constructor(
    @InjectRepository(Kontak) private repositoryKontak: Repository<Kontak>,
    @InjectRepository(eventEntity) private repository: Repository<eventEntity>,
    @InjectRepository(Berita) private repositoryBerita: Repository<Berita>,
    @InjectRepository(Rfc235) private repositoryRFC: Repository<Rfc235>,
    @InjectRepository(Layanan) private repositoryLayanan: Repository<Layanan>,
    @InjectRepository(Artikel) private repositoryArtikel: Repository<Artikel>,
    @InjectRepository(PanduanPedoman)
    private repositoryPanduanPedoman: Repository<PanduanPedoman>,
    @InjectRepository(PanduanKeamanan)
    private repoPanduKeamanan: Repository<PanduanKeamanan>,
    @InjectRepository(PeringatanKeamanan)
    private repoPeriKeamanan: Repository<PeringatanKeamanan>,
    @InjectRepository(InformasiUmum)
    private repositoryInUm: Repository<InformasiUmum>,
    @InjectRepository(MonitoringEntity)
    private repoMonitoring: Repository<MonitoringEntity>,
  ) {}

  async findAllEvent() {
    const data = await this.repository.find({ order: { created_at: 'DESC' } });
    return data;
  }

  async findEventById(id: string) {
    const data = await this.repository.find({ where: { id: id } });
    return data;
  }

  async findAllInformasiUmum() {
    const data = await this.repositoryInUm.find({
      order: { created_at: 'DESC' },
    });
    return data[0];
  }

  async findAllKontak() {
    const data = await this.repositoryKontak.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findKontakById(id: string) {
    const data = await this.repositoryKontak.find({
      where: { id: id },
    });
    return data;
  }

  async findAllBerita() {
    const data = await this.repositoryBerita.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findBeritaById(id: string) {
    const data = await this.repositoryBerita.find({
      where: { id: id },
    });
    return data;
  }

  async findAllArtikel() {
    const data = await this.repositoryArtikel.find({
      order: { tgl_posting: 'DESC' },
    });
    return data;
  }

  async findArtikelById(id: string) {
    const data = await this.repositoryArtikel.find({
      where: { id: id },
    });
    return data;
  }

  async findAllLayanan() {
    const data = await this.repositoryLayanan.find({
      order: { tgl_posting: 'DESC' },
    });
    return data;
  }

  async findLayananById(id: string) {
    const data = await this.repositoryLayanan.find({
      where: { id: id },
    });
    return data;
  }

  async findAllRFC() {
    const data = await this.repositoryRFC.find({
      order: { tgl_posting: 'DESC' },
    });
    return data;
  }

  async findRFCById(id: string) {
    const data = await this.repositoryRFC.find({
      where: { id: id },
    });
    return data;
  }

  async findAllPedoman() {
    const data = await this.repositoryPanduanPedoman.find({
      order: { tanggal: 'DESC' },
    });
    return data;
  }

  async findPedomanyId(id: string) {
    const data = await this.repositoryPanduanPedoman.find({
      where: { id: id },
    });
    return data;
  }

  async findAllPanduanKeamanan() {
    const data = await this.repoPanduKeamanan.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findPanduanKeamananId(id: string) {
    const data = await this.repoPanduKeamanan.findOne({
      where: { id: id },
    });
    return data;
  }

  async findAllPeringatanKeamanan() {
    const data = await this.repoPeriKeamanan.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findPeringatanKeamananId(id: string) {
    const data = await this.repoPeriKeamanan.findOne({
      where: { id: id },
    });
    return data;
  }

  async findAllMonitoring() {
    const data = await this.repoMonitoring.find({
      order: { created_at: 'DESC' },
      relations: ['tahun'],
    });
    return data;
  }

  async findAllMonitoringByBulan(bulan: any) {
    const data = await this.repoMonitoring.find({
      order: { created_at: 'DESC' },
    });
    return data;
  }
  async findAllMonitoringByTahun(tahun: any) {
    const data = await this.repoMonitoring.find({
      where: { tahun: tahun },
      order: { created_at: 'DESC' },
    });
    return data;
  }

  async findAllMonitoringDropdownTahun() {
    const data = await this.repoMonitoring
      .createQueryBuilder('monitoring') // Replace 'entityAlias' with your entity alias
      .select('monitoring.tahun', 'tahun') // Specify the columns you want to select
      .groupBy('monitoring.tahun') // Group by the "tahun" column
      .getRawMany();

    return data;
  }
  async findAllMonitoringDropdownBulan() {
    const data = await this.repoMonitoring
      .createQueryBuilder('monitoring') // Replace 'entityAlias' with your entity alias
      .select('monitoring.bulan', 'bulan') // Specify the columns you want to select
      .groupBy('monitoring.bulan') // Group by the "tahun" column
      .getRawMany();

    return data;
  }

  async findPMonitoringId(id: string) {
    const data = await this.repoMonitoring.findOne({
      where: { id: id },
      relations: ['tahun'],
    });
    return data;
  }

  async readFileAsBase64(filePath: string): Promise<string> {
    const readFile = util.promisify(fs.readFile);
    try {
      const fileContent = await readFile(filePath);
      const base64Content = fileContent.toString('base64');
      return base64Content;
    } catch (error) {
      throw new Error(`Error reading file: ${error.message}`);
    }
  }
}
