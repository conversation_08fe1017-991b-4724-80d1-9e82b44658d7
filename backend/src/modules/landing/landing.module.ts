import { Module } from '@nestjs/common';
import { LandingService } from './landing.service';
import { LandingController } from './landing.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InformasiUmum } from '../informasi_umum/entities/informasi_umum.entity';
import { Artikel } from '../artikel/entities/artikel.entity';
import { Kontak } from '../kontak/entities/kontak.entity';
import { Berita } from '../berita/entities/berita.entity';
import { Layanan } from '../layanan/entities/layanan.entity';
import { Rfc235 } from '../rfc235/entities/rfc235.entity';
import { Event } from '../event/entities/event.entity';
import { PanduanPedoman } from '../panduan-pedoman/entities/panduan-pedoman.entity';
import { PeringatanKeamanan } from '../keamanan/entities/keamanan_peringatan.entity';
import { PanduanKeamanan } from '../keamanan/entities/keamanan_panduan.entity';
import { MonitoringEntity } from '../monitoring/entities/monitoring.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Event,
      InformasiUmum,
      Artikel,
      Kontak,
      Berita,
      Layanan,
      Rfc235,
      PanduanKeamanan,
      MonitoringEntity,
      PeringatanKeamanan,
      PanduanPedoman,
    ]),
  ],
  controllers: [LandingController],
  providers: [LandingService],
})
export class LandingModule {}
