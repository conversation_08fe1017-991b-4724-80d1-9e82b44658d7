import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { N8nIntegrationService } from '../n8n-integration.service';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';

@Injectable()
export class N8nAuthGuard implements CanActivate {
  private readonly logger = new Logger(N8nAuthGuard.name);
  private readonly n8nIncomingWebhookSecret: string | undefined;
  private readonly isN8nIntegrationEnabled: boolean;

  constructor(
    private n8nIntegrationService: N8nIntegrationService,
    private configService: ConfigService,
  ) {
    this.n8nIncomingWebhookSecret = this.configService.get<string>(
      'N8N_INCOMING_WEBHOOK_SECRET',
    );
    this.isN8nIntegrationEnabled = this.configService.get<boolean>(
      'ENABLE_N8N_INTEGRATION',
      false,
    );

    if (this.isN8nIntegrationEnabled && !this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8N_INCOMING_WEBHOOK_SECRET is not set while N8N integration is enabled. ' +
          'Incoming webhooks from N8N cannot be securely validated. THIS IS A SECURITY RISK.',
      );
    }
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    if (!this.isN8nIntegrationEnabled) {
      this.logger.warn(
        'N8NAuthGuard: N8N integration is disabled. Denying access to N8N webhook endpoint.',
      );
      throw new HttpException(
        'N8N Integration is disabled.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    if (!this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8NAuthGuard: N8N_INCOMING_WEBHOOK_SECRET is not configured. ' +
          'Allowing request without signature validation (INSECURE). Endpoint should be protected otherwise.',
      );
      return true; // Allow if secret not set, but this is a security hole.
    }

    const request = context.switchToHttp().getRequest();
    const signatureFromHeader =
      request.headers['x-n8n-signature'] ||
      request.headers['x-webhook-signature'];

    if (!signatureFromHeader) {
      this.logger.warn(
        'N8NAuthGuard: Missing webhook signature header (e.g., x-n8n-signature) from incoming N8N request.',
      );
      throw new HttpException('Missing signature', HttpStatus.UNAUTHORIZED);
    }

    // Access raw body for signature validation
    const rawBody = request.rawBody;
    if (!rawBody) {
      this.logger.error(
        'N8NAuthGuard: Raw request body not available for N8N signature validation. ' +
          'Ensure `rawBody: true` is set for the JSON body parser in `main.ts` or that `bodyParser` is configured to expose it.',
      );
      throw new HttpException(
        'Internal server error during N8N authentication (raw body missing)',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const payloadString = rawBody.toString('utf8');
    const isValid = this.n8nIntegrationService.validateIncomingWebhookSignature(
      payloadString,
      signatureFromHeader as string,
    );

    if (!isValid) {
      this.logger.warn('N8NAuthGuard: Invalid N8N webhook signature.');
      throw new HttpException('Invalid signature', HttpStatus.FORBIDDEN);
    }

    this.logger.debug(
      'N8NAuthGuard: Valid N8N webhook signature. Request allowed.',
    );
    return true;
  }
}
