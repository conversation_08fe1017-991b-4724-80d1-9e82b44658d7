import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { N8nIntegrationService } from './n8n-integration.service';
import { N8nWebhookController } from './controllers/n8n-webhook.controller';
import { N8nAuthGuard } from './guards/n8n-auth.guard';
import { ChatModule } from '../chat/chat.module';

@Module({
  imports: [
    ConfigModule,
    HttpModule.register({
      // Configure HttpModule for N8nIntegrationService
      timeout: 10000, // Default timeout for N8N calls
      maxRedirects: 3,
    }),
    // Use forwardRef to handle circular dependency between N8nIntegrationModule and ChatModule
    forwardRef(() => ChatModule),
  ],
  controllers: [N8nWebhookController],
  providers: [N8nIntegrationService, N8nAuthGuard],
  exports: [N8nIntegrationService], // Export if other modules need to call N8N directly
})
export class N8nIntegrationModule {}
