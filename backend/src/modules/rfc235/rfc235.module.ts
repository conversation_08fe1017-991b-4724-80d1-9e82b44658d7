import { Module } from '@nestjs/common';
import { Rfc235Service } from './rfc235.service';
import { Rfc235Controller } from './rfc235.controller';
import { Rfc235 } from './entities/rfc235.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { FileAccessService } from 'src/common/services/file-access.service';
import { AuditService } from 'src/common/services/audit.service';

@Module({
  imports: [TypeOrmModule.forFeature([Rfc235])],
  controllers: [Rfc235Controller],
  providers: [Rfc235Service, FileService, FileAccessService, AuditService],
})
export class Rfc235Module {}
