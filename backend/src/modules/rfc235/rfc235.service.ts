import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as util from 'util';
import { CreateRfc235Dto } from './dto/create-rfc235.dto';
import { UpdateRfc235Dto } from './dto/update-rfc235.dto';
import { Rfc235 } from './entities/rfc235.entity';

@Injectable()
export class Rfc235Service {
  private readonly logger = new Logger(Rfc235Service.name);

  constructor(
    @InjectRepository(Rfc235)
    private repository: Repository<Rfc235>,
  ) {}
  create(createRfc235Dto: CreateRfc235Dto, foto: any) {
    const request = new Rfc235();
    request.kategori = createRfc235Dto.kategori;
    request.judul = createRfc235Dto.judul;
    request.isi = createRfc235Dto.isi;
    request.tgl_posting = createRfc235Dto.tgl_posting;
    if (foto) {
      request.file_pdf = foto;
    } else {
      request.file_pdf = '';
    }
    const result = this.repository.insert(request);
    return result;
  }

  async findAll() {
    const data = await this.repository.find();
    return data;
  }

  async findOne(id) {
    const data = await this.repository.findOne({ where: { id: id } });
    return data;
  }

  async readFileAsBase64(filePath: string): Promise<string> {
    const readFile = util.promisify(fs.readFile);
    try {
      const fileContent = await readFile(filePath);
      const base64Content = fileContent.toString('base64');
      return base64Content;
    } catch (error) {
      throw new Error(`Error reading file: ${error.message}`);
    }
  }

  async getByLanguage(body: any) {
    const data = await this.repository.find({
      where: { kategori: body.kategori },
    });
    return data;
  }

  async update(id, data: UpdateRfc235Dto, foto: any, id_user: string) {
    try {
      const dataa = await this.repository.findOne({ where: { id: id } });

      const request = new Rfc235();
      request.kategori = data.kategori;
      request.judul = data.judul;
      request.isi = data.isi;
      request.tgl_posting = data.tgl_posting;
      if (foto) {
        // fs.unlink('uploads/rfc/' + dataa.file_pdf, (e) => {});
        request.file_pdf = foto;
      }

      const result = this.repository.update(id, request);
      return result;
    } catch (error) {
      console.log(error);
      return;
    }
  }

  async remove(id: string) {
    try {
      const dataa = await this.repository.findOne({ where: { id: id } });
      fs.unlink('uploads/rfc/' + dataa.file_pdf, (e) => {
        console.log(e);
      });
      const data = await this.repository.delete(id);
    } catch (error) {}
    return;
  }

  /**
   * Check if a user has access to a specific RFC file
   * @param fileId - The RFC file ID
   * @param userId - The user ID requesting access
   * @returns Promise<boolean> - true if access is granted
   */
  async checkFileAccess(fileId: string, userId: string): Promise<boolean> {
    try {
      // For now, this is a placeholder that grants access to all authenticated users
      // In a real implementation, you would check:
      // 1. If the file exists
      // 2. User permissions/roles
      // 3. File visibility settings
      // 4. Organization/department access rules

      const file = await this.findOne(fileId);
      if (!file) {
        this.logger.warn(`File access denied: RFC file ${fileId} not found`);
        return false;
      }

      this.logger.log(
        `File access granted: User ${userId} accessing RFC file ${fileId}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error checking file access for RFC ${fileId} by user ${userId}:`,
        error,
      );
      return false;
    }
  }
}
