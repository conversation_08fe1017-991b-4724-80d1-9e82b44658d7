import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'rfc_file' })
export class Rfc235 {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  file_pdf: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  isi: string;

  @Column()
  judul: string;

  @Column()
  kategori: string;

  @Column({
    // type: 'datetime'
  })
  tgl_posting: Date;

  @Column({
    nullable: true,
  })
  created_at?: Date;

  @Column({
    nullable: true,
  })
  updated_at?: Date;

  @BeforeUpdate()
  updateDate() {
    this.updated_at = new Date();
  }

  @BeforeInsert()
  createDate() {
    this.created_at = new Date();
  }
}
