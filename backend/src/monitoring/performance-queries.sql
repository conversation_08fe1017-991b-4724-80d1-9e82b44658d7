-- 📊 PostgreSQL Performance Monitoring Queries
-- Use these queries to monitor database performance after optimization

-- ============================================================================
-- 🔍 INDEX USAGE ANALYSIS
-- ============================================================================

-- Check index usage efficiency
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        ELSE 'GOOD_USAGE'
    END as usage_status
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Find unused indexes (candidates for removal)
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND idx_scan = 0
    AND indexrelid IS NOT NULL
ORDER BY pg_relation_size(indexrelid) DESC;

-- ============================================================================
-- 🐌 SLOW QUERY ANALYSIS (requires pg_stat_statements)
-- ============================================================================

-- Top 10 slowest queries by average execution time
SELECT 
    query,
    calls,
    total_exec_time,
    mean_exec_time,
    stddev_exec_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;

-- Queries with high total execution time
SELECT 
    query,
    calls,
    total_exec_time,
    mean_exec_time,
    (total_exec_time/sum(total_exec_time) OVER()) * 100 as percentage_total
FROM pg_stat_statements 
ORDER BY total_exec_time DESC 
LIMIT 10;

-- ============================================================================
-- 📊 TABLE STATISTICS
-- ============================================================================

-- Table sizes and activity
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    seq_scan as sequential_scans,
    idx_scan as index_scans,
    CASE 
        WHEN seq_scan > idx_scan AND seq_scan > 100 THEN 'NEEDS_INDEX'
        WHEN idx_scan > seq_scan THEN 'GOOD_INDEX_USAGE'
        ELSE 'LOW_ACTIVITY'
    END as scan_analysis
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ============================================================================
-- 🔗 CONNECTION MONITORING
-- ============================================================================

-- Current database connections
SELECT 
    datname,
    state,
    COUNT(*) as connection_count,
    MAX(now() - state_change) as max_duration
FROM pg_stat_activity 
WHERE datname = current_database()
GROUP BY datname, state
ORDER BY connection_count DESC;

-- Long-running queries
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
    AND state != 'idle'
ORDER BY duration DESC;

-- ============================================================================
-- 💾 CACHE PERFORMANCE
-- ============================================================================

-- Buffer cache hit ratio (should be > 95%)
SELECT 
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    CASE 
        WHEN heap_blks_read + heap_blks_hit = 0 THEN 0
        ELSE ROUND((heap_blks_hit::numeric / (heap_blks_read + heap_blks_hit)) * 100, 2)
    END as cache_hit_ratio
FROM pg_statio_user_tables 
WHERE schemaname = 'public'
    AND (heap_blks_read + heap_blks_hit) > 0
ORDER BY cache_hit_ratio ASC;

-- Overall cache hit ratio
SELECT 
    'Database Cache Hit Ratio' as metric,
    ROUND(
        (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 
        2
    ) as percentage
FROM pg_statio_user_tables;

-- ============================================================================
-- 🎯 CONTENT PERFORMANCE ANALYSIS
-- ============================================================================

-- Content table performance (berita, artikel, layanan)
WITH content_performance AS (
    SELECT 
        'berita' as table_name,
        COUNT(*) as record_count,
        pg_size_pretty(pg_total_relation_size('berita')) as size,
        (SELECT COUNT(*) FROM pg_stat_user_indexes WHERE tablename = 'berita') as index_count,
        (SELECT seq_scan FROM pg_stat_user_tables WHERE tablename = 'berita') as seq_scans,
        (SELECT idx_scan FROM pg_stat_user_tables WHERE tablename = 'berita') as idx_scans
    FROM berita
    
    UNION ALL
    
    SELECT 
        'artikel' as table_name,
        COUNT(*) as record_count,
        pg_size_pretty(pg_total_relation_size('artikel')) as size,
        (SELECT COUNT(*) FROM pg_stat_user_indexes WHERE tablename = 'artikel') as index_count,
        (SELECT seq_scan FROM pg_stat_user_tables WHERE tablename = 'artikel') as seq_scans,
        (SELECT idx_scan FROM pg_stat_user_tables WHERE tablename = 'artikel') as idx_scans
    FROM artikel
    
    UNION ALL
    
    SELECT 
        'layanan' as table_name,
        COUNT(*) as record_count,
        pg_size_pretty(pg_total_relation_size('layanan')) as size,
        (SELECT COUNT(*) FROM pg_stat_user_indexes WHERE tablename = 'layanan') as index_count,
        (SELECT seq_scan FROM pg_stat_user_tables WHERE tablename = 'layanan') as seq_scans,
        (SELECT idx_scan FROM pg_stat_user_tables WHERE tablename = 'layanan') as idx_scans
    FROM layanan
)
SELECT 
    table_name,
    record_count,
    size,
    index_count,
    seq_scans,
    idx_scans,
    CASE 
        WHEN idx_scans > seq_scans THEN 'OPTIMIZED'
        WHEN seq_scans > idx_scans AND seq_scans > 100 THEN 'NEEDS_OPTIMIZATION'
        ELSE 'LOW_ACTIVITY'
    END as performance_status
FROM content_performance;

-- ============================================================================
-- 💬 CHAT SYSTEM PERFORMANCE
-- ============================================================================

-- Chat system metrics
SELECT 
    'Active Chat Rooms' as metric,
    COUNT(*) as value
FROM chat_rooms 
WHERE status = 'active'

UNION ALL

SELECT 
    'Messages Last Hour' as metric,
    COUNT(*) as value
FROM chat_messages 
WHERE created_at > NOW() - INTERVAL '1 hour'

UNION ALL

SELECT 
    'Active Chat Users' as metric,
    COUNT(*) as value
FROM chat_users 
WHERE is_active = true

UNION ALL

SELECT 
    'Total Messages Today' as metric,
    COUNT(*) as value
FROM chat_messages 
WHERE DATE(created_at) = CURRENT_DATE;

-- ============================================================================
-- 🚨 PERFORMANCE ALERTS
-- ============================================================================

-- Generate performance alerts
WITH performance_checks AS (
    -- Check 1: High sequential scans
    SELECT 
        'HIGH_SEQ_SCANS' as alert_type,
        tablename as details,
        seq_scan as value
    FROM pg_stat_user_tables 
    WHERE schemaname = 'public' 
        AND seq_scan > idx_scan 
        AND seq_scan > 100
    
    UNION ALL
    
    -- Check 2: Low cache hit ratio
    SELECT 
        'LOW_CACHE_HIT' as alert_type,
        tablename as details,
        ROUND((heap_blks_hit::numeric / (heap_blks_read + heap_blks_hit)) * 100, 2) as value
    FROM pg_statio_user_tables 
    WHERE schemaname = 'public'
        AND (heap_blks_read + heap_blks_hit) > 0
        AND (heap_blks_hit::numeric / (heap_blks_read + heap_blks_hit)) < 0.95
    
    UNION ALL
    
    -- Check 3: Unused indexes
    SELECT 
        'UNUSED_INDEX' as alert_type,
        indexname as details,
        0 as value
    FROM pg_stat_user_indexes 
    WHERE schemaname = 'public' 
        AND idx_scan = 0
)
SELECT 
    alert_type,
    details,
    value,
    CASE 
        WHEN alert_type = 'HIGH_SEQ_SCANS' THEN 'Consider adding indexes to reduce sequential scans'
        WHEN alert_type = 'LOW_CACHE_HIT' THEN 'Consider increasing shared_buffers or investigating query patterns'
        WHEN alert_type = 'UNUSED_INDEX' THEN 'Consider dropping unused index to save space'
    END as recommendation
FROM performance_checks
ORDER BY alert_type, value DESC;
