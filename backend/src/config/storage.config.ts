import { join } from 'path';

/**
 * Storage configuration for secure file handling
 * This configuration supports moving files outside the webroot for enhanced security
 */
export class StorageConfig {
  /**
   * Secure upload path outside the webroot
   * Default: /var/app/secure-files/
   * Can be overridden by SECURE_UPLOAD_PATH environment variable
   */
  static readonly SECURE_UPLOAD_PATH =
    process.env.SECURE_UPLOAD_PATH || '/var/app/secure-files/';

  /**
   * Legacy uploads directory (to be phased out)
   * Used for reference during migration process
   */
  static readonly LEGACY_UPLOADS_PATH = join(process.cwd(), 'uploads');

  /**
   * Maximum file size in bytes (default: 10MB)
   */
  static readonly MAX_FILE_SIZE = parseInt(
    process.env.MAX_FILE_SIZE || '10485760',
  );

  /**
   * Allowed file extensions for uploads
   */
  static readonly ALLOWED_EXTENSIONS = [
    '.pdf',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.svg',
    '.txt',
    '.csv',
    '.zip',
    '.rar',
  ];

  /**
   * Generate a secure filename using UUID
   * @param originalName Original filename
   * @returns Secure filename with UUID
   */
  static generateSecureFilename(originalName: string): string {
    const extension = originalName.substring(originalName.lastIndexOf('.'));
    const uuid = require('crypto').randomUUID();
    return `${uuid}${extension}`;
  }

  /**
   * Get the full secure file path
   * @param filename Secure filename
   * @returns Full path to the secure file
   */
  static getSecureFilePath(filename: string): string {
    return join(this.SECURE_UPLOAD_PATH, filename);
  }
}

export default StorageConfig;
