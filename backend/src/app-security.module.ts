/**
 * Comprehensive Security Module
 * Implements OWASP security standards across the application
 */

import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { APP_GUARD, APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Security Guards
import { JwtAuthGuard } from './common/guards/jwt-auth.guard';
import { RateLimitGuard } from './common/guards/rate-limit.guard';
import { CsrfGuard } from './common/guards/csrf.guard';

// Security Middleware
import { SecurityHeadersMiddleware } from './common/middleware/security-headers.middleware';
import { ApiSecurityMiddleware } from './common/middleware/api-security.middleware';

// Security Filters & Interceptors
import { SecureExceptionFilter } from './common/filters/secure-exception.filter';
import { SecureFileUploadInterceptor } from './common/interceptors/file-upload.interceptor';

// Security Services
import { SecureEncryptionService } from './common/services/encryption.service';
import { JwtConfigService } from './common/config/jwt.config';

@Module({
  imports: [
    // Rate limiting configuration
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        throttlers: [
          {
            name: 'default',
            ttl: configService.get<number>('THROTTLE_TTL', 60) * 1000, // Convert to milliseconds
            limit: configService.get<number>('THROTTLE_LIMIT', 10), // 10 requests per TTL
          },
        ],
        storage: undefined, // Use in-memory storage (consider Redis for production)
      }),
    }),
  ],
  providers: [
    // Security Services
    SecureEncryptionService,
    JwtConfigService,

    // Global Guards
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RateLimitGuard,
    },
    {
      provide: APP_GUARD,
      useClass: CsrfGuard,
    },

    // Global Exception Filter
    {
      provide: APP_FILTER,
      useClass: SecureExceptionFilter,
    },

    // Global Interceptors
    {
      provide: APP_INTERCEPTOR,
      useClass: SecureFileUploadInterceptor,
    },
  ],
  exports: [
    SecureEncryptionService,
    JwtConfigService,
  ],
})
export class AppSecurityModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply security middleware to all routes
    consumer
      .apply(SecurityHeadersMiddleware, ApiSecurityMiddleware)
      .forRoutes('*');
  }
}

/**
 * Security Configuration Validation Schema
 */
export const securityConfigSchema = {
  // JWT Configuration
  JWT_ACCESS_TOKEN_SECRET: {
    required: true,
    minLength: 64,
    description: 'JWT access token secret (64+ hex characters)',
  },
  JWT_REFRESH_TOKEN_SECRET: {
    required: true,
    minLength: 64,
    description: 'JWT refresh token secret (64+ hex characters)',
  },
  JWT_ACCESS_TOKEN_EXPIRATION_TIME: {
    required: true,
    default: '900', // 15 minutes
    description: 'JWT access token expiration in seconds',
  },
  JWT_REFRESH_TOKEN_EXPIRATION_TIME: {
    required: true,
    default: '604800', // 7 days
    description: 'JWT refresh token expiration in seconds',
  },

  // Encryption Configuration
  ENCRYPTION_KEY: {
    required: true,
    minLength: 64,
    description: 'AES-256 encryption key (64 hex characters)',
  },

  // Database Security
  DB_SSL: {
    required: false,
    default: 'true',
    description: 'Enable SSL for database connections',
  },
  DB_SSL_REJECT_UNAUTHORIZED: {
    required: false,
    default: 'true',
    description: 'Reject unauthorized SSL certificates',
  },

  // CORS Configuration
  ALLOWED_ORIGINS: {
    required: true,
    default: 'http://localhost:3000,http://localhost:5173',
    description: 'Comma-separated list of allowed origins',
  },

  // Rate Limiting
  THROTTLE_TTL: {
    required: false,
    default: '60',
    description: 'Rate limit time window in seconds',
  },
  THROTTLE_LIMIT: {
    required: false,
    default: '100',
    description: 'Maximum requests per time window',
  },

  // Session Configuration
  SESSION_SECRET: {
    required: true,
    minLength: 32,
    description: 'Session secret for CSRF protection',
  },

  // File Upload Security
  MAX_FILE_SIZE: {
    required: false,
    default: '10485760', // 10MB
    description: 'Maximum file upload size in bytes',
  },
  ALLOWED_FILE_TYPES: {
    required: false,
    default: 'jpg,jpeg,png,gif,pdf,txt',
    description: 'Comma-separated list of allowed file extensions',
  },

  // Security Headers
  ENABLE_HSTS: {
    required: false,
    default: 'true',
    description: 'Enable HTTP Strict Transport Security',
  },
  ENABLE_CSP: {
    required: false,
    default: 'true',
    description: 'Enable Content Security Policy',
  },

  // Logging
  LOG_LEVEL: {
    required: false,
    default: 'info',
    description: 'Application log level (error, warn, info, debug)',
  },
  ENABLE_SECURITY_LOGGING: {
    required: false,
    default: 'true',
    description: 'Enable security event logging',
  },
};

/**
 * Security Environment Validation
 */
export function validateSecurityEnvironment(): void {
  const errors: string[] = [];

  Object.entries(securityConfigSchema).forEach(([key, config]) => {
    const value = process.env[key];

    if (config.required && !value) {
      errors.push(`${key} is required but not provided`);
      return;
    }

    if (value && 'minLength' in config && config.minLength && value.length < config.minLength) {
      errors.push(`${key} must be at least ${config.minLength} characters long`);
    }
  });

  if (errors.length > 0) {
    console.error('Security Configuration Errors:');
    errors.forEach(error => console.error(`  - ${error}`));
    throw new Error('Security configuration validation failed');
  }

  console.log('✅ Security configuration validation passed');
}

/**
 * Generate secure environment template
 */
export function generateSecureEnvTemplate(): string {
  const template = Object.entries(securityConfigSchema)
    .map(([key, config]) => {
      const comment = `# ${config.description}`;
      const value = ('default' in config ? config.default : null) || (key.includes('SECRET') || key.includes('KEY') ?
        '<GENERATE_SECURE_VALUE>' : '<SET_VALUE>');
      return `${comment}\n${key}=${value}`;
    })
    .join('\n\n');

  return `# Security Configuration Template
# Generated on ${new Date().toISOString()}
# 
# IMPORTANT: Replace all placeholder values with actual secure values
# Use the following commands to generate secure values:
# - For secrets/keys: openssl rand -hex 32
# - For JWT secrets: openssl rand -hex 64

${template}

# Additional Security Recommendations:
# 1. Use environment-specific configuration files
# 2. Never commit secrets to version control
# 3. Use a secrets management service in production
# 4. Regularly rotate secrets and keys
# 5. Monitor for security events and anomalies
# 6. Keep dependencies updated
# 7. Perform regular security audits
`;
}

/**
 * Security Health Check
 */
export class SecurityHealthCheck {
  static async performSecurityCheck(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    checks: Array<{ name: string; status: 'pass' | 'fail'; message: string }>;
  }> {
    const checks = [];

    // Check environment configuration
    try {
      validateSecurityEnvironment();
      checks.push({
        name: 'Environment Configuration',
        status: 'pass' as const,
        message: 'All required security environment variables are configured',
      });
    } catch (error) {
      checks.push({
        name: 'Environment Configuration',
        status: 'fail' as const,
        message: error.message,
      });
    }

    // Check JWT secret strength
    const jwtSecret = process.env.JWT_ACCESS_TOKEN_SECRET;
    if (jwtSecret && jwtSecret.length >= 64) {
      checks.push({
        name: 'JWT Secret Strength',
        status: 'pass' as const,
        message: 'JWT secret meets minimum length requirements',
      });
    } else {
      checks.push({
        name: 'JWT Secret Strength',
        status: 'fail' as const,
        message: 'JWT secret is too weak or missing',
      });
    }

    // Check encryption key
    const encryptionKey = process.env.ENCRYPTION_KEY;
    if (encryptionKey && encryptionKey.length >= 64) {
      checks.push({
        name: 'Encryption Key',
        status: 'pass' as const,
        message: 'Encryption key meets minimum requirements',
      });
    } else {
      checks.push({
        name: 'Encryption Key',
        status: 'fail' as const,
        message: 'Encryption key is too weak or missing',
      });
    }

    // Determine overall status
    const failedChecks = checks.filter(check => check.status === 'fail');
    let status: 'healthy' | 'warning' | 'critical';

    if (failedChecks.length === 0) {
      status = 'healthy';
    } else if (failedChecks.length <= 2) {
      status = 'warning';
    } else {
      status = 'critical';
    }

    return { status, checks };
  }
}
