import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import 'dotenv/config';
import * as Jo<PERSON> from 'joi';
import { ConfigModule } from '@nestjs/config';
import { BeritaModule } from './modules/berita/berita.module';
import { UserModule } from './modules/user/user.module';
import { AuthModule } from './modules/auth/auth.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './common/guards/jwt-auth.guard';
import { MenuModule } from './modules/menu/menu.module';
import { HealthModule } from './modules/health/health.module';
import { ArtikelModule } from './modules/artikel/artikel.module';
import { EventModule } from './modules/event/event.module';
import { LayananModule } from './modules/layanan/layanan.module';
import { KontakModule } from './modules/kontak/kontak.module';
import { Rfc235Module } from './modules/rfc235/rfc235.module';
import { InformasiUmumModule } from './modules/informasi_umum/informasi_umum.module';
import { LandingModule } from './modules/landing/landing.module';
import { PanduanPedomanModule } from './modules/panduan-pedoman/panduan-pedoman.module';

import { KeamananModule } from './modules/keamanan/keamanan.module';
import { MonitoringModule } from './modules/monitoring/monitoring.module';
import { BulanTahunModule } from './modules/bulan_tahun/bulan_tahun.module';
import { MasterProgresTicketModule } from './modules/master_progres_ticket/master_progres_ticket.module';
import { TicketingModule } from './modules/ticketing/ticketing.module';
import { dataSourceOptions } from './source';
import { MasterRoleModule } from './modules/master_role/master_role.module';
import { ChatModule } from './modules/chat/chat.module';
import { CommonAppModule } from './common/common-app.module';
import { N8nIntegrationModule } from './modules/n8n-integration/n8n-integration.module';
import { WinstonLogger } from './utils/globalFunction/writelogger';
import { UserActivityLoggerMiddleware } from './utils/user-activity-logger.middleware';
import { CsrfMiddleware } from './common/middleware/csrf.middleware';
import { CsrfController } from './common/controllers/csrf.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        DB_HOST: Joi.string().required(),
        DB_PORT: Joi.string().required(),
        DB_USER: Joi.string().required(),
        // DB_PASS:Joi.string().required(),
        DB_NAME: Joi.string().required(),
        JWT_ACCESS_TOKEN_SECRET: Joi.string().required(),
        JWT_ACCESS_TOKEN_EXPIRATION_TIME: Joi.string().required(),
        JWT_REFRESH_TOKEN_SECRET: Joi.string().required(),
        JWT_REFRESH_TOKEN_EXPIRATION_TIME: Joi.string().required(),
        JWT_ACCESS_COOKIE: Joi.boolean().required(),
        // ...
      }),
    }),

    TypeOrmModule.forRoot({
      // type: 'postgres',
      // host: process.env.DB_HOST,
      // port: parseInt(process.env.DB_PORT),
      // username: process.env.DB_USER,
      // password: process.env.DB_PASS,
      // database: process.env.DB_NAME,
      ...dataSourceOptions,

      entities: ['dist/**/*.entity{.ts,.js}'],
      synchronize: true,
    }),
    BeritaModule,

    UserModule,
    AuthModule,

    MenuModule,

    HealthModule,

    ArtikelModule,

    EventModule,

    LayananModule,

    KontakModule,

    Rfc235Module,

    InformasiUmumModule,

    LandingModule,

    PanduanPedomanModule,

    KeamananModule,

    MonitoringModule,

    BulanTahunModule,

    MasterProgresTicketModule,

    TicketingModule,

    MasterRoleModule,

    CommonAppModule, // Global module for OfficeHoursService

    ChatModule,

    N8nIntegrationModule, // N8N integration module
  ],
  controllers: [AppController, CsrfController],
  exports: ['LoggerService'],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: 'LoggerService',
      useClass: WinstonLogger,
    },
  ],
})
// export class AppModule {}
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply CSRF protection first
    consumer
      .apply(CsrfMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });

    // Then apply user activity logging
    consumer
      .apply(UserActivityLoggerMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
