import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFullTextSearchIndexes1735000005000 implements MigrationInterface {
  name = 'AddFullTextSearchIndexes1735000005000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🚀 Starting Migration 005: Full-Text Search Indexes');
    
    try {
      // 🔍 ENABLE FULL-TEXT SEARCH EXTENSIONS
      console.log('Enabling full-text search extensions...');
      
      // Enable unaccent extension for better Indonesian text search
      await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS unaccent`);
      
      // Enable pg_trgm for similarity search
      await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS pg_trgm`);

      // 📰 BERITA FULL-TEXT SEARCH
      console.log('Creating berita full-text search indexes...');
      
      // Indonesian language full-text search
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_search_indonesian 
        ON berita USING gin(to_tsvector('indonesian', judul || ' ' || isi))
      `);
      
      // Simple text search (without unaccent for compatibility)
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_search_simple
        ON berita USING gin(to_tsvector('simple', judul || ' ' || isi))
      `);
      
      // Trigram search for fuzzy matching
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_trigram 
        ON berita USING gin((judul || ' ' || isi) gin_trgm_ops)
      `);

      // 📄 ARTIKEL FULL-TEXT SEARCH
      console.log('Creating artikel full-text search indexes...');
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_search_indonesian 
        ON artikel USING gin(to_tsvector('indonesian', judul || ' ' || isi))
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_search_simple
        ON artikel USING gin(to_tsvector('simple', judul || ' ' || isi))
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_trigram 
        ON artikel USING gin((judul || ' ' || isi) gin_trgm_ops)
      `);

      // 🛠️ LAYANAN FULL-TEXT SEARCH
      console.log('Creating layanan full-text search indexes...');
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_layanan_search_indonesian 
        ON layanan USING gin(to_tsvector('indonesian', judul || ' ' || isi))
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_layanan_search_simple
        ON layanan USING gin(to_tsvector('simple', judul || ' ' || isi))
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_layanan_trigram 
        ON layanan USING gin((judul || ' ' || isi) gin_trgm_ops)
      `);

      // 🔒 SECURITY CONTENT SEARCH
      console.log('Creating security content search indexes...');
      
      // Peringatan Keamanan search
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_peringatan_keamanan_search 
        ON peringatan_keamanan USING gin(to_tsvector('indonesian', judul || ' ' || COALESCE(isi, '')))
      `);
      
      // RFC File search
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_rfc_file_search
        ON rfc_file USING gin(to_tsvector('indonesian', judul || ' ' || COALESCE(isi, '')))
      `);

      // 📋 TITLE-ONLY SEARCH INDEXES (Faster for autocomplete)
      console.log('Creating title-only search indexes...');
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_title_trigram 
        ON berita USING gin(judul gin_trgm_ops)
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_title_trigram 
        ON artikel USING gin(judul gin_trgm_ops)
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_layanan_title_trigram 
        ON layanan USING gin(judul gin_trgm_ops)
      `);

      // 🎯 CATEGORY SEARCH OPTIMIZATION
      console.log('Creating category search indexes...');
      
      // Category trigram search for fuzzy category matching
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_berita_kategori_trigram 
        ON berita USING gin(kategori gin_trgm_ops)
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_artikel_kategori_trigram 
        ON artikel USING gin(kategori gin_trgm_ops)
      `);
      
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_layanan_kategori_trigram 
        ON layanan USING gin(kategori gin_trgm_ops)
      `);

      // 💬 CHAT CONTENT SEARCH (For message search)
      console.log('Creating chat content search indexes...');
      
      // Chat message content search (encrypted content will need special handling)
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_content_trigram 
        ON chat_messages USING gin(content gin_trgm_ops)
      `);

      console.log('✅ Migration 005 completed successfully');
      console.log('📝 Note: Full-text search is now available for content queries');
      
    } catch (error) {
      console.error('❌ Migration 005 failed:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Rolling back Migration 005: Full-Text Search Indexes');
    
    try {
      const indexesToDrop = [
        'idx_chat_messages_content_trigram',
        'idx_layanan_kategori_trigram',
        'idx_artikel_kategori_trigram',
        'idx_berita_kategori_trigram',
        'idx_layanan_title_trigram',
        'idx_artikel_title_trigram',
        'idx_berita_title_trigram',
        'idx_rfc_file_search',
        'idx_peringatan_keamanan_search',
        'idx_layanan_trigram',
        'idx_layanan_search_simple',
        'idx_layanan_search_indonesian',
        'idx_artikel_trigram',
        'idx_artikel_search_simple',
        'idx_artikel_search_indonesian',
        'idx_berita_trigram',
        'idx_berita_search_simple',
        'idx_berita_search_indonesian',
      ];

      for (const indexName of indexesToDrop) {
        await queryRunner.query(`DROP INDEX IF EXISTS ${indexName}`);
      }

      console.log('✅ Migration 005 rollback completed');
      
    } catch (error) {
      console.error('❌ Migration 005 rollback failed:', error);
      throw error;
    }
  }
}
