import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateChatTables1733286000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create chat_users table
    await queryRunner.query(`
      CREATE TABLE chat_users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VA<PERSON><PERSON><PERSON>(255) UNIQUE,
        phone VARCHAR(50) UNIQUE,
        name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
        password_hash VARCHAR(255) DEFAULT '',
        verification_status VARCHAR(20) DEFAULT 'guest' CHECK (verification_status IN ('guest', 'pending', 'verified')),
        is_active BOOLEAN DEFAULT true,
        last_seen TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create chat_rooms table
    await queryRunner.query(`
      CREATE TABLE chat_rooms (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        type VARCHAR(20) DEFAULT 'user-operator' CHECK (type IN ('user-operator', 'group', 'system')),
        title VARCHAR(255),
        status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'archived')),
        last_message_at TIMESTAMP,
        closed_at TIMESTAMP,
        closed_by_type VARCHAR(50),
        closed_by_id UUID,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create chat_room_participants table
    await queryRunner.query(`
      CREATE TABLE chat_room_participants (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        chat_room_id UUID NOT NULL,
        participant_type VARCHAR(20) NOT NULL CHECK (participant_type IN ('guest_user', 'system_user')),
        guest_user_id UUID,
        system_user_id UUID,
        status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'left', 'kicked')),
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        left_at TIMESTAMP,
        last_read_message_id UUID,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (guest_user_id) REFERENCES chat_users(id) ON DELETE CASCADE,
        FOREIGN KEY (system_user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    // Create chat_messages table
    await queryRunner.query(`
      CREATE TABLE chat_messages (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        chat_room_id UUID NOT NULL,
        sender_type VARCHAR(20) NOT NULL CHECK (sender_type IN ('guest_user', 'system_user', 'system')),
        sender_guest_user_id UUID,
        sender_system_user_id UUID,
        content TEXT NOT NULL,
        message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),
        attachment_url VARCHAR(500),
        attachment_name VARCHAR(255),
        attachment_size INTEGER,
        status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
        is_edited BOOLEAN DEFAULT false,
        edited_at TIMESTAMP,
        reply_to_message_id UUID,
        is_deleted BOOLEAN DEFAULT false,
        deleted_at TIMESTAMP,
        metadata TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chat_room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_guest_user_id) REFERENCES chat_users(id) ON DELETE SET NULL,
        FOREIGN KEY (sender_system_user_id) REFERENCES users(id) ON DELETE SET NULL
      );
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX IDX_chat_messages_room_timestamp ON chat_messages (chat_room_id, timestamp);
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_chat_messages_sender_type ON chat_messages (sender_type);
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_chat_participants_room_user ON chat_room_participants (chat_room_id, guest_user_id, system_user_id);
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_chat_rooms_status_updated ON chat_rooms (status, last_message_at);
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_chat_users_email ON chat_users (email);
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_chat_users_phone ON chat_users (phone);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables in reverse order
    await queryRunner.query(`DROP TABLE IF EXISTS chat_messages CASCADE;`);
    await queryRunner.query(
      `DROP TABLE IF EXISTS chat_room_participants CASCADE;`,
    );
    await queryRunner.query(`DROP TABLE IF EXISTS chat_rooms CASCADE;`);
    await queryRunner.query(`DROP TABLE IF EXISTS chat_users CASCADE;`);
  }
}
