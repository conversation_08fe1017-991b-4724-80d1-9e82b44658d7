import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddChatSystemIndexes1735000002000 implements MigrationInterface {
  name = 'AddChatSystemIndexes1735000002000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🚀 Starting Migration 002: Chat System Performance Indexes');
    
    try {
      // 💬 CHAT MESSAGES - Critical for Real-time Performance
      console.log('Creating chat_messages indexes...');

      // Most critical: Room messages ordered by timestamp
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_room_timestamp
        ON chat_messages (chat_room_id, timestamp DESC)
      `);

      // Sender type filtering
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_type
        ON chat_messages (sender_type)
      `);

      // Message status filtering
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_status
        ON chat_messages (status)
      `);

      // Active messages only (partial index)
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_active
        ON chat_messages (timestamp DESC)
        WHERE is_deleted = false
      `);

      // 🏠 CHAT ROOMS - For Operator Dashboard
      console.log('Creating chat_rooms indexes...');

      // Active rooms ordered by last activity
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_rooms_status_last_message
        ON chat_rooms (status, last_message_at DESC)
      `);

      // Handler assignment queries (skip if column doesn't exist)
      try {
        await queryRunner.query(`
          CREATE INDEX IF NOT EXISTS idx_chat_rooms_handler
          ON chat_rooms (closed_by_id)
        `);
      } catch (e) {
        console.log('Skipping handler index - column may not exist');
      }

      // Active rooms only (partial index)
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_rooms_active
        ON chat_rooms (last_message_at DESC)
        WHERE status = 'active'
      `);

      // 👥 CHAT PARTICIPANTS - For Room Membership
      console.log('Creating chat_room_participants indexes...');

      // Room participant lookups
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_participants_room_guest
        ON chat_room_participants (chat_room_id, guest_user_id)
      `);

      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_participants_room_system
        ON chat_room_participants (chat_room_id, system_user_id)
      `);

      // Participant status filtering
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_participants_status
        ON chat_room_participants (status)
      `);

      // 👤 CHAT USERS - For User Management
      console.log('Creating chat_users indexes...');

      // Verification status queries
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_users_verification_status
        ON chat_users (verification_status)
      `);

      // Active users only
      await queryRunner.query(`
        CREATE INDEX IF NOT EXISTS idx_chat_users_active
        ON chat_users (last_seen DESC)
        WHERE is_active = true
      `);

      // Skip chat_operators table as it may not exist yet
      console.log('Skipping chat_operators indexes - table may not exist yet');

      console.log('✅ Migration 002 completed successfully');
      
    } catch (error) {
      console.error('❌ Migration 002 failed:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Rolling back Migration 002: Chat System Indexes');
    
    try {
      const indexesToDrop = [
        'idx_chat_operators_role',
        'idx_chat_operators_online',
        'idx_chat_users_active',
        'idx_chat_users_verification_status',
        'idx_chat_participants_status',
        'idx_chat_participants_room_system',
        'idx_chat_participants_room_guest',
        'idx_chat_rooms_active',
        'idx_chat_rooms_handler',
        'idx_chat_rooms_status_last_message',
        'idx_chat_messages_active',
        'idx_chat_messages_status',
        'idx_chat_messages_sender_type',
        'idx_chat_messages_room_timestamp',
      ];

      for (const indexName of indexesToDrop) {
        await queryRunner.query(`DROP INDEX IF EXISTS ${indexName}`);
      }

      console.log('✅ Migration 002 rollback completed');
      
    } catch (error) {
      console.error('❌ Migration 002 rollback failed:', error);
      throw error;
    }
  }
}
