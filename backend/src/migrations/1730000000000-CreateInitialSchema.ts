import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialSchema1730000000000 implements MigrationInterface {
  name = 'CreateInitialSchema1730000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🚀 Creating initial database schema...');
    
    try {
      // Enable UUID extension
      await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
      
      // 1. Create master_role table first (no dependencies)
      console.log('Creating master_role table...');
      await queryRunner.query(`
        CREATE TABLE "master_role" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "name" character varying NOT NULL,
          "ket" character varying NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_master_role" PRIMARY KEY ("id")
        )
      `);

      // 2. Create users table (depends on master_role)
      console.log('Creating users table...');
      await queryRunner.query(`
        CREATE TABLE "users" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "nama" character varying NOT NULL,
          "username" character varying NOT NULL,
          "password" character varying NOT NULL,
          "email" character varying NOT NULL,
          "roleId" uuid,
          "foto" character varying,
          "currentHashedRefreshToken" character varying,
          "forgetPasswordToken" character varying,
          "status" integer NOT NULL DEFAULT '1',
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_users" PRIMARY KEY ("id"),
          CONSTRAINT "UQ_users_username" UNIQUE ("username"),
          CONSTRAINT "UQ_users_email" UNIQUE ("email")
        )
      `);

      // 3. Create hak_akses table
      console.log('Creating hak_akses table...');
      await queryRunner.query(`
        CREATE TABLE "hak_akses" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "roleId" uuid,
          "id_menu" uuid,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_hak_akses" PRIMARY KEY ("id")
        )
      `);

      // 4. Create menu table
      console.log('Creating menu table...');
      await queryRunner.query(`
        CREATE TABLE "menu" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "jenis_menu" character varying NOT NULL,
          "nama_menu" character varying NOT NULL,
          "link" character varying,
          "icon" character varying,
          "posisi" integer,
          "id_menu" uuid,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_menu" PRIMARY KEY ("id")
        )
      `);

      // 5. Create content tables
      console.log('Creating content tables...');
      
      // Berita table
      await queryRunner.query(`
        CREATE TABLE "berita" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "judul" character varying NOT NULL,
          "slug" character varying NOT NULL,
          "kategori" character varying NOT NULL,
          "foto" character varying NOT NULL,
          "isi" text NOT NULL,
          "tgl_posting" TIMESTAMP NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_berita" PRIMARY KEY ("id")
        )
      `);

      // Artikel table
      await queryRunner.query(`
        CREATE TABLE "artikel" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "judul" character varying NOT NULL,
          "slug" character varying NOT NULL,
          "kategori" character varying NOT NULL,
          "foto" character varying NOT NULL,
          "file_pdf" character varying,
          "isi" text NOT NULL,
          "tgl_posting" TIMESTAMP NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_artikel" PRIMARY KEY ("id")
        )
      `);

      // Layanan table
      await queryRunner.query(`
        CREATE TABLE "layanan" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "judul" character varying NOT NULL,
          "slug" character varying NOT NULL,
          "kategori" character varying NOT NULL,
          "foto" character varying NOT NULL,
          "isi" text NOT NULL,
          "tgl_posting" TIMESTAMP NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_layanan" PRIMARY KEY ("id")
        )
      `);

      // Event table
      await queryRunner.query(`
        CREATE TABLE "event" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "judul" character varying NOT NULL,
          "isi" text NOT NULL,
          "tgl_awal" TIMESTAMP NOT NULL,
          "tgl_akhir" TIMESTAMP NOT NULL,
          "files" text,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_event" PRIMARY KEY ("id")
        )
      `);

      // 6. Create user activity table
      console.log('Creating user_activity table...');
      await queryRunner.query(`
        CREATE TABLE "user_activity" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "username" character varying NOT NULL,
          "route" character varying NOT NULL,
          "action" character varying NOT NULL,
          "ip" character varying,
          "user_agent" character varying,
          "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_user_activity" PRIMARY KEY ("id")
        )
      `);

      // 7. Create additional content tables
      console.log('Creating additional content tables...');
      
      // Informasi Umum
      await queryRunner.query(`
        CREATE TABLE "informasi_umum" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "logo" character varying,
          "nama_instansi" character varying,
          "alamat" text,
          "telepon" character varying,
          "email" character varying,
          "website" character varying,
          "deskripsi" text,
          "visi" text,
          "misi" text,
          "struktur_organisasi" character varying,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_informasi_umum" PRIMARY KEY ("id")
        )
      `);

      // Kontak
      await queryRunner.query(`
        CREATE TABLE "kontak" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "nama" character varying NOT NULL,
          "email" character varying NOT NULL,
          "telepon" character varying,
          "subjek" character varying NOT NULL,
          "pesan" text NOT NULL,
          "status" character varying DEFAULT 'pending',
          "tanggal_kirim" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          "tanggal_respon" TIMESTAMP,
          "respon" text,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_kontak" PRIMARY KEY ("id")
        )
      `);

      // Security content tables
      await queryRunner.query(`
        CREATE TABLE "peringatan_keamanan" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "file_pdf" character varying NOT NULL,
          "foto" character varying NOT NULL,
          "isi" text,
          "link_sumber" text,
          "tgl_posting" TIMESTAMP NOT NULL,
          "judul" character varying NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_peringatan_keamanan" PRIMARY KEY ("id")
        )
      `);

      await queryRunner.query(`
        CREATE TABLE "panduan_pedoman" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "nama" character varying NOT NULL,
          "file_pdf" character varying NOT NULL,
          "tanggal" TIMESTAMP NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_panduan_pedoman" PRIMARY KEY ("id")
        )
      `);

      // Year/Month management table
      await queryRunner.query(`
        CREATE TABLE "tahun" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "tahun" character varying NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_tahun" PRIMARY KEY ("id")
        )
      `);

      // Monitoring table (core business feature)
      await queryRunner.query(`
        CREATE TABLE "monitoring" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "file" character varying NOT NULL,
          "foto" character varying NOT NULL,
          "nama" text,
          "jenis" text,
          "tahunId" uuid,
          "bulan" text,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_monitoring" PRIMARY KEY ("id")
        )
      `);

      // RFC File table (core business feature for document management)
      await queryRunner.query(`
        CREATE TABLE "rfc_file" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "file_pdf" character varying NOT NULL,
          "judul" character varying NOT NULL,
          "kategori" character varying NOT NULL,
          "isi" text,
          "tgl_posting" TIMESTAMP NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_rfc_file" PRIMARY KEY ("id")
        )
      `);

      // Master Progress Ticket table (ticket status management)
      await queryRunner.query(`
        CREATE TABLE "master_progres_ticket" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "name" character varying NOT NULL,
          "color" character varying NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_master_progres_ticket" PRIMARY KEY ("id")
        )
      `);

      // Ticketing table (core business feature for user support)
      await queryRunner.query(`
        CREATE TABLE "ticketing" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "progresId" uuid,
          "number" character varying,
          "ket" character varying,
          "name" character varying NOT NULL,
          "email" character varying NOT NULL,
          "subject" character varying NOT NULL,
          "body" character varying NOT NULL,
          "attachment" character varying NOT NULL,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_ticketing" PRIMARY KEY ("id")
        )
      `);

      // Ticket History table (ticket tracking)
      await queryRunner.query(`
        CREATE TABLE "riwayat_ticket" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "ticketId" uuid NOT NULL,
          "progresId" uuid,
          "ket" character varying,
          "created_at" TIMESTAMP,
          "updated_at" TIMESTAMP,
          CONSTRAINT "PK_riwayat_ticket" PRIMARY KEY ("id")
        )
      `);

      // 8. Create foreign key constraints
      console.log('Creating foreign key constraints...');
      
      await queryRunner.query(`
        ALTER TABLE "users" 
        ADD CONSTRAINT "FK_users_role" 
        FOREIGN KEY ("roleId") REFERENCES "master_role"("id") ON DELETE SET NULL
      `);

      await queryRunner.query(`
        ALTER TABLE "hak_akses" 
        ADD CONSTRAINT "FK_hak_akses_role" 
        FOREIGN KEY ("roleId") REFERENCES "master_role"("id") ON DELETE CASCADE
      `);

      await queryRunner.query(`
        ALTER TABLE "menu"
        ADD CONSTRAINT "FK_menu_parent"
        FOREIGN KEY ("id_menu") REFERENCES "menu"("id") ON DELETE SET NULL
      `);

      await queryRunner.query(`
        ALTER TABLE "monitoring"
        ADD CONSTRAINT "FK_monitoring_tahun"
        FOREIGN KEY ("tahunId") REFERENCES "tahun"("id") ON DELETE SET NULL
      `);

      await queryRunner.query(`
        ALTER TABLE "ticketing"
        ADD CONSTRAINT "FK_ticketing_progres"
        FOREIGN KEY ("progresId") REFERENCES "master_progres_ticket"("id") ON DELETE SET NULL
      `);

      await queryRunner.query(`
        ALTER TABLE "riwayat_ticket"
        ADD CONSTRAINT "FK_riwayat_ticket_ticket"
        FOREIGN KEY ("ticketId") REFERENCES "ticketing"("id") ON DELETE CASCADE
      `);

      await queryRunner.query(`
        ALTER TABLE "riwayat_ticket"
        ADD CONSTRAINT "FK_riwayat_ticket_progres"
        FOREIGN KEY ("progresId") REFERENCES "master_progres_ticket"("id") ON DELETE CASCADE
      `);

      console.log('✅ Initial schema creation completed successfully');
      
    } catch (error) {
      console.error('❌ Initial schema creation failed:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('🔄 Rolling back initial schema...');
    
    try {
      // Drop tables in reverse order (respecting foreign key dependencies)
      const tablesToDrop = [
        'riwayat_ticket',
        'ticketing',
        'master_progres_ticket',
        'rfc_file',
        'monitoring',
        'tahun',
        'panduan_pedoman',
        'peringatan_keamanan',
        'kontak',
        'informasi_umum',
        'user_activity',
        'event',
        'layanan',
        'artikel',
        'berita',
        'menu',
        'hak_akses',
        'users',
        'master_role'
      ];

      for (const table of tablesToDrop) {
        await queryRunner.query(`DROP TABLE IF EXISTS "${table}" CASCADE`);
      }

      console.log('✅ Initial schema rollback completed');
      
    } catch (error) {
      console.error('❌ Initial schema rollback failed:', error);
      throw error;
    }
  }
}
