{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"seed": "node ./src/seeds/seed.js", "migrate:dev": "node ./scripts/migrate-and-seed.js", "migrate:prod": "NODE_ENV=production node ./scripts/migrate-and-seed.js", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/source.ts", "migration:run": "typeorm migration:run -d dist/source.js", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/source.ts", "create:admin": "node ./scripts/create-admin.js", "create:testuser": "node ./scripts/create-test-user.js", "prebuild": "rm -rf dist", "build": "nest build && cp -r src/seeds dist/", "build2": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch --port 3000", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:create": "typeorm-ts-node-commonjs migration:create", "migration:show": "typeorm-ts-node-commonjs migration:show -d src/source.ts"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.8", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.8", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.8", "@nestjs/platform-socket.io": "^11.1.2", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.2", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/passport-local": "^1.0.38", "axios": "^1.7.7", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "crypto-js": "^4.2.0", "csrf-csrf": "^4.0.3", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "nest-winston": "^1.9.7", "nodemailer": "^6.9.15", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.12.0", "pg-promise": "^11.9.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "svg-captcha": "^1.4.0", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.24", "typeorm-extension": "^3.6.1", "winston": "^3.14.2"}, "devDependencies": {"@eslint/compat": "^1.3.1", "@eslint/js": "^9.29.0", "@nestjs/cli": "^11.0.1", "@nestjs/testing": "^11.0.8", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.22", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.7.8", "@types/nodemailer": "^6.4.16", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.29.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "jest": "^29.7.0", "prettier": "^3.3.3", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}