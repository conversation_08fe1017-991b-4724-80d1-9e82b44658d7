version: '3.8'

services:
  # 🐘 PostgreSQL Database with Optimizations
  postgres:
    image: postgres:15-alpine
    container_name: csrit-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-dbcsrit}
      POSTGRES_USER: ${DB_USER:-aistech}
      POSTGRES_PASSWORD: ${DB_PASS:-changeme}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      # Persistent data storage
      - postgres_data:/var/lib/postgresql/data
      
      # Custom PostgreSQL configuration
      - ./docker/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./docker/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
      
      # Initialization scripts
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
      
      # Backup storage
      - postgres_backups:/var/backups/postgresql
      
      # Logs
      - postgres_logs:/var/log/postgresql
    command: >
      postgres 
      -c config_file=/etc/postgresql/postgresql.conf
      -c hba_file=/etc/postgresql/pg_hba.conf
    networks:
      - csrit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-aistech} -d ${DB_NAME:-dbcsrit}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s



  # 🚀 Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        NODE_ENV: production
    container_name: csrit-backend
    restart: unless-stopped
    ports:
      - "${APP_PORT:-3000}:3000"
    environment:
      # Database Configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ${DB_USER:-aistech}
      DB_PASS: ${DB_PASS:-changeme}
      DB_NAME: ${DB_NAME:-dbcsrit}
      
      # Performance Configuration
      DB_POOL_MIN: ${DB_POOL_MIN:-5}
      DB_POOL_MAX: ${DB_POOL_MAX:-20}
      DB_QUERY_TIMEOUT: ${DB_QUERY_TIMEOUT:-30000}
      DB_CONNECTION_TIMEOUT: ${DB_CONNECTION_TIMEOUT:-5000}
      

      
      # Application Configuration
      NODE_ENV: production
      JWT_ACCESS_TOKEN_SECRET: ${JWT_ACCESS_TOKEN_SECRET}
      JWT_ACCESS_TOKEN_EXPIRATION_TIME: ${JWT_ACCESS_TOKEN_EXPIRATION_TIME:-15m}
      JWT_REFRESH_TOKEN_SECRET: ${JWT_REFRESH_TOKEN_SECRET}
      JWT_REFRESH_TOKEN_EXPIRATION_TIME: ${JWT_REFRESH_TOKEN_EXPIRATION_TIME:-7d}
      JWT_ACCESS_COOKIE: ${JWT_ACCESS_COOKIE:-true}
      
      # Monitoring Configuration
      ENABLE_QUERY_LOGGING: ${ENABLE_QUERY_LOGGING:-false}
      SLOW_QUERY_THRESHOLD: ${SLOW_QUERY_THRESHOLD:-1000}
    volumes:
      # Application logs
      - app_logs:/app/logs
      
      # File uploads (if any)
      - app_uploads:/app/uploads
      
      # Performance benchmarks
      - ./tmp/performance_benchmarks:/tmp/performance_benchmarks
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - csrit-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 📊 Database Monitoring (Optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: csrit-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "${PGADMIN_PORT:-8080}:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json
    depends_on:
      - postgres
    networks:
      - csrit-network
    profiles:
      - monitoring

  # 🔍 Performance Monitoring with Grafana (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: csrit-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - csrit-network
    profiles:
      - monitoring

  # 🔄 Database Migration Runner (One-time service)
  migration:
    build:
      context: .
      dockerfile: Dockerfile.migration
    container_name: csrit-migration
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ${DB_USER:-aistech}
      DB_PASS: ${DB_PASS:-changeme}
      DB_NAME: ${DB_NAME:-dbcsrit}
    volumes:
      - ./scripts:/app/scripts
      - migration_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - csrit-network
    profiles:
      - migration

volumes:
  postgres_data:
    driver: local
  postgres_backups:
    driver: local
  postgres_logs:
    driver: local
  pgadmin_data:
    driver: local
  grafana_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  migration_logs:
    driver: local

networks:
  csrit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
