# 🗄️ Database Setup Guide

This guide helps you set up the database for the WBS-KG backend application with your Aiven cloud PostgreSQL database.

## 📋 Prerequisites

- Node.js and npm installed
- Access to your Aiven PostgreSQL database
- Database credentials configured in `.env.local`

## 🚀 Quick Setup

### 1. **Configure Database Connection**

Make sure your `.env.local` file is properly configured:

```env
# Database Configuration
DB_HOST=pg-2296a02c-aistech-91d2.c.aivencloud.com
DB_PORT=20805
DB_USER=avnadmin
DB_PASS=AVNS_Y04gpZMwkY2TOYbofhy
DB_NAME=db_wbs_kg

# SSL Configuration (Required for Aiven)
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=false
```

### 2. **Run Database Setup**

```bash
# Navigate to backend directory
cd backend

# Run the automated setup script
chmod +x ./scripts/setup-database.sh
./scripts/setup-database.sh
```

This script will:
- ✅ Install required dependencies
- ✅ Run all database migrations
- ✅ Seed initial data (roles, ticket statuses)
- ✅ Create default admin user

## 👤 Default Admin Credentials

After setup, you can login with:

- **Username**: `admin`
- **Email**: `<EMAIL>`
- **Password**: `admin123`

> ⚠️ **IMPORTANT**: Change the default password immediately after first login!

## 🔧 Manual Setup (Alternative)

If you prefer manual setup:

### 1. **Install Dependencies**
```bash
npm install
npm install --save-dev pg-promise bcrypt uuid
```

### 2. **Run Migrations**
```bash
npm run build
npm run migration:run
```

### 3. **Run Seeds**
```bash
npm run seed
```

### 4. **Create Admin User**
```bash
npm run migrate:dev
```

## 📝 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run migrate:dev` | Run migrations and seeds for development |
| `npm run migrate:prod` | Run migrations and seeds for production |
| `npm run migration:generate` | Generate new migration file |
| `npm run migration:run` | Run pending migrations |
| `npm run migration:revert` | Revert last migration |
| `npm run seed` | Run basic seeds only |

## 🗂️ Database Structure

The application creates the following main tables:

- **users** - Application users
- **master_role** - User roles (Admin, User)
- **user_activity** - User activity logs
- **chat_users** - Chat system users
- **chat_operators** - Chat operators
- **tickets** - Support tickets
- **articles** - Content articles
- **events** - Event management

## 🔍 Troubleshooting

### Connection Issues

1. **SSL Certificate Error**:
   ```bash
   # Make sure SSL is properly configured
   DB_SSL=true
   DB_SSL_REJECT_UNAUTHORIZED=false
   ```

2. **Connection Timeout**:
   ```bash
   # Increase timeout values
   DB_CONNECTION_TIMEOUT=30000
   DB_QUERY_TIMEOUT=60000
   ```

3. **Permission Denied**:
   - Verify your database user has CREATE, INSERT, UPDATE permissions
   - Check if the database exists and is accessible

### Migration Issues

1. **Migration Already Exists**:
   ```bash
   # Check migration status
   npx typeorm migration:show -d dist/source.js
   ```

2. **Failed Migration**:
   ```bash
   # Revert and try again
   npm run migration:revert
   npm run migration:run
   ```

### Seeding Issues

1. **Duplicate Key Error**:
   - Seeds use `ON CONFLICT DO NOTHING` to prevent duplicates
   - This is normal if running seeds multiple times

2. **Role Not Found**:
   - Make sure role seeds run before user seeds
   - Check if master_role table exists and has data

## 🔒 Security Notes

- Default admin password should be changed immediately
- Use strong passwords for production
- Regularly backup your database
- Monitor user activity logs
- Keep database credentials secure

## 📊 Performance Tips

- The application includes optimized indexes for better performance
- Connection pooling is configured for cloud databases
- Query logging can be enabled for debugging

## 🆘 Support

If you encounter issues:

1. Check the application logs
2. Verify database connectivity
3. Ensure all environment variables are set
4. Check Aiven dashboard for database status

For additional help, refer to the main project documentation.
