# Application Configuration
APP_NAME=CSRIT
APP_PORT=5000
APP_URL=http://localhost:5000
NODE_ENV=development

# Frontend URL for CORS (comma-separated for multiple origins)
FRONTEND_URL=http://localhost:3000,http://localhost:3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=userdb
DB_PASS=passdb
DB_NAME=namedb
DB_ENTITIES="['dist/**/*.entity{.ts,.js}']"
DB_SYNCHRONIZE=true

# JWT Configuration
JWT_REFRESH_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_SECRET=S3cret4dm1r3r
JWT_ACCESS_TOKEN_EXPIRATION_TIME=7200
JWT_ACCESS_TOKEN_SECRET=S3cretT3chN1que
JWT_ACCESS_COOKIE=true

# Password Hashing
HASH_SALT=20

# External Services
ZAMMAD_TOKEN=askdnasjdnasjd
ZAMMAD_URL=asdasd.com

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_USER=<EMAIL>
MAIL_PASSWORD=evrmexmqllsdndhb
MAIL_FROM=<EMAIL>
MAIL_PORT=465
MAIL_TRANSPORT=smtp://${MAIL_USER}:${MAIL_PASSWORD}@${MAIL_HOST}
# CSRF Configuration (Official Package)
CSRF_SECRET=DQrpPcYyYtgu4wOCuOViMecn4EFIFzF8

# File Storage Configuration
SECURE_UPLOAD_PATH=/var/app/secure-files/
MAX_FILE_SIZE=10485760

# --- N8N Integration ---
# Master switch for N8N integration. If false, N8N related calls will be skipped.
ENABLE_N8N_INTEGRATION=true
# URL for the N8N webhook that NestJS will call to send chat events.
N8N_WEBHOOK_URL=http://localhost:5678/webhook/your_chat_workflow_endpoint
# API Key for authenticating NestJS requests to the N8N_WEBHOOK_URL (if N8N webhook is secured).
N8N_OUTGOING_API_KEY=your_n8n_api_key_for_nestjs_to_use
# Secret used by NestJS to verify incoming webhook calls from N8N. N8N must include a matching signature.
N8N_INCOMING_WEBHOOK_SECRET=your_secret_for_n8n_to_call_nestjs_securely

# --- Office Hours Configuration ---
# IANA timezone string for office hours calculation (e.g., Asia/Makassar, America/New_York).
OFFICE_TIMEZONE=Asia/Makassar
# Start hour of office operations (24-hour format, e.g., 8 for 8 AM).
OFFICE_START_HOUR=8
# End hour of office operations (24-hour format, e.g., 17 for 5 PM. Chat initiated before this hour is considered within office hours).
OFFICE_END_HOUR=17
# Comma-separated list of working days. Sunday=0, Monday=1, ..., Saturday=6.
OFFICE_DAYS=0,1

# --- Holiday API Configuration (Optional) ---
# URL for an external API to fetch holiday list. If not provided, system falls back to hardcoded list.
HOLIDAY_API_URL=https://api.example.com/holidays
# API Key for the HOLIDAY_API_URL, if required by the API.
HOLIDAY_API_KEY=your_holiday_api_key_if_needed
