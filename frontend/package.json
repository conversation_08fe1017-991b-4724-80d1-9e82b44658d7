{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5173", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tanstack/vue-query": "^5.80.2", "@types/node": "^18.19.110", "@vueuse/core": "^13.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "lucide-vue-next": "^0.517.0", "pinia": "^3.0.2", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "vue": "^3.5.13", "vue-router": "^4.4.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@types/crypto-js": "^4.2.2", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}