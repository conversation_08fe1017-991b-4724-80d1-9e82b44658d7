<template>
  <div class="h-full flex flex-col bg-card">
    <!-- Header -->
    <div class="p-5 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold">Chat Rooms</h3>
        <div class="flex items-center gap-2 text-xs">
          <span
            class="w-2 h-2 rounded-full"
            :class="isConnected ? 'bg-green-500' : 'bg-red-500'"
          ></span>
          <span class="text-muted-foreground">
            {{ isConnected ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="px-5 py-4 border-b border-gray-200">
      <div class="flex gap-2 flex-wrap">
        <Button
          v-for="status in roomStatuses"
          :key="status.value"
          @click="selectedStatus = status.value"
          :variant="selectedStatus === status.value ? 'default' : 'outline'"
          size="sm"
          class="flex items-center gap-1"
        >
          {{ status.label }}
          <Badge
            v-if="status.count > 0"
            :variant="selectedStatus === status.value ? 'secondary' : 'default'"
            class="ml-1 text-xs"
          >
            {{ status.count }}
          </Badge>
        </Button>
      </div>
    </div>

    <!-- Room List -->
    <div class="flex-1 overflow-y-auto">
      <div
        v-for="room in filteredRooms"
        :key="room.id"
        @click="selectRoom(room.id)"
        class="p-4 border-b border-gray-200 cursor-pointer transition-colors hover:bg-accent/50"
        :class="{
          'bg-accent border-l-4 border-l-primary': selectedRoomId === room.id,
          'bg-muted/30': room.unreadCount > 0 && selectedRoomId !== room.id
        }"
      >
        <div class="flex justify-between items-start">
          <div class="flex-1 min-w-0">
            <!-- Room Header -->
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm font-medium truncate">
                {{ room.userName || room.userEmail || `User ${room.userId.slice(-6)}` }}
              </span>
              <Badge
                :variant="room.status === 'active' ? 'default' : room.status === 'waiting' ? 'secondary' : 'outline'"
                class="text-xs uppercase"
              >
                {{ room.status }}
              </Badge>
            </div>
            
            <!-- Last Message -->
            <div v-if="room.lastMessage" class="flex justify-between items-center gap-2">
              <span class="text-xs text-gray-600 truncate flex-1">
                {{ truncateMessage(room.lastMessage.content) }}
              </span>
              <span class="text-xs text-gray-500 whitespace-nowrap">
                {{ formatTime(room.lastMessage.timestamp) }}
              </span>
            </div>
            
            <div v-else class="text-xs text-gray-500 italic">
              No messages yet
            </div>
          </div>

          <!-- Room Meta -->
          <div class="flex flex-col items-center gap-1 ml-2">
            <div 
              v-if="room.unreadCount > 0" 
              class="bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium"
            >
              {{ room.unreadCount }}
            </div>
            
            <div v-if="isUserTyping(room.id)" class="flex items-center">
              <div class="flex gap-0.5">
                <span class="w-1 h-1 bg-blue-600 rounded-full animate-pulse"></span>
                <span class="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style="animation-delay: 0.2s"></span>
                <span class="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style="animation-delay: 0.4s"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredRooms.length === 0" class="p-10 text-center text-gray-500">
        <p class="text-sm">No {{ selectedStatus === 'all' ? '' : selectedStatus }} chat rooms found</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useOperatorStore } from '../stores/operator.store';
import { useOperatorApi } from '../composables/useOperatorApi';
import { Button, Badge } from '@/components/ui';

const operatorStore = useOperatorStore();

// TanStack Query
const { chatRoomsQuery } = useOperatorApi(operatorStore.accessToken);

const selectedStatus = ref<string>('all');

const chatRooms = computed(() => chatRoomsQuery.data.value || []);
const selectedRoomId = computed(() => operatorStore.selectedRoomId);
const isConnected = computed(() => operatorStore.isConnected);
const typingIndicators = computed(() => operatorStore.typingIndicators);

const roomStatuses = computed(() => [
  { 
    value: 'all', 
    label: 'All', 
    count: chatRooms.value.length 
  },
  { 
    value: 'active', 
    label: 'Active', 
    count: chatRooms.value.filter(r => r.status === 'active').length 
  },
  { 
    value: 'waiting', 
    label: 'Waiting', 
    count: chatRooms.value.filter(r => r.status === 'waiting').length 
  },
  { 
    value: 'closed', 
    label: 'Closed', 
    count: chatRooms.value.filter(r => r.status === 'closed').length 
  },
]);

const filteredRooms = computed(() => {
  if (selectedStatus.value === 'all') {
    return chatRooms.value;
  }
  return chatRooms.value.filter(room => room.status === selectedStatus.value);
});

const selectRoom = (roomId: string): void => {
  operatorStore.selectRoom(roomId);
};

const isUserTyping = (roomId: string): boolean => {
  const indicators = typingIndicators.value[roomId] || [];
  return indicators.some(indicator => indicator.userId && indicator.isTyping);
};

const truncateMessage = (content: string, maxLength: number = 50): string => {
  if (content.length <= maxLength) return content;
  return content.substring(0, maxLength) + '...';
};

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    const minutes = Math.floor(diffInHours * 60);
    return `${minutes}m ago`;
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h ago`;
  } else {
    return date.toLocaleDateString();
  }
};
</script>