<template>
  <div class="h-full flex flex-col bg-white">
    <!-- No Room Selected State -->
    <div v-if="!selectedRoom" class="flex-1 flex items-center justify-center bg-gray-50">
      <div class="text-center text-gray-500">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Chat Room</h3>
        <p class="text-sm">Choose a chat room from the list to start viewing and responding to messages.</p>
      </div>
    </div>

    <!-- Chat Container -->
    <div v-else class="h-full flex flex-col">
      <!-- Chat Header -->
      <div class="px-6 py-4 border-b border-gray-200 bg-white">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ getUserDisplayName() }}</h3>
            <div class="flex items-center gap-3 mt-1">
              <span 
                class="text-xs px-2 py-0.5 rounded-full font-medium uppercase"
                :class="{
                  'bg-green-100 text-green-800': selectedRoom.status === 'active',
                  'bg-yellow-100 text-yellow-800': selectedRoom.status === 'waiting',
                  'bg-red-100 text-red-800': selectedRoom.status === 'closed'
                }"
              >
                {{ selectedRoom.status }}
              </span>
              <span class="text-xs text-gray-500">Room: {{ selectedRoom.id.slice(-8) }}</span>
            </div>
          </div>
          
          <div class="flex gap-2">
            <button 
              v-if="selectedRoom.unreadCount > 0"
              @click="markAllAsRead"
              class="px-3 py-1.5 text-xs font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors"
            >
              Mark All Read ({{ selectedRoom.unreadCount }})
            </button>
            
            <button 
              @click="refreshMessages"
              :disabled="isLoading"
              class="px-3 py-1.5 text-xs font-medium text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span v-if="isLoading">Refreshing...</span>
              <span v-else>🔄 Refresh</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Messages Area -->
      <div ref="messagesContainer" class="flex-1 overflow-y-auto p-4 bg-gray-50 space-y-4">
        <!-- No Messages -->
        <div v-if="selectedRoomMessages.length === 0" class="text-center py-10 text-gray-500">
          <p class="text-sm">No messages in this conversation yet.</p>
        </div>

        <!-- Messages List -->
        <div v-else class="space-y-4">
          <div 
            v-for="message in selectedRoomMessages"
            :key="message.id"
            class="flex"
            :class="message.senderType === 'operator' ? 'justify-end' : 'justify-start'"
          >
            <div 
              class="max-w-xs lg:max-w-md px-4 py-3 rounded-lg relative"
              :class="{
                'bg-blue-600 text-white': message.senderType === 'operator',
                'bg-white border border-gray-200 text-gray-900': message.senderType === 'user',
                'border-l-4 border-l-yellow-400': !message.isRead && message.senderType === 'user'
              }"
            >
              <!-- Message Header -->
              <div class="flex justify-between items-center mb-1 text-xs">
                <span class="font-medium" :class="message.senderType === 'operator' ? 'text-blue-100' : 'text-gray-600'">
                  {{ getSenderName(message) }}
                </span>
                <span :class="message.senderType === 'operator' ? 'text-blue-100' : 'text-gray-500'">
                  {{ formatMessageTime(message.timestamp) }}
                </span>
              </div>
              
              <!-- Message Content -->
              <div class="text-sm leading-relaxed whitespace-pre-wrap">
                {{ message.content }}
              </div>

              <!-- Attachments -->
              <div v-if="message.attachments && message.attachments.length > 0" class="mt-2 space-y-1">
                <div 
                  v-for="attachment in message.attachments"
                  :key="attachment.id"
                  class="flex items-center gap-2 p-2 rounded text-xs"
                  :class="message.senderType === 'operator' ? 'bg-blue-500' : 'bg-gray-100'"
                >
                  <span>📎</span>
                  <span class="flex-1 truncate">{{ attachment.filename }}</span>
                  <span class="text-xs opacity-75">({{ formatFileSize(attachment.size) }})</span>
                </div>
              </div>

              <!-- Message Status -->
              <div class="text-right mt-1">
                <span class="text-xs" :class="message.senderType === 'operator' ? 'text-blue-100' : 'text-gray-400'">
                  {{ message.isRead ? '✓✓' : '✓' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div v-if="isUserTyping" class="flex justify-start">
          <div class="bg-white border border-gray-200 px-4 py-3 rounded-lg flex items-center gap-2 text-sm text-gray-600">
            <span>User is typing</span>
            <div class="flex gap-1">
              <span class="w-1 h-1 bg-gray-400 rounded-full animate-pulse"></span>
              <span class="w-1 h-1 bg-gray-400 rounded-full animate-pulse" style="animation-delay: 0.2s"></span>
              <span class="w-1 h-1 bg-gray-400 rounded-full animate-pulse" style="animation-delay: 0.4s"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Message Input Area -->
      <div class="border-t border-gray-200 bg-white p-4">
        <div class="flex gap-3 items-end">
          <textarea
            v-model="messageText"
            @keydown="handleKeyDown"
            @input="handleTyping"
            placeholder="Type your message..."
            rows="3"
            :disabled="!isConnected"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm"
          ></textarea>
          
          <div class="flex gap-2">
            <button 
              @click="handleAttachment"
              :disabled="!isConnected"
              title="Add attachment (placeholder)"
              class="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              📎
            </button>
            
            <button 
              @click="sendMessage"
              :disabled="!canSendMessage"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              Send
            </button>
          </div>
        </div>
        
        <div v-if="!isConnected" class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-800 text-sm text-center">
          ⚠️ Not connected to chat server
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue';
import { useOperatorStore } from '../stores/operator.store';
import { useOperatorApi } from '../composables/useOperatorApi';
import type { ChatMessage } from '../types/operator.types';

const operatorStore = useOperatorStore();

// TanStack Query
const { useRoomMessagesQuery } = useOperatorApi(operatorStore.accessToken);
const roomMessagesQuery = useRoomMessagesQuery(operatorStore.selectedRoomId);

// Refs
const messagesContainer = ref<HTMLElement>();
const messageText = ref('');
const typingTimeout = ref<number>();
const isRefreshing = ref(false);

// Computed
const selectedRoom = computed(() => operatorStore.selectedRoom);
const selectedRoomMessages = computed(() => roomMessagesQuery.data.value || []);
const isConnected = computed(() => operatorStore.isConnected);
const typingIndicators = computed(() => operatorStore.typingIndicators);
const isLoading = computed(() => roomMessagesQuery.isLoading.value);

const isUserTyping = computed(() => {
  if (!selectedRoom.value) return false;
  const indicators = typingIndicators.value[selectedRoom.value.id] || [];
  return indicators.some(indicator => indicator.userId && indicator.isTyping);
});

const canSendMessage = computed(() => 
  isConnected.value && 
  messageText.value.trim() !== '' && 
  selectedRoom.value
);

// Methods
const getUserDisplayName = (): string => {
  if (!selectedRoom.value) return '';
  return selectedRoom.value.userName || 
         selectedRoom.value.userEmail || 
         `User ${selectedRoom.value.userId.slice(-6)}`;
};

const getSenderName = (message: ChatMessage): string => {
  if (message.senderType === 'operator') {
    return message.senderName || 'Operator';
  }
  return getUserDisplayName();
};

const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const scrollToBottom = async (): Promise<void> => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const sendMessage = (): void => {
  if (!canSendMessage.value) return;

  const content = messageText.value.trim();
  messageText.value = '';

  // Send via WebSocket through the store
  // This will be handled by the WebSocket service
  console.log('Sending message:', content);
  
  // Clear typing indicator
  if (selectedRoom.value) {
    // operatorStore.sendTypingIndicator(selectedRoom.value.id, false);
  }

  scrollToBottom();
};

const handleKeyDown = (event: KeyboardEvent): void => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

const handleTyping = (): void => {
  if (!selectedRoom.value) return;

  // Send typing indicator
  // operatorStore.sendTypingIndicator(selectedRoom.value.id, true);

  // Clear previous timeout
  if (typingTimeout.value) {
    window.clearTimeout(typingTimeout.value);
  }

  // Set timeout to stop typing indicator
  typingTimeout.value = window.setTimeout(() => {
    if (selectedRoom.value) {
      // operatorStore.sendTypingIndicator(selectedRoom.value.id, false);
    }
  }, 2000);
};

const markAllAsRead = (): void => {
  if (!selectedRoom.value) return;
  operatorStore.markRoomAsRead(selectedRoom.value.id);
};

const refreshMessages = async (): Promise<void> => {
  if (!selectedRoom.value) return;

  isRefreshing.value = true;
  try {
    await operatorStore.fetchRoomMessages(selectedRoom.value.id);
  } finally {
    isRefreshing.value = false;
  }
};

const handleAttachment = (): void => {
  // Placeholder for file attachment functionality
  alert('File attachment feature coming soon!');
};

// Lifecycle
onMounted(() => {
  scrollToBottom();
});

onUnmounted(() => {
  if (typingTimeout.value) {
    window.clearTimeout(typingTimeout.value);
  }
});

// Watch for new messages and scroll to bottom
watch(selectedRoomMessages, () => {
  scrollToBottom();
}, { deep: true });
</script>