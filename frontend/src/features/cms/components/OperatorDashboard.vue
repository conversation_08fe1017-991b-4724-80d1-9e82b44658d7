<template>
  <div class="h-screen flex flex-col bg-gray-50">
    <!-- Header -->
    <div class="bg-white px-6 py-4 border-b border-gray-200 shadow-sm">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold mb-1">Chat Management System</h1>
          <div class="flex items-center gap-4 text-sm">
            <span class="text-gray-600 font-medium">{{ operator?.name || operator?.username }}</span>
            <Badge v-if="totalUnreadCount > 0" variant="destructive">
              {{ totalUnreadCount }} unread
            </Badge>
          </div>
        </div>

        <div class="flex gap-3">
          <Button
            @click="refreshData"
            :disabled="isLoading"
            variant="outline"
            size="sm"
          >
            <span v-if="isLoading">Refreshing...</span>
            <span v-else>🔄 Refresh</span>
          </Button>

          <Button
            @click="logout"
            variant="destructive"
            size="sm"
          >
            Logout
          </Button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Chat Rooms Sidebar -->
      <div class="w-80 min-w-[300px] max-w-[400px] bg-white border-r border-gray-200 flex flex-col">
        <ChatRoomList />
      </div>

      <!-- Chat View -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <OperatorChatView />
      </div>
    </div>

    <!-- Error Display -->
    <Alert v-if="error" class="fixed top-5 right-5 max-w-md z-50 shadow-lg" variant="destructive">
      <div class="flex items-center gap-3">
        <AlertDescription class="flex-1">{{ error }}</AlertDescription>
        <Button
          @click="clearError"
          variant="ghost"
          size="sm"
          class="h-5 w-5 p-0 hover:bg-destructive/20"
        >
          ×
        </Button>
      </div>
    </Alert>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useOperatorStore } from '../stores/operator.store';
import { useOperatorApi } from '../composables/useOperatorApi';
import { OperatorWebSocketService } from '../services/operator-websocket.service';
import ChatRoomList from './ChatRoomList.vue';
import OperatorChatView from './OperatorChatView.vue';
import { Button, Badge, Alert, AlertDescription } from '@/components/ui';

const router = useRouter();
const operatorStore = useOperatorStore();

// TanStack Query
const { chatRoomsQuery, refreshChatRooms } = useOperatorApi(operatorStore.accessToken);

// Computed
const operator = computed(() => operatorStore.operator);
const totalUnreadCount = computed(() => operatorStore.totalUnreadCount);
const error = computed(() => operatorStore.error);
const isLoading = computed(() => chatRoomsQuery.isLoading.value);

let websocketService: OperatorWebSocketService | null = null;

// Methods
const initializeWebSocket = (): void => {
  if (!operatorStore.accessToken) return;

  websocketService = new OperatorWebSocketService(
    // onMessage
    (message) => {
      operatorStore.addMessage(message);
    },
    // onTypingIndicator
    (indicator) => {
      operatorStore.updateTypingIndicator(indicator);
    },
    // onRoomUpdate
    (room) => {
      operatorStore.updateRoom(room);
      refreshChatRooms(); // Refresh the query cache
    },
    // onNewRoom
    (room) => {
      operatorStore.addNewRoom(room);
      refreshChatRooms(); // Refresh the query cache
    },
    // onConnectionChange
    (connected) => {
      operatorStore.setConnectionStatus(connected);
    },
    // onError
    (errorMessage) => {
      console.error('WebSocket error:', errorMessage);
    }
  );

  websocketService.connect(operatorStore.accessToken);
};

const refreshData = async (): Promise<void> => {
  refreshChatRooms();
};

const logout = (): void => {
  if (websocketService) {
    websocketService.disconnect();
    websocketService = null;
  }
  
  operatorStore.logout();
  router.push('/cms/login');
};

const clearError = (): void => {
  operatorStore.clearError();
};

// Lifecycle
onMounted(async () => {
  // Check if operator is authenticated
  if (!operatorStore.isAuthenticated) {
    router.push('/cms/login');
    return;
  }

  // Initialize WebSocket connection
  initializeWebSocket();
});

onUnmounted(() => {
  if (websocketService) {
    websocketService.disconnect();
  }
});
</script>