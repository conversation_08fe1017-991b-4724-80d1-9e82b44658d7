<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <Card class="w-full max-w-md">
      <CardHeader class="text-center">
        <CardTitle class="text-3xl font-bold">CMS Operator Login</CardTitle>
        <CardDescription>Sign in to access the chat management system</CardDescription>
      </CardHeader>

      <CardContent>
        <form @submit.prevent="handleLogin" class="space-y-6">
          <div class="space-y-4">
            <div class="space-y-2">
              <Label for="username">Username</Label>
              <Input
                id="username"
                v-model="credentials.username"
                type="text"
                placeholder="Enter your username"
                :disabled="isLoading"
                required
              />
            </div>

            <div class="space-y-2">
              <Label for="password">Password</Label>
              <Input
                id="password"
                v-model="credentials.password"
                type="password"
                placeholder="Enter your password"
                :disabled="isLoading"
                required
              />
            </div>
          </div>

          <Alert v-if="error" variant="destructive">
            <AlertDescription>{{ error }}</AlertDescription>
          </Alert>

          <Button
            type="submit"
            :disabled="isLoading || !isFormValid"
            class="w-full"
          >
            <span v-if="isLoading">Signing in...</span>
            <span v-else>Sign In</span>
          </Button>
        </form>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useOperatorStore } from '../stores/operator.store';
import { useOperatorApi } from '../composables/useOperatorApi';
import { tokenStorage } from '@/lib/secure-storage';
import type { OperatorLoginRequest } from '../types/operator.types';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Input,
  Label,
  Alert,
  AlertDescription
} from '@/components/ui';

const router = useRouter();
const operatorStore = useOperatorStore();

// TanStack Query
const { loginMutation } = useOperatorApi(operatorStore.accessToken);

const credentials = ref<OperatorLoginRequest>({
  username: '',
  password: '',
});

const isLoading = computed(() => loginMutation.isPending.value);
const error = computed(() => loginMutation.error.value?.message || operatorStore.error);

const isFormValid = computed(() =>
  credentials.value.username.trim() !== '' &&
  credentials.value.password.trim() !== ''
);

const handleLogin = async (): Promise<void> => {
  if (!isFormValid.value) return;

  try {
    const result = await loginMutation.mutateAsync(credentials.value);
    
    // Update store with login result
    operatorStore.operator = result.operator;
    operatorStore.accessToken = result.accessToken;
    operatorStore.refreshToken = result.refreshToken;
    operatorStore.isAuthenticated = true;

    // Store tokens securely
    tokenStorage.setAccessToken(result.accessToken);
    tokenStorage.setRefreshToken(result.refreshToken);
    tokenStorage.setOperatorData(result.operator);

    router.push('/cms/dashboard');
  } catch (err) {
    console.error('Login failed:', err);
  }
};

// Clear error when component unmounts
onUnmounted(() => {
  operatorStore.clearError();
});
</script>