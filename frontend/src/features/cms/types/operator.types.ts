export interface Operator {
  id: string;
  username: string;
  email: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface OperatorLoginRequest {
  username: string;
  password: string;
}

export interface OperatorLoginResponse {
  operator: Operator;
  accessToken: string;
  refreshToken: string;
}

export interface ChatRoom {
  id: string;
  userId: string;
  userName?: string;
  userEmail?: string;
  status: 'active' | 'closed' | 'waiting';
  lastMessage?: {
    id: string;
    content: string;
    timestamp: string;
    senderType: 'user' | 'operator';
    senderName?: string;
  };
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ChatMessage {
  id: string;
  roomId: string;
  content: string;
  senderType: 'user' | 'operator';
  senderId: string;
  senderName?: string;
  timestamp: string;
  isRead: boolean;
  attachments?: MessageAttachment[];
}

export interface MessageAttachment {
  id: string;
  filename: string;
  url: string;
  type: string;
  size: number;
}

export interface TypingIndicator {
  roomId: string;
  userId?: string;
  operatorId?: string;
  isTyping: boolean;
}

export interface WebSocketEvents {
  // Operator events
  operatorJoinRoom: { roomId: string };
  operatorLeaveRoom: { roomId: string };
  operatorSendMessage: { roomId: string; content: string };
  operatorTyping: { roomId: string; isTyping: boolean };
  operatorMarkAsRead: { roomId: string; messageIds: string[] };
  
  // Incoming events
  receiveMessage: ChatMessage;
  userTypingIndicator: TypingIndicator;
  roomUpdated: ChatRoom;
  newChatRoom: ChatRoom;
  messageRead: { roomId: string; messageIds: string[] };
}