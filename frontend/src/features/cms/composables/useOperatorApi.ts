import { computed, type Ref } from 'vue';
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';
import { api } from '@/services/http.service';
import { API_ENDPOINTS } from '@/config/api.config';
import type {
  OperatorLoginRequest,
  OperatorLoginResponse,
  ChatRoom,
  ChatMessage
} from '../types/operator.types';

// API Functions
const loginApi = async (credentials: OperatorLoginRequest): Promise<OperatorLoginResponse> => {
  return api.post(API_ENDPOINTS.CHAT.OPERATOR.LOGIN, credentials);
};

const fetchChatRoomsApi = async (token: string): Promise<ChatRoom[]> => {
  // Temporarily store token for this request
  const originalToken = localStorage.getItem('access_token');
  localStorage.setItem('access_token', token);

  try {
    const result = await api.get(API_ENDPOINTS.CHAT.OPERATOR.ROOMS);
    return result.data || [];
  } finally {
    // Restore original token
    if (originalToken) {
      localStorage.setItem('access_token', originalToken);
    } else {
      localStorage.removeItem('access_token');
    }
  }
};

const fetchRoomMessagesApi = async (roomId: string, token: string): Promise<ChatMessage[]> => {
  // Temporarily store token for this request
  const originalToken = localStorage.getItem('access_token');
  localStorage.setItem('access_token', token);

  try {
    const result = await api.get(API_ENDPOINTS.CHAT.MESSAGES(roomId));
    return result.data?.messages || [];
  } finally {
    // Restore original token
    if (originalToken) {
      localStorage.setItem('access_token', originalToken);
    } else {
      localStorage.removeItem('access_token');
    }
  }
};

export function useOperatorApi(accessToken: Ref<string | null> | string | null) {
  const queryClient = useQueryClient();
  
  // Convert to ref if it's a raw value
  const tokenRef = typeof accessToken === 'string' || accessToken === null
    ? computed(() => accessToken)
    : accessToken;

  // Login Mutation
  const loginMutation = useMutation({
    mutationFn: loginApi,
  });

  // Chat Rooms Query
  const chatRoomsQuery = useQuery({
    queryKey: ['chatRooms'],
    queryFn: () => fetchChatRoomsApi(tokenRef.value!),
    enabled: computed(() => !!tokenRef.value),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Room Messages Query Factory
  const useRoomMessagesQuery = (roomId: Ref<string | null> | string | null) => {
    const roomIdRef = typeof roomId === 'string' || roomId === null
      ? computed(() => roomId)
      : roomId;
      
    return useQuery({
      queryKey: ['roomMessages', roomIdRef],
      queryFn: () => fetchRoomMessagesApi(roomIdRef.value!, tokenRef.value!),
      enabled: computed(() => !!roomIdRef.value && !!tokenRef.value),
    });
  };

  // Refresh functions
  const refreshChatRooms = () => {
    queryClient.invalidateQueries({ queryKey: ['chatRooms'] });
  };

  const refreshRoomMessages = (roomId: string) => {
    queryClient.invalidateQueries({ queryKey: ['roomMessages', roomId] });
  };

  return {
    // Mutations
    loginMutation,
    
    // Queries
    chatRoomsQuery,
    useRoomMessagesQuery,
    
    // Refresh functions
    refreshChatRooms,
    refreshRoomMessages,
    
    // Query client
    queryClient,
  };
}