import { io, Socket } from 'socket.io-client';
import type { ChatMessage, TypingIndicator, ChatRoom } from '../types/operator.types';

export class OperatorWebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private onMessage: (message: ChatMessage) => void;
  private onTypingIndicator: (indicator: TypingIndicator) => void;
  private onRoomUpdate: (room: ChatRoom) => void;
  private onNewRoom: (room: ChatRoom) => void;
  private onConnectionChange: (connected: boolean) => void;
  private onError: (error: string) => void;

  constructor(
    onMessage: (message: ChatMessage) => void,
    onTypingIndicator: (indicator: TypingIndicator) => void,
    onRoomUpdate: (room: ChatRoom) => void,
    onNewRoom: (room: ChatRoom) => void,
    onConnectionChange: (connected: boolean) => void,
    onError: (error: string) => void
  ) {
    this.onMessage = onMessage;
    this.onTypingIndicator = onTypingIndicator;
    this.onRoomUpdate = onRoomUpdate;
    this.onNewRoom = onNewRoom;
    this.onConnectionChange = onConnectionChange;
    this.onError = onError;
  }

  connect(token: string): void {
    if (this.socket?.connected) {
      return;
    }

    const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000';
    
    this.socket = io(`${backendUrl}/chat`, {
      auth: {
        token,
        userType: 'system_user'
      },
      transports: ['websocket'],
      upgrade: false,
    });

    this.setupEventListeners();
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.reconnectAttempts = 0;
  }

  joinRoom(roomId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('joinRoom', { chatRoomId: roomId });
    }
  }

  leaveRoom(roomId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('leaveRoom', { chatRoomId: roomId });
    }
  }

  sendMessage(roomId: string, content: string): void {
    if (this.socket?.connected) {
      this.socket.emit('sendMessage', { 
        chatRoomId: roomId, 
        content,
        messageType: 'text'
      });
    }
  }

  sendTypingIndicator(roomId: string, isTyping: boolean): void {
    if (this.socket?.connected) {
      this.socket.emit('typing', { chatRoomId: roomId, isTyping });
    }
  }

  markAsRead(roomId: string, messageId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('markAsRead', { chatRoomId: roomId, messageId });
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Operator WebSocket connected');
      this.onConnectionChange(true);
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Operator WebSocket disconnected:', reason);
      this.onConnectionChange(false);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect
        return;
      }
      
      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('Operator WebSocket connection error:', error);
      this.onError(`Connection error: ${error.message}`);
      this.handleReconnect();
    });

    // Chat events
    this.socket.on('newMessage', (message: any) => {
      const chatMessage: ChatMessage = {
        id: message.id,
        roomId: message.chatRoomId,
        content: message.content,
        senderType: message.senderType === 'guest_user' ? 'user' : 'operator',
        senderId: message.senderId,
        senderName: message.senderName,
        timestamp: message.timestamp,
        isRead: message.isRead || false,
        attachments: message.attachments || []
      };
      this.onMessage(chatMessage);
    });

    this.socket.on('userTyping', (data: any) => {
      const indicator: TypingIndicator = {
        roomId: data.chatRoomId,
        userId: data.userType === 'guest_user' ? data.userId : undefined,
        operatorId: data.userType === 'system_user' ? data.userId : undefined,
        isTyping: data.isTyping
      };
      this.onTypingIndicator(indicator);
    });

    this.socket.on('authenticated', (data: { success: boolean }) => {
      if (data.success) {
        console.log('Successfully authenticated with chat server');
      }
    });

    this.socket.on('joinedRoom', (data: { chatRoomId: string }) => {
      console.log('Joined room:', data.chatRoomId);
    });

    this.socket.on('messageRead', (data: { chatRoomId: string; messageId: string }) => {
      console.log('Message marked as read:', data);
    });

    this.socket.on('roomUpdated', (room: any) => {
      const chatRoom: ChatRoom = {
        id: room.id,
        userId: room.userId,
        userName: room.userName,
        userEmail: room.userEmail,
        status: room.status,
        lastMessage: room.lastMessage,
        unreadCount: room.unreadCount || 0,
        createdAt: room.createdAt,
        updatedAt: room.updatedAt
      };
      this.onRoomUpdate(chatRoom);
    });

    this.socket.on('newChatRoom', (room: any) => {
      const chatRoom: ChatRoom = {
        id: room.id,
        userId: room.userId,
        userName: room.userName,
        userEmail: room.userEmail,
        status: room.status,
        lastMessage: room.lastMessage,
        unreadCount: room.unreadCount || 0,
        createdAt: room.createdAt,
        updatedAt: room.updatedAt
      };
      this.onNewRoom(chatRoom);
    });

    this.socket.on('error', (error: any) => {
      console.error('Operator WebSocket error:', error);
      this.onError(error.message || 'WebSocket error occurred');
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.onError('Maximum reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      this.socket?.connect();
    }, delay);
  }

  get isConnected(): boolean {
    return this.socket?.connected || false;
  }
}