<template>
  <div class="p-6">
    <div class="mb-6 text-center">
      <h3 class="text-xl font-semibold mb-2">Start a Chat</h3>
      <p class="text-sm text-muted-foreground">Fill in your details to begin chatting with our support team</p>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div class="space-y-2">
        <Label for="name">
          Name <span class="text-destructive">*</span>
        </Label>
        <Input
          id="name"
          v-model="form.name"
          type="text"
          placeholder="Enter your name"
          required
          :disabled="isSubmitting"
          :class="errors.name ? 'border-destructive focus-visible:ring-destructive' : ''"
        />
        <span v-if="errors.name" class="text-xs text-destructive">{{ errors.name }}</span>
      </div>

      <div class="space-y-2">
        <Label for="email">Email (Optional)</Label>
        <Input
          id="email"
          v-model="form.email"
          type="email"
          placeholder="Enter your email"
          :disabled="isSubmitting"
          :class="errors.email ? 'border-destructive focus-visible:ring-destructive' : ''"
        />
        <span v-if="errors.email" class="text-xs text-destructive">{{ errors.email }}</span>
      </div>

      <div class="space-y-2">
        <Label for="phone">Phone (Optional)</Label>
        <Input
          id="phone"
          v-model="form.phone"
          type="tel"
          placeholder="Enter your phone number"
          :disabled="isSubmitting"
          :class="errors.phone ? 'border-destructive focus-visible:ring-destructive' : ''"
        />
        <span v-if="errors.phone" class="text-xs text-destructive">{{ errors.phone }}</span>
      </div>

      <div class="space-y-2">
        <Label for="initialMessage">Initial Message (Optional)</Label>
        <Textarea
          id="initialMessage"
          v-model="form.initialMessage"
          placeholder="How can we help you today?"
          :rows="3"
          :disabled="isSubmitting"
        />
      </div>

      <Alert v-if="connectionError" variant="destructive">
        <AlertDescription class="flex items-center gap-2">
          <span class="text-base">⚠️</span>
          <span>{{ connectionError }}</span>
        </AlertDescription>
      </Alert>

      <Button
        type="submit"
        :disabled="isSubmitting || !isFormValid"
        class="w-full"
      >
        <div v-if="isSubmitting" class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
        {{ isSubmitting ? 'Starting Chat...' : 'Start Chat' }}
      </Button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue'
import { useChatStore } from '../stores/chat.store'
import type { ChatInitiationRequest } from '../types/chat.types'
import {
  Input,
  Label,
  Textarea,
  Button,
  Alert,
  AlertDescription
} from '@/components/ui'

// Emits
const emit = defineEmits<{
  chatInitiated: [data: { userId: string; userName: string; chatRoomId: string }]
}>()

// Store
const chatStore = useChatStore()

// Reactive form data
const form = reactive<ChatInitiationRequest>({
  name: '',
  email: '',
  phone: '',
  initialMessage: ''
})

// Form validation errors
const errors = reactive({
  name: '',
  email: '',
  phone: ''
})

// Loading state
const isSubmitting = computed(() => chatStore.isInitiating)
const connectionError = computed(() => chatStore.connectionError)

// Form validation
const isFormValid = computed(() => {
  return form.name.trim().length > 0 && !hasValidationErrors.value
})

const hasValidationErrors = computed(() => {
  return Object.values(errors).some(error => error.length > 0)
})

// Validation functions
const validateName = () => {
  if (!form.name.trim()) {
    errors.name = 'Name is required'
  } else if (form.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters'
  } else {
    errors.name = ''
  }
}

const validateEmail = () => {
  if (form.email && form.email.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(form.email)) {
      errors.email = 'Please enter a valid email address'
    } else {
      errors.email = ''
    }
  } else {
    errors.email = ''
  }
}

const validatePhone = () => {
  if (form.phone && form.phone.trim()) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    if (!phoneRegex.test(form.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.phone = 'Please enter a valid phone number'
    } else {
      errors.phone = ''
    }
  } else {
    errors.phone = ''
  }
}

// Watch form changes for validation
const validateForm = () => {
  validateName()
  validateEmail()
  validatePhone()
}

// Form submission
const handleSubmit = async () => {
  validateForm()
  
  if (!isFormValid.value) {
    return
  }

  try {
    // Prepare request data (remove empty optional fields)
    const requestData: ChatInitiationRequest = {
      name: form.name.trim()
    }

    if (form.email && form.email.trim()) {
      requestData.email = form.email.trim()
    }

    if (form.phone && form.phone.trim()) {
      requestData.phone = form.phone.trim()
    }

    if (form.initialMessage && form.initialMessage.trim()) {
      requestData.initialMessage = form.initialMessage.trim()
    }

    const response = await chatStore.initiateChat(requestData)

    if (response.success) {
      emit('chatInitiated', response.data)
    }
  } catch (error) {
    console.error('Failed to initiate chat:', error)
    // Error is handled by the store and displayed via connectionError
  }
}

// Reset form
const resetForm = () => {
  form.name = ''
  form.email = ''
  form.phone = ''
  form.initialMessage = ''
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
}

// Expose methods for parent component
defineExpose({
  resetForm
})
</script>