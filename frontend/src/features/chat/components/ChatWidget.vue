<template>
  <div class="fixed bottom-5 right-5 z-50 font-sans">
    <!-- Chat toggle button (when collapsed) -->
    <Button
      v-if="!isExpanded"
      @click="toggleWidget"
      :class="`relative w-16 h-16 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground border-2 border-primary rounded-full shadow-xl transition-all duration-300 text-2xl hover:-translate-y-1 hover:shadow-2xl p-0 group ${hasUnreadMessages ? 'animate-bounce' : ''}`"
    >
      <span class="drop-shadow-sm">💬</span>
      <Badge
        v-if="hasUnreadMessages"
        variant="destructive"
        class="absolute -top-2 -right-2 w-6 h-6 flex items-center justify-center text-xs font-bold border-2 border-background shadow-lg animate-pulse p-0"
      >
        {{ unreadCount }}
      </Badge>

      <!-- Hover tooltip -->
      <div class="absolute right-full mr-4 bg-popover text-popover-foreground text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none shadow-lg border">
        Need help? Click to chat!
        <div class="absolute top-1/2 -translate-y-1/2 left-full w-0 h-0 border-l-4 border-l-popover border-y-4 border-y-transparent"></div>
      </div>
    </Button>

    <!-- Chat window (when expanded) -->
    <Card
      v-if="isExpanded"
      class="w-96 h-[600px] rounded-xl shadow-2xl flex flex-col overflow-hidden animate-in slide-in-from-bottom-4 duration-300 md:w-96 sm:w-screen sm:h-screen sm:rounded-none sm:fixed sm:inset-0"
    >
      <!-- Chat header -->
      <CardHeader class="flex flex-row items-center justify-between p-4 bg-primary text-primary-foreground">
        <div class="flex-1">
          <CardTitle class="text-lg mb-1 text-primary-foreground">
            {{ chatTitle }}
          </CardTitle>
          <div class="flex items-center gap-2 text-sm opacity-90">
            <span
              :class="[
                'w-2 h-2 rounded-full',
                isConnected ? 'bg-green-400' : 'bg-red-400'
              ]"
            ></span>
            <span>{{ connectionStatusText }}</span>
          </div>
        </div>

        <div class="flex gap-2">
          <Button
            @click="minimizeWidget"
            variant="ghost"
            size="sm"
            class="w-8 h-8 bg-white/10 hover:bg-white/20 text-primary-foreground p-0"
            title="Minimize"
          >
            ➖
          </Button>
          <Button
            @click="closeWidget"
            variant="ghost"
            size="sm"
            class="w-8 h-8 bg-white/10 hover:bg-destructive text-primary-foreground p-0"
            title="Close chat"
          >
            ✕
          </Button>
        </div>
      </CardHeader>

      <!-- Chat content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Chat initiation form (before chat starts) -->
        <ChatInitiationForm
          v-if="!currentRoom"
          @chat-initiated="handleChatInitiated"
          ref="initiationForm"
        />

        <!-- Chat interface (after chat starts) -->
        <div v-else class="flex-1 flex flex-col h-full">
          <!-- Welcome message -->
          <Alert v-if="showWelcomeMessage" class="m-4 border-primary/20 bg-primary/5">
            <div class="flex items-start gap-3">
              <div class="flex-1">
                <h4 class="text-sm font-semibold text-primary mb-1">Welcome, {{ currentUser?.name }}!</h4>
                <AlertDescription class="text-xs text-primary/80 leading-relaxed">
                  You're now connected to our support team. How can we help you today?
                </AlertDescription>
              </div>
              <Button
                @click="dismissWelcome"
                variant="ghost"
                size="sm"
                class="text-primary hover:text-primary/80 p-0 w-5 h-5"
              >
                ✕
              </Button>
            </div>
          </Alert>

          <!-- Messages area -->
          <div class="flex-1 overflow-hidden">
            <MessageList />
          </div>

          <!-- Message input area -->
          <div class="flex-shrink-0">
            <MessageInput />
          </div>
        </div>
      </div>

      <!-- Chat footer (optional branding/info) -->
      <div class="p-2 bg-muted border-t border-gray-200 text-center">
        <span class="text-xs text-muted-foreground">Powered by ChatSystem</span>
      </div>
    </Card>

    <!-- Error modal -->
    <div
      v-if="showErrorModal"
      class="fixed inset-0 bg-black/50 flex items-center justify-center z-[51]"
      @click="closeErrorModal"
    >
      <div class="bg-white rounded-xl max-w-md w-[90%] overflow-hidden" @click.stop>
        <div class="flex items-center justify-between p-4 bg-red-50 border-b border-red-200">
          <h3 class="text-base font-semibold text-red-700 m-0">Connection Error</h3>
          <button
            @click="closeErrorModal"
            class="bg-none border-none text-red-600 cursor-pointer text-lg p-0 w-6 h-6 flex items-center justify-center hover:text-red-800"
          >
            ✕
          </button>
        </div>
        <div class="p-5">
          <p class="text-gray-700 leading-relaxed mb-5">{{ connectionError }}</p>
          <div class="flex gap-3 justify-end">
            <button
              @click="retryConnection"
              class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white border-none rounded-md cursor-pointer text-sm transition-colors"
            >
              Retry Connection
            </button>
            <button
              @click="closeErrorModal"
              class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 border-none rounded-md cursor-pointer text-sm transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useChatStore } from '../stores/chat.store'
import ChatInitiationForm from './ChatInitiationForm.vue'
import MessageList from './MessageList.vue'
import MessageInput from './MessageInput.vue'
import {
  Button,
  Badge,
  Card,
  CardHeader,
  CardTitle,
  Alert,
  AlertDescription
} from '@/components/ui'

// Props
interface Props {
  initiallyExpanded?: boolean
  showToggleButton?: boolean
  position?: 'bottom-right' | 'bottom-left' | 'center'
}

const props = withDefaults(defineProps<Props>(), {
  initiallyExpanded: false,
  showToggleButton: true,
  position: 'bottom-right'
})

// Store
const chatStore = useChatStore()

// Refs
const isExpanded = ref(props.initiallyExpanded)
const showWelcomeMessage = ref(false)
const showErrorModal = ref(false)
const initiationForm = ref<InstanceType<typeof ChatInitiationForm>>()

// Computed
const currentUser = computed(() => chatStore.currentUser)
const currentRoom = computed(() => chatStore.currentRoom)
const isConnected = computed(() => chatStore.isConnected)
const isAuthenticated = computed(() => chatStore.isAuthenticated)
const connectionError = computed(() => chatStore.connectionError)
const messages = computed(() => chatStore.messages)

const chatTitle = computed(() => {
  if (!currentRoom.value) {
    return 'Start a Chat'
  }
  return 'Support Chat'
})

const connectionStatusText = computed(() => {
  if (!isConnected.value) {
    return 'Disconnected'
  }
  if (!isAuthenticated.value) {
    return 'Connecting...'
  }
  return 'Online'
})

const hasUnreadMessages = computed(() => {
  return unreadCount.value > 0
})

const unreadCount = computed(() => {
  if (!currentRoom.value || isExpanded.value) {
    return 0
  }
  
  // Count unread messages from operators
  return messages.value.filter(
    msg => msg.senderType === 'system_user' && !msg.isRead
  ).length
})

// Widget controls
const toggleWidget = () => {
  isExpanded.value = !isExpanded.value
  
  if (isExpanded.value) {
    // Mark messages as read when opening
    markVisibleMessagesAsRead()
  }
}

const minimizeWidget = () => {
  isExpanded.value = false
}

const closeWidget = () => {
  isExpanded.value = false
  
  // Optionally disconnect or reset chat
  if (currentRoom.value) {
    // Just minimize, don't disconnect
    return
  }
  
  // If no active chat, reset everything
  chatStore.resetStore()
}

const dismissWelcome = () => {
  showWelcomeMessage.value = false
}

// Chat event handlers
const handleChatInitiated = (data: { userId: string; userName: string; chatRoomId: string }) => {
  console.log('Chat initiated:', data)
  showWelcomeMessage.value = true
  
  // Auto-dismiss welcome message after 10 seconds
  setTimeout(() => {
    showWelcomeMessage.value = false
  }, 10000)
}

// Error handling
const closeErrorModal = () => {
  showErrorModal.value = false
}

const retryConnection = () => {
  showErrorModal.value = false
  chatStore.connect()
}

// Message management
const markVisibleMessagesAsRead = () => {
  if (!currentRoom.value) return
  
  const unreadMessages = messages.value.filter(
    msg => msg.senderType === 'system_user' && !msg.isRead
  )
  
  unreadMessages.forEach(msg => {
    chatStore.markMessageAsRead(msg.id)
  })
}

// Keyboard shortcuts
const handleKeyDown = (event: KeyboardEvent) => {
  // ESC to close/minimize
  if (event.key === 'Escape' && isExpanded.value) {
    minimizeWidget()
  }
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
  
  // Initialize socket connection
  chatStore.initializeSocket()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
  
  // Clean up if needed
  if (chatStore.socket) {
    chatStore.disconnect()
  }
})

// Watchers
watch(connectionError, (error) => {
  if (error && isExpanded.value) {
    showErrorModal.value = true
  }
})

watch(isExpanded, (expanded) => {
  if (expanded) {
    markVisibleMessagesAsRead()
  }
})

// Watch for new messages to show notifications
watch(
  () => messages.value.length,
  (newLength, oldLength) => {
    if (newLength > oldLength && !isExpanded.value) {
      const latestMessage = messages.value[messages.value.length - 1]
      if (latestMessage?.senderType === 'system_user') {
        // Could add browser notification here
        console.log('New message received while minimized')
      }
    }
  }
)
</script>