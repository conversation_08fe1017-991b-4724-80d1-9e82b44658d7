<template>
  <div class="relative h-full overflow-y-auto p-4 bg-gray-50" ref="messageContainer">
    <div v-if="isLoadingHistory" class="flex items-center justify-center gap-2 p-5 text-gray-500 text-sm">
      <div class="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
      <span>Loading chat history...</span>
    </div>

    <div v-if="messages.length === 0 && !isLoadingHistory" class="flex flex-col items-center justify-center h-48 text-gray-500 text-center">
      <div class="text-5xl mb-4 opacity-50">💬</div>
      <p>No messages yet. Start the conversation!</p>
    </div>

    <div v-else class="flex flex-col gap-3">
      <div
        v-for="message in sortedMessages"
        :key="message.id"
        :class="[
          'flex max-w-[80%]',
          message.senderType === 'guest_user' ? 'self-end' : 'self-start'
        ]"
      >
        <div :class="[
          'rounded-xl p-3 shadow-sm min-w-[120px]',
          message.senderType === 'guest_user'
            ? 'bg-blue-500 text-white'
            : 'bg-white text-gray-900'
        ]">
          <div class="flex justify-between items-center mb-1.5 text-xs">
            <span :class="[
              'font-semibold',
              message.senderType === 'guest_user' ? 'text-white/90' : 'text-gray-900'
            ]">
              {{ message.senderType === 'guest_user' ? 'You' : 'Support' }}
            </span>
            <span :class="[
              'opacity-70 text-[11px]',
              message.senderType === 'guest_user' ? 'text-white/70' : 'text-gray-500'
            ]">
              {{ formatTime(message.timestamp) }}
            </span>
          </div>

          <div class="mb-1.5">
            <div v-if="message.messageType === 'text'" class="leading-relaxed break-words">
              {{ message.content }}
            </div>

            <div v-else-if="message.messageType === 'image'" class="space-y-2">
              <img
                :src="message.attachmentUrl"
                :alt="message.attachmentName || 'Image'"
                @load="() => scrollToBottom()"
                @error="handleImageError"
                class="max-w-full max-h-48 rounded-lg cursor-pointer"
              />
              <p v-if="message.content" class="text-sm opacity-90">{{ message.content }}</p>
            </div>

            <div v-else-if="message.messageType === 'file'" class="space-y-2">
              <div :class="[
                'flex items-center gap-3 p-3 rounded-lg',
                message.senderType === 'guest_user'
                  ? 'bg-white/10'
                  : 'bg-black/5'
              ]">
                <span class="text-xl">📎</span>
                <div class="flex flex-col gap-1 flex-1">
                  <span class="font-medium text-sm">{{ message.attachmentName || 'File' }}</span>
                  <a
                    v-if="message.attachmentUrl"
                    :href="message.attachmentUrl"
                    target="_blank"
                    rel="noopener noreferrer"
                    :class="[
                      'text-xs hover:underline',
                      message.senderType === 'guest_user'
                        ? 'text-white/90'
                        : 'text-blue-600'
                    ]"
                  >
                    Download
                  </a>
                </div>
              </div>
              <p v-if="message.content" class="text-sm opacity-90">{{ message.content }}</p>
            </div>
          </div>

          <div v-if="message.senderType === 'guest_user'" class="flex justify-end mt-1">
            <span :class="[
              'text-xs opacity-70',
              message.isRead ? 'text-green-300' : 'text-white/70'
            ]">
              {{ message.isRead ? '✓✓' : '✓' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Typing indicator -->
      <div v-if="isTypingIndicatorVisible" class="flex self-start">
        <div class="bg-white rounded-xl p-3 shadow-sm">
          <div class="flex justify-between items-center mb-1.5 text-xs">
            <span class="font-semibold text-gray-900">Support</span>
          </div>
          <div class="flex gap-1 py-2">
            <span class="w-2 h-2 bg-gray-500 rounded-full animate-bounce [animation-delay:-0.32s]"></span>
            <span class="w-2 h-2 bg-gray-500 rounded-full animate-bounce [animation-delay:-0.16s]"></span>
            <span class="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- Scroll to bottom button -->
    <button
      v-if="showScrollButton"
      @click="() => scrollToBottom()"
      class="absolute bottom-5 right-5 w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center text-lg hover:-translate-y-0.5"
      title="Scroll to bottom"
    >
      ↓
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUpdated, watch } from 'vue'
import { useChatStore } from '../stores/chat.store'

// Store
const chatStore = useChatStore()

// Refs
const messageContainer = ref<HTMLElement>()
const showScrollButton = ref(false)
const isLoadingHistory = ref(false)

// Computed
const messages = computed(() => chatStore.messages)
const sortedMessages = computed(() => chatStore.sortedMessages)
const isTypingIndicatorVisible = computed(() => chatStore.isTypingIndicatorVisible)

// Auto-scroll management
let isUserScrolling = false
let scrollTimeout: number | null = null

const checkScrollPosition = () => {
  if (!messageContainer.value) return

  const { scrollTop, scrollHeight, clientHeight } = messageContainer.value
  const isAtBottom = scrollHeight - scrollTop - clientHeight < 50

  showScrollButton.value = !isAtBottom && messages.value.length > 0
}

const scrollToBottom = (smooth = true) => {
  nextTick(() => {
    if (!messageContainer.value) return

    messageContainer.value.scrollTo({
      top: messageContainer.value.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto'
    })
  })
}

const handleScroll = () => {
  checkScrollPosition()
  
  isUserScrolling = true
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  
  scrollTimeout = window.setTimeout(() => {
    isUserScrolling = false
  }, 1000)
}

// Message formatting
const formatTime = (timestamp: Date | string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 24) {
    // Show time for messages within 24 hours
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  } else if (diffInHours < 24 * 7) {
    // Show day and time for messages within a week
    return date.toLocaleDateString([], { 
      weekday: 'short',
      hour: '2-digit', 
      minute: '2-digit' 
    })
  } else {
    // Show full date for older messages
    return date.toLocaleDateString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  console.error('Failed to load image:', img.src)
}

// Load chat history
const loadHistory = async () => {
  if (!chatStore.currentRoom) return

  isLoadingHistory.value = true
  try {
    await chatStore.loadChatHistory(1, 50)
  } catch (error) {
    console.error('Failed to load chat history:', error)
  } finally {
    isLoadingHistory.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  if (messageContainer.value) {
    messageContainer.value.addEventListener('scroll', handleScroll)
  }
  
  // Load history when component mounts
  if (chatStore.currentRoom && messages.value.length === 0) {
    loadHistory()
  }
  
  // Scroll to bottom initially
  scrollToBottom(false)
})

onUpdated(() => {
  // Auto-scroll to bottom when new messages arrive (if user isn't scrolling)
  if (!isUserScrolling) {
    scrollToBottom()
  }
})

// Watch for new messages
watch(
  () => messages.value.length,
  (newLength, oldLength) => {
    if (newLength > oldLength && !isUserScrolling) {
      scrollToBottom()
    }
  }
)

// Watch for room changes
watch(
  () => chatStore.currentRoom?.id,
  (newRoomId) => {
    if (newRoomId && messages.value.length === 0) {
      loadHistory()
    }
  }
)
</script>
