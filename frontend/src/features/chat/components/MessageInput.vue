<template>
  <div class="bg-white border-t border-gray-200 p-4">
    <div v-if="connectionError" class="flex items-center gap-2 p-3 mb-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
      <span class="text-base">⚠️</span>
      <span class="flex-1">{{ connectionError }}</span>
      <button @click="reconnect" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors">
        Reconnect
      </button>
    </div>

    <div v-if="!isConnected" class="flex items-center gap-2 p-3 mb-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm">
      <span class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></span>
      <span>Connecting...</span>
    </div>

    <div v-else-if="!isAuthenticated" class="flex items-center gap-2 p-3 mb-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm">
      <span class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></span>
      <span>Authenticating...</span>
    </div>

    <form v-else @submit.prevent="handleSubmit" class="flex flex-col gap-2">
      <div class="flex items-end gap-2 p-2 border border-gray-300 rounded-xl bg-white transition-colors focus-within:border-blue-500 focus-within:shadow-sm focus-within:shadow-blue-500/10">
        <!-- File attachment button (placeholder) -->
        <button
          type="button"
          @click="handleFileAttachment"
          class="flex-shrink-0 w-9 h-9 bg-transparent hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed border-none rounded-md cursor-pointer text-base flex items-center justify-center transition-colors"
          title="Attach file (Coming soon)"
          disabled
        >
          📎
        </button>

        <!-- Message input -->
        <textarea
          ref="messageInput"
          v-model="messageText"
          @keydown="handleKeyDown"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          placeholder="Type your message..."
          rows="1"
          class="flex-1 border-none outline-none resize-none font-inherit text-sm leading-6 py-2 min-h-[20px] max-h-[120px] overflow-y-auto placeholder-gray-400 disabled:opacity-60 disabled:cursor-not-allowed md:text-sm"
          :disabled="isSending"
        ></textarea>

        <!-- Send button -->
        <button
          type="submit"
          :disabled="!canSend"
          class="flex-shrink-0 w-9 h-9 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white border-none rounded-md cursor-pointer flex items-center justify-center transition-all duration-200 hover:not(:disabled):-translate-y-px"
          title="Send message"
        >
          <div v-if="isSending" class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span v-else class="text-base transition-transform hover:not(:disabled):-rotate-12">➤</span>
        </button>
      </div>

      <!-- Character count (optional) -->
      <div v-if="showCharCount" class="self-end text-xs text-gray-500 px-1">
        {{ messageText.length }}/{{ maxLength }}
      </div>
    </form>

    <!-- File upload modal (placeholder) -->
    <div v-if="showFileModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000]" @click="closeFileModal">
      <div class="bg-white rounded-xl p-6 max-w-sm w-[90%] text-center" @click.stop>
        <h3 class="text-lg font-semibold text-gray-900 mb-3">File Attachment</h3>
        <p class="text-gray-600 mb-5">File attachment feature is coming soon!</p>
        <button @click="closeFileModal" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { useChatStore } from '../stores/chat.store'

// Store
const chatStore = useChatStore()

// Refs
const messageInput = ref<HTMLTextAreaElement>()
const messageText = ref('')
const isSending = ref(false)
const showFileModal = ref(false)
const isTyping = ref(false)
const typingTimeout = ref<number | null>(null)

// Constants
const maxLength = 2000
const showCharCount = computed(() => messageText.value.length > maxLength * 0.8)

// Computed
const isConnected = computed(() => chatStore.isConnected)
const isAuthenticated = computed(() => chatStore.isAuthenticated)
const connectionError = computed(() => chatStore.connectionError)

const canSend = computed(() => {
  return (
    messageText.value.trim().length > 0 &&
    messageText.value.length <= maxLength &&
    isConnected.value &&
    isAuthenticated.value &&
    !isSending.value
  )
})

// Auto-resize textarea
const adjustTextareaHeight = () => {
  nextTick(() => {
    if (!messageInput.value) return

    messageInput.value.style.height = 'auto'
    const scrollHeight = messageInput.value.scrollHeight
    const maxHeight = 120 // Max height in pixels (about 5 lines)
    
    messageInput.value.style.height = Math.min(scrollHeight, maxHeight) + 'px'
  })
}

// Typing indicator management
const sendTypingIndicator = (typing: boolean) => {
  if (!isConnected.value || !isAuthenticated.value) return

  try {
    chatStore.sendTypingIndicator(typing)
    isTyping.value = typing
  } catch (error) {
    console.error('Failed to send typing indicator:', error)
  }
}

const handleTypingStart = () => {
  if (!isTyping.value) {
    sendTypingIndicator(true)
  }

  // Clear existing timeout
  if (typingTimeout.value) {
    clearTimeout(typingTimeout.value)
  }

  // Set timeout to stop typing indicator
  typingTimeout.value = window.setTimeout(() => {
    sendTypingIndicator(false)
  }, 3000) // Stop typing indicator after 3 seconds of inactivity
}

const handleTypingStop = () => {
  if (typingTimeout.value) {
    clearTimeout(typingTimeout.value)
    typingTimeout.value = null
  }
  
  if (isTyping.value) {
    sendTypingIndicator(false)
  }
}

// Event handlers
const handleInput = () => {
  adjustTextareaHeight()
  handleTypingStart()
}

const handleKeyDown = (event: KeyboardEvent) => {
  // Send message on Enter (without Shift)
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSubmit()
    return
  }

  // Allow Shift+Enter for new lines
  if (event.key === 'Enter' && event.shiftKey) {
    // Let the default behavior happen (new line)
    nextTick(() => adjustTextareaHeight())
  }
}

const handleFocus = () => {
  // Optional: Add focus styling or behavior
}

const handleBlur = () => {
  handleTypingStop()
}

const handleSubmit = async () => {
  if (!canSend.value) return

  const message = messageText.value.trim()
  if (!message) return

  isSending.value = true
  handleTypingStop()

  try {
    await chatStore.sendMessage(message, 'text')
    messageText.value = ''
    adjustTextareaHeight()
    
    // Focus back to input
    nextTick(() => {
      messageInput.value?.focus()
    })
  } catch (error) {
    console.error('Failed to send message:', error)
    // Error is handled by the store
  } finally {
    isSending.value = false
  }
}

const handleFileAttachment = () => {
  showFileModal.value = true
}

const closeFileModal = () => {
  showFileModal.value = false
}

const reconnect = () => {
  chatStore.connect()
}

// Lifecycle hooks
onMounted(() => {
  adjustTextareaHeight()
  
  // Focus input when component mounts
  nextTick(() => {
    messageInput.value?.focus()
  })
})

onUnmounted(() => {
  handleTypingStop()
})

// Watch for connection changes
watch(isAuthenticated, (newValue) => {
  if (newValue) {
    nextTick(() => {
      messageInput.value?.focus()
    })
  }
})

// Watch message text for auto-resize
watch(messageText, () => {
  adjustTextareaHeight()
})
</script>