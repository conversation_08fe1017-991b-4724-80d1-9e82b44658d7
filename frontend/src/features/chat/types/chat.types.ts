export interface ChatUser {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  isVerified: boolean;
  createdAt: Date;
}

export interface ChatRoom {
  id: string;
  userId: string;
  operatorId?: string;
  status: 'waiting' | 'active' | 'closed';
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  chatRoomId: string;
  senderId: string;
  senderType: 'guest_user' | 'system_user';
  content: string;
  messageType: 'text' | 'image' | 'file';
  timestamp: Date;
  attachmentUrl?: string;
  attachmentName?: string;
  replyToMessageId?: string;
  isRead?: boolean;
}

export interface ChatInitiationRequest {
  name: string;
  email?: string;
  phone?: string;
  initialMessage?: string;
}

export interface ChatInitiationResponse {
  success: boolean;
  data: {
    userId: string;
    userName: string;
    chatRoomId: string;
    messageId?: string;
  };
}

export interface TypingIndicator {
  chatRoomId: string;
  userId: string;
  userType: 'guest_user' | 'system_user';
  isTyping: boolean;
}

export interface MessageReadReceipt {
  chatRoomId: string;
  messageId: string;
  readBy: string;
  readByType: 'guest_user' | 'system_user';
}

export interface WebSocketEvents {
  // Client to Server
  authenticate: {
    userId: string;
    userType: 'guest_user' | 'system_user';
  };
  sendMessage: {
    chatRoomId: string;
    content: string;
    messageType?: 'text' | 'image' | 'file';
    attachmentUrl?: string;
    attachmentName?: string;
    replyToMessageId?: string;
  };
  joinRoom: {
    chatRoomId: string;
  };
  leaveRoom: {
    chatRoomId: string;
  };
  markAsRead: {
    chatRoomId: string;
    messageId: string;
  };
  typing: {
    chatRoomId: string;
    isTyping: boolean;
  };

  // Server to Client
  newMessage: ChatMessage;
  userTyping: TypingIndicator;
  messageRead: MessageReadReceipt;
  authenticated: { success: boolean };
  joinedRoom: { chatRoomId: string };
  leftRoom: { chatRoomId: string };
  error: { message: string };
}

export interface ChatState {
  isConnected: boolean;
  isAuthenticated: boolean;
  currentUser: ChatUser | null;
  currentRoom: ChatRoom | null;
  messages: ChatMessage[];
  typingUsers: string[];
  connectionError: string | null;
}