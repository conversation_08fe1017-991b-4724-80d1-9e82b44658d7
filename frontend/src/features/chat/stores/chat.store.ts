import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { io, Socket } from 'socket.io-client'
import type { 
  ChatUser, 
  ChatRoom, 
  ChatMessage, 
  ChatInitiationRequest,
  ChatInitiationResponse,
  TypingIndicator,
  MessageReadReceipt
} from '../types/chat.types'

export const useChatStore = defineStore('chat', () => {
  // State
  const socket = ref<Socket | null>(null)
  const isConnected = ref(false)
  const isAuthenticated = ref(false)
  const currentUser = ref<ChatUser | null>(null)
  const currentRoom = ref<ChatRoom | null>(null)
  const messages = ref<ChatMessage[]>([])
  const typingUsers = ref<string[]>([])
  const connectionError = ref<string | null>(null)
  const isInitiating = ref(false)
  const isReconnecting = ref(false)

  // Computed
  const sortedMessages = computed(() => {
    return [...messages.value].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    )
  })

  const isTypingIndicatorVisible = computed(() => {
    return typingUsers.value.length > 0
  })

  // Actions
  const initializeSocket = () => {
    if (socket.value) return

    // Use the centralized API configuration
    import('@/config/api.config').then(({ buildBackendUrl }) => {
      const socketUrl = buildBackendUrl('/chat')

      socket.value = io(socketUrl, {
        autoConnect: false,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      })

      setupSocketListeners()
    })
  }

  const setupSocketListeners = () => {
    if (!socket.value) return

    socket.value.on('connect', () => {
      isConnected.value = true
      connectionError.value = null
      isReconnecting.value = false
      console.log('Connected to chat server')
    })

    socket.value.on('disconnect', () => {
      isConnected.value = false
      isAuthenticated.value = false
      console.log('Disconnected from chat server')
    })

    socket.value.on('connect_error', (error) => {
      connectionError.value = `Connection failed: ${error.message}`
      isReconnecting.value = true
      console.error('Connection error:', error)
    })

    socket.value.on('authenticated', (data: { success: boolean }) => {
      isAuthenticated.value = data.success
      if (data.success) {
        console.log('Successfully authenticated with chat server')
      }
    })

    socket.value.on('newMessage', (message: ChatMessage) => {
      messages.value.push(message)
      
      // Auto-mark as read if it's from operator to user
      if (message.senderType === 'system_user' && currentRoom.value) {
        markMessageAsRead(message.id)
      }
    })

    socket.value.on('userTyping', (data: TypingIndicator) => {
      if (data.isTyping) {
        if (!typingUsers.value.includes(data.userId)) {
          typingUsers.value.push(data.userId)
        }
      } else {
        typingUsers.value = typingUsers.value.filter(id => id !== data.userId)
      }
    })

    socket.value.on('messageRead', (data: MessageReadReceipt) => {
      const message = messages.value.find(m => m.id === data.messageId)
      if (message) {
        message.isRead = true
      }
    })

    socket.value.on('joinedRoom', (data: { chatRoomId: string }) => {
      console.log('Joined room:', data.chatRoomId)
    })

    socket.value.on('error', (data: { message: string }) => {
      connectionError.value = data.message
      console.error('Chat error:', data.message)
    })
  }

  const connect = () => {
    if (!socket.value) {
      initializeSocket()
    }
    socket.value?.connect()
  }

  const disconnect = () => {
    socket.value?.disconnect()
    isConnected.value = false
    isAuthenticated.value = false
  }

  const authenticate = (userId: string, userType: 'guest_user' | 'system_user') => {
    if (!socket.value || !isConnected.value) {
      throw new Error('Socket not connected')
    }

    socket.value.emit('authenticate', { userId, userType })
  }

  const initiateChat = async (data: ChatInitiationRequest): Promise<ChatInitiationResponse> => {
    isInitiating.value = true
    connectionError.value = null

    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000'
      const response = await fetch(`${backendUrl}/api/chat/initiate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ChatInitiationResponse = await response.json()

      if (result.success) {
        // Set current user and room
        currentUser.value = {
          id: result.data.userId,
          name: result.data.userName,
          email: data.email,
          phone: data.phone,
          isVerified: false, // Placeholder for verification
          createdAt: new Date(),
        }

        currentRoom.value = {
          id: result.data.chatRoomId,
          userId: result.data.userId,
          status: 'waiting',
          createdAt: new Date(),
          updatedAt: new Date(),
        }

        // Connect to WebSocket and authenticate
        connect()
        
        // Wait for connection before authenticating
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Connection timeout'))
          }, 5000)

          const checkConnection = () => {
            if (isConnected.value) {
              clearTimeout(timeout)
              resolve()
            } else {
              setTimeout(checkConnection, 100)
            }
          }
          checkConnection()
        })

        authenticate(result.data.userId, 'guest_user')
        
        // Join the chat room
        joinRoom(result.data.chatRoomId)
      }

      return result
    } catch (error) {
      connectionError.value = error instanceof Error ? error.message : 'Failed to initiate chat'
      throw error
    } finally {
      isInitiating.value = false
    }
  }

  const sendMessage = (content: string, messageType: 'text' | 'image' | 'file' = 'text') => {
    if (!socket.value || !isAuthenticated.value || !currentRoom.value) {
      throw new Error('Not connected or authenticated')
    }

    socket.value.emit('sendMessage', {
      chatRoomId: currentRoom.value.id,
      content,
      messageType,
    })
  }

  const joinRoom = (chatRoomId: string) => {
    if (!socket.value || !isAuthenticated.value) {
      throw new Error('Not connected or authenticated')
    }

    socket.value.emit('joinRoom', { chatRoomId })
  }

  const leaveRoom = (chatRoomId: string) => {
    if (!socket.value) return

    socket.value.emit('leaveRoom', { chatRoomId })
  }

  const markMessageAsRead = (messageId: string) => {
    if (!socket.value || !isAuthenticated.value || !currentRoom.value) return

    socket.value.emit('markAsRead', {
      chatRoomId: currentRoom.value.id,
      messageId,
    })
  }

  const sendTypingIndicator = (isTyping: boolean) => {
    if (!socket.value || !isAuthenticated.value || !currentRoom.value) return

    socket.value.emit('typing', {
      chatRoomId: currentRoom.value.id,
      isTyping,
    })
  }

  const loadChatHistory = async (page: number = 1, limit: number = 50) => {
    if (!currentRoom.value) return

    try {
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000'
      const response = await fetch(
        `${backendUrl}/api/chat/rooms/${currentRoom.value.id}/messages?page=${page}&limit=${limit}`
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (result.success) {
        // Prepend older messages to maintain chronological order
        if (page === 1) {
          messages.value = result.data.messages || []
        } else {
          messages.value = [...(result.data.messages || []), ...messages.value]
        }
      }
    } catch (error) {
      console.error('Failed to load chat history:', error)
    }
  }

  const clearChat = () => {
    messages.value = []
    typingUsers.value = []
    currentUser.value = null
    currentRoom.value = null
    connectionError.value = null
    isAuthenticated.value = false
  }

  const resetStore = () => {
    disconnect()
    clearChat()
    socket.value = null
  }

  return {
    // State
    socket,
    isConnected,
    isAuthenticated,
    currentUser,
    currentRoom,
    messages,
    typingUsers,
    connectionError,
    isInitiating,
    isReconnecting,

    // Computed
    sortedMessages,
    isTypingIndicatorVisible,

    // Actions
    initializeSocket,
    connect,
    disconnect,
    authenticate,
    initiateChat,
    sendMessage,
    joinRoom,
    leaveRoom,
    markMessageAsRead,
    sendTypingIndicator,
    loadChatHistory,
    clearChat,
    resetStore,
  }
})