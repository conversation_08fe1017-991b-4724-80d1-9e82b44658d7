/**
 * API Configuration for Frontend
 * Centralizes all API-related configuration for different environments
 */

export interface ApiConfig {
  baseURL: string;
  backendURL: string;
  wsURL: string;
  timeout: number;
}

/**
 * Get API configuration based on environment variables
 */
export const getApiConfig = (): ApiConfig => {
  const isDevelopment = import.meta.env.DEV;
  
  return {
    // API base URL - for same-domain deployment in production
    baseURL: import.meta.env.VITE_API_BASE_URL || (isDevelopment ? '/api' : '/api'),
    
    // Backend URL - for WebSocket connections and direct backend calls
    backendURL: import.meta.env.VITE_BACKEND_URL || (isDevelopment ? 'http://localhost:3000' : 'https://wbs-kg.aistech.id'),
    
    // WebSocket URL - for real-time chat functionality
    wsURL: import.meta.env.VITE_WS_URL || (isDevelopment ? 'ws://localhost:3000' : 'wss://wbs-kg.aistech.id'),
    
    // Request timeout in milliseconds
    timeout: 30000,
  };
};

/**
 * Default API configuration instance
 */
export const API_CONFIG = getApiConfig();

/**
 * API endpoints configuration
 */
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/v1/auth/login',
    LOGOUT: '/v1/auth/logout',
    REFRESH: '/v1/auth/refresh',
    REGISTER: '/v1/auth/register',
    PROFILE: '/v1/auth/profile',
  },
  
  // User management
  USER: {
    LIST: '/v1/user',
    DETAIL: (id: string) => `/v1/user/${id}`,
    UPDATE: (id: string) => `/v1/user/${id}`,
    DELETE: (id: string) => `/v1/user/delete/${id}`,
    ACTIVITY: '/v1/user/user-activity',
  },
  
  // Content modules
  BERITA: {
    LIST: '/v1/berita',
    DETAIL: (id: string) => `/v1/berita/${id}`,
    CREATE: '/v1/berita',
    UPDATE: (id: string) => `/v1/berita/${id}`,
    DELETE: (id: string) => `/v1/berita/delete/${id}`,
  },
  
  ARTIKEL: {
    LIST: '/v1/artikel',
    DETAIL: (id: string) => `/v1/artikel/${id}`,
    CREATE: '/v1/artikel',
    UPDATE: (id: string) => `/v1/artikel/${id}`,
    DELETE: (id: string) => `/v1/artikel/delete/${id}`,
  },
  
  EVENT: {
    LIST: '/v1/event',
    DETAIL: (id: string) => `/v1/event/${id}`,
    CREATE: '/v1/event',
    UPDATE: (id: string) => `/v1/event/${id}`,
    DELETE_FILE: (id: string, name: string) => `/v1/event/${id}/${name}`,
  },
  
  // Landing page
  LANDING: {
    EVENTS: '/v1/landing/event',
    BERITA: '/v1/landing/berita',
    ARTIKEL: '/v1/landing/artikel',
  },
  
  // Security modules
  KEAMANAN: {
    PANDUAN: {
      LIST: '/v1/keamanan/panduan',
      CREATE: '/v1/keamanan/panduan',
      UPDATE: (id: string) => `/v1/keamanan/panduan/${id}`,
      DELETE: (id: string) => `/v1/keamanan/panduan/delete/${id}`,
    },
    PERINGATAN: {
      LIST: '/v1/keamanan/peringatan',
      CREATE: '/v1/keamanan/peringatan',
      UPDATE: (id: string) => `/v1/keamanan/peringatan/${id}`,
      DELETE: (id: string) => `/v1/keamanan/peringatan/delete/${id}`,
    },
  },
  
  // Ticketing system
  TICKETING: {
    LIST: '/v1/ticketing',
    DETAIL: (id: string) => `/v1/ticketing/${id}`,
    BY_NUMBER: (number: string) => `/v1/ticketing/bynumber/${number}`,
    HISTORY: (id: string) => `/v1/ticketing/history/${id}`,
    CREATE: '/v1/ticketing',
    UPDATE: (id: string) => `/v1/ticketing/${id}`,
    UPDATE_PROGRESS: (id: string) => `/v1/ticketing/progress/${id}`,
  },
  
  // Chat system
  CHAT: {
    INITIATE: '/chat/initiate',
    MESSAGES: (chatRoomId: string) => `/chat/rooms/${chatRoomId}/messages`,
    ACCESS: (chatRoomId: string) => `/chat/rooms/${chatRoomId}/access`,
    OPERATOR: {
      LOGIN: '/chat/operator/login',
      ROOMS: '/chat/operator/chat-rooms',
      ASSIGN: (chatRoomId: string) => `/chat/operator/chat-rooms/${chatRoomId}/assign`,
      ONLINE: '/chat/operator/online',
      MY_ROOMS: '/chat/operator/my-rooms',
    },
  },
  
  // Health check
  HEALTH: '/health',
  
  // File access
  FILES: {
    SECURE: (filename: string) => `/files/secure/${filename}`,
    PUBLIC: (filename: string) => `/files/public/${filename}`,
  },
} as const;

/**
 * Build full URL for API endpoint
 */
export const buildApiUrl = (endpoint: string): string => {
  const config = getApiConfig();
  return `${config.baseURL}${endpoint}`;
};

/**
 * Build full backend URL (for WebSocket connections)
 */
export const buildBackendUrl = (path: string = ''): string => {
  const config = getApiConfig();
  return `${config.backendURL}${path}`;
};

/**
 * Build WebSocket URL
 */
export const buildWsUrl = (path: string = ''): string => {
  const config = getApiConfig();
  return `${config.wsURL}${path}`;
};
