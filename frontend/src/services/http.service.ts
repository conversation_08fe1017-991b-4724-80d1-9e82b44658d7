/**
 * HTTP Service for API Communication
 * Centralized HTTP client with authentication and error handling
 */

import { API_CONFIG, buildApiUrl } from '@/config/api.config';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface ApiError {
  success: false;
  message: string;
  statusCode?: number;
  errors?: any[];
}

export class HttpService {
  private timeout: number;

  constructor() {
    this.timeout = API_CONFIG.timeout;
  }

  /**
   * Get authentication token from storage
   */
  private getAuthToken(): string | null {
    // Try to get token from localStorage or sessionStorage
    return localStorage.getItem('access_token') || sessionStorage.getItem('access_token');
  }

  /**
   * Build request headers
   */
  private buildHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...customHeaders,
    };

    const token = this.getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: any;
    if (isJson) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      const error: ApiError = {
        success: false,
        message: data?.message || `HTTP Error: ${response.status}`,
        statusCode: response.status,
        errors: data?.errors,
      };
      throw error;
    }

    return data;
  }

  /**
   * Make HTTP request with timeout
   */
  private async makeRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return this.handleResponse<T>(response);
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(buildApiUrl(endpoint), window.location.origin);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    return this.makeRequest<T>(url.toString(), {
      method: 'GET',
      headers: this.buildHeaders(),
    });
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<T> {
    const url = buildApiUrl(endpoint);
    const headers = this.buildHeaders(customHeaders);

    let body: string | FormData;
    if (data instanceof FormData) {
      body = data;
      // Remove Content-Type header for FormData (browser will set it with boundary)
      delete headers['Content-Type'];
    } else {
      body = JSON.stringify(data);
    }

    return this.makeRequest<T>(url, {
      method: 'POST',
      headers,
      body,
    });
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<T> {
    const url = buildApiUrl(endpoint);
    const headers = this.buildHeaders(customHeaders);

    let body: string | FormData;
    if (data instanceof FormData) {
      body = data;
      delete headers['Content-Type'];
    } else {
      body = JSON.stringify(data);
    }

    return this.makeRequest<T>(url, {
      method: 'PUT',
      headers,
      body,
    });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<T> {
    const url = buildApiUrl(endpoint);
    const headers = this.buildHeaders(customHeaders);

    let body: string | FormData;
    if (data instanceof FormData) {
      body = data;
      delete headers['Content-Type'];
    } else {
      body = JSON.stringify(data);
    }

    return this.makeRequest<T>(url, {
      method: 'PATCH',
      headers,
      body,
    });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string): Promise<T> {
    const url = buildApiUrl(endpoint);

    return this.makeRequest<T>(url, {
      method: 'DELETE',
      headers: this.buildHeaders(),
    });
  }

  /**
   * Upload file(s)
   */
  async upload<T = any>(endpoint: string, files: File | File[], additionalData?: Record<string, any>): Promise<T> {
    const formData = new FormData();

    if (Array.isArray(files)) {
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });
    } else {
      formData.append('file', files);
    }

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    return this.post<T>(endpoint, formData);
  }
}

/**
 * Default HTTP service instance
 */
export const httpService = new HttpService();

/**
 * Convenience methods for common API patterns
 */
export const api = {
  get: httpService.get.bind(httpService),
  post: httpService.post.bind(httpService),
  put: httpService.put.bind(httpService),
  patch: httpService.patch.bind(httpService),
  delete: httpService.delete.bind(httpService),
  upload: httpService.upload.bind(httpService),
};
