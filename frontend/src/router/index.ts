import { createRouter, createWebHistory } from 'vue-router';
import { useOperatorStore } from '../features/cms/stores/operator.store';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../components/HomePage.vue'),
    },
    {
      path: '/cms/login',
      name: 'OperatorLogin',
      component: () => import('../features/cms/components/OperatorLogin.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/cms/dashboard',
      name: 'OperatorDashboard',
      component: () => import('../features/cms/components/OperatorDashboard.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/cms',
      redirect: '/cms/dashboard',
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/',
    },
  ],
});

// Navigation guards
router.beforeEach((to: any, _from: any, next: any) => {
  const operatorStore = useOperatorStore();
  
  // Initialize operator store from localStorage if not already done
  if (!operatorStore.isAuthenticated) {
    operatorStore.initializeFromStorage();
  }

  const isAuthenticated = operatorStore.isAuthenticated;
  const requiresAuth = to.matched.some((record: any) => record.meta?.requiresAuth);
  const requiresGuest = to.matched.some((record: any) => record.meta?.requiresGuest);

  if (requiresAuth && !isAuthenticated) {
    // Redirect to login if route requires authentication and user is not authenticated
    next('/cms/login');
  } else if (requiresGuest && isAuthenticated) {
    // Redirect to dashboard if route requires guest and user is authenticated
    next('/cms/dashboard');
  } else {
    next();
  }
});

export default router;