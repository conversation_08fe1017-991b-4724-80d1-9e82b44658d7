<script setup lang="ts">
import { cva } from 'class-variance-authority'
import { computed } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: string
  htmlFor?: string
}>()

const labelVariants = cva(
  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
)

const classes = computed(() => {
  return cn(labelVariants(), props.class)
})
</script>

<template>
  <label :class="classes" :for="htmlFor">
    <slot />
  </label>
</template>
