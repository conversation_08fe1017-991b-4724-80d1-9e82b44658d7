<script setup lang="ts">
import { ref } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { useCounterStore } from '../stores/counter'
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Alert,
  AlertDescription
} from '@/components/ui'

defineProps<{ msg: string }>()

const count = ref(0)
const counterStore = useCounterStore()

// Example TanStack Query usage
const { data: demoData, isLoading, error } = useQuery({
  queryKey: ['demo'],
  queryFn: async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { message: 'TanStack Query is working!', timestamp: new Date().toISOString() }
  }
})
</script>

<template>
  <div class="max-w-2xl mx-auto text-center">
    <h1 class="text-4xl font-bold mb-8">{{ msg }}</h1>

    <Card class="mb-6">
      <CardHeader>
        <CardTitle>Vue 3 Counter</CardTitle>
      </CardHeader>
      <CardContent>
        <Button @click="count++">
          Count is {{ count }}
        </Button>
      </CardContent>
    </Card>

    <Card class="mb-6">
      <CardHeader>
        <CardTitle>Pinia Store</CardTitle>
        <CardDescription>Store count: {{ counterStore.count }}</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex gap-2 justify-center">
          <Button @click="counterStore.increment" variant="default">
            Increment
          </Button>
          <Button @click="counterStore.decrement" variant="destructive">
            Decrement
          </Button>
        </div>
      </CardContent>
    </Card>

    <Card class="mb-6">
      <CardHeader>
        <CardTitle>TanStack Query</CardTitle>
      </CardHeader>
      <CardContent>
        <div v-if="isLoading" class="text-muted-foreground">Loading...</div>
        <Alert v-else-if="error" variant="destructive">
          <AlertDescription>Error occurred</AlertDescription>
        </Alert>
        <div v-else-if="demoData" class="text-green-600">
          <p>{{ demoData.message }}</p>
          <p class="text-sm text-muted-foreground mt-2">{{ demoData.timestamp }}</p>
        </div>
      </CardContent>
    </Card>

    <div class="text-muted-foreground space-y-2">
      <p>
        Edit <code class="bg-muted px-2 py-1 rounded text-sm">components/HelloWorld.vue</code> to test HMR
      </p>
      <p>
        Check out
        <a href="https://vuejs.org/guide/quick-start.html#local" target="_blank" class="text-primary hover:underline">
          create-vue
        </a>, the official Vue + Vite starter
      </p>
      <p>
        Learn more about IDE Support for Vue in the
        <a
          href="https://vuejs.org/guide/scaling-up/tooling.html#ide-support"
          target="_blank"
          class="text-primary hover:underline"
        >
          Vue Docs Scaling up Guide
        </a>.
      </p>
      <p class="text-muted-foreground text-sm">Click on the Vite and Vue logos to learn more</p>
    </div>
  </div>
</template>
