<template>
  <div class="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
    <div class="flex items-center space-x-8 mb-8">
      <a href="https://vite.dev" target="_blank" class="hover:opacity-80 transition-opacity">
        <img src="/vite.svg" class="h-24 w-24" alt="Vite logo" />
      </a>
      <a href="https://vuejs.org/" target="_blank" class="hover:opacity-80 transition-opacity">
        <img src="/vite.svg" class="h-24 w-24" alt="Vue logo" />
      </a>
    </div>

    <HelloWorld msg="Vite + Vue + TypeScript + Tailwind + ShadCN UI" />

    <Separator class="my-8 w-full max-w-2xl" />

    <!-- CMS Access Link -->
    <Card class="w-full max-w-md">
      <CardHeader class="text-center">
        <CardTitle>CMS Access</CardTitle>
        <CardDescription>Access the chat management system for operators</CardDescription>
      </CardHeader>
      <CardContent>
        <Button asChild class="w-full">
          <router-link to="/cms/login">
            Access CMS Operator Interface
          </router-link>
        </Button>
      </CardContent>
    </Card>
  </div>

  <!-- Chat Widget -->
  <ChatWidget />
</template>

<script setup lang="ts">
import HelloWorld from './HelloWorld.vue'
import ChatWidget from '../features/chat/components/ChatWidget.vue'
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Separator
} from '@/components/ui'
</script>