/**
 * Secure Storage Utility
 * OWASP Reference: https://cheatsheetseries.owasp.org/cheatsheets/HTML5_Security_Cheat_Sheet.html#local-storage
 */

import CryptoJS from 'crypto-js';

class SecureStorage {
  private readonly encryptionKey: string;
  private readonly storagePrefix = 'wbs_kg_';

  constructor() {
    // Generate or retrieve encryption key from sessionStorage (more secure than localStorage)
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  private getOrCreateEncryptionKey(): string {
    const keyName = `${this.storagePrefix}enc_key`;
    let key = sessionStorage.getItem(keyName);
    
    if (!key) {
      // Generate a new encryption key for this session
      key = CryptoJS.lib.WordArray.random(256/8).toString();
      sessionStorage.setItem(keyName, key);
    }
    
    return key;
  }

  private encrypt(data: string): string {
    try {
      return CryptoJS.AES.encrypt(data, this.encryptionKey).toString();
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  private decrypt(encryptedData: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      
      if (!decrypted) {
        throw new Error('Decryption resulted in empty string');
      }
      
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  setItem(key: string, value: string): void {
    try {
      const encryptedValue = this.encrypt(value);
      const prefixedKey = `${this.storagePrefix}${key}`;
      
      // Use sessionStorage for sensitive data (cleared when tab closes)
      sessionStorage.setItem(prefixedKey, encryptedValue);
    } catch (error) {
      console.error('Failed to store encrypted data:', error);
      throw new Error('Failed to store data securely');
    }
  }

  getItem(key: string): string | null {
    try {
      const prefixedKey = `${this.storagePrefix}${key}`;
      const encryptedValue = sessionStorage.getItem(prefixedKey);
      
      if (!encryptedValue) {
        return null;
      }
      
      return this.decrypt(encryptedValue);
    } catch (error) {
      console.error('Failed to retrieve encrypted data:', error);
      // Remove corrupted data
      this.removeItem(key);
      return null;
    }
  }

  removeItem(key: string): void {
    const prefixedKey = `${this.storagePrefix}${key}`;
    sessionStorage.removeItem(prefixedKey);
  }

  clear(): void {
    // Only clear items with our prefix
    const keys = Object.keys(sessionStorage);
    keys.forEach(key => {
      if (key.startsWith(this.storagePrefix)) {
        sessionStorage.removeItem(key);
      }
    });
  }

  // Check if storage is available and working
  isAvailable(): boolean {
    try {
      const testKey = `${this.storagePrefix}test`;
      const testValue = 'test';
      sessionStorage.setItem(testKey, testValue);
      const retrieved = sessionStorage.getItem(testKey);
      sessionStorage.removeItem(testKey);
      return retrieved === testValue;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const secureStorage = new SecureStorage();

// Token-specific utilities
export const tokenStorage = {
  setAccessToken: (token: string) => secureStorage.setItem('access_token', token),
  getAccessToken: () => secureStorage.getItem('access_token'),
  setRefreshToken: (token: string) => secureStorage.setItem('refresh_token', token),
  getRefreshToken: () => secureStorage.getItem('refresh_token'),
  setOperatorData: (data: object) => secureStorage.setItem('operator_data', JSON.stringify(data)),
  getOperatorData: () => {
    const data = secureStorage.getItem('operator_data');
    return data ? JSON.parse(data) : null;
  },
  clearTokens: () => {
    secureStorage.removeItem('access_token');
    secureStorage.removeItem('refresh_token');
    secureStorage.removeItem('operator_data');
  },
};
