# Frontend - Vue3 + TypeScript + Vite + Tailwind + TanStack Query + Pinia

This is the frontend application for the WBS KG monorepo, built with modern Vue.js ecosystem tools.

## Tech Stack

- **Vue 3** - Progressive JavaScript framework
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **TanStack Query (Vue Query)** - Data fetching and state management
- **Pinia** - Vue state management library

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm run dev

# Build for production
pnpm run build

# Preview production build
pnpm run preview
```

## Project Structure

```
frontend/
├── src/
│   ├── components/         # Vue components
│   ├── stores/            # Pinia stores
│   ├── assets/            # Static assets
│   ├── App.vue            # Root component
│   ├── main.ts            # Application entry point
│   ├── style.css          # Global styles (Tailwind)
│   └── vite-env.d.ts      # TypeScript declarations
├── public/                # Public static files
├── dist/                  # Build output
├── tailwind.config.js     # Tailwind configuration
├── postcss.config.js      # PostCSS configuration
├── vite.config.ts         # Vite configuration
├── tsconfig.json          # TypeScript configuration
└── package.json           # Dependencies and scripts
```

## Features Demonstrated

- Vue 3 Composition API with TypeScript
- Tailwind CSS utility classes for styling
- Pinia store for state management (counter example)
- TanStack Query for data fetching (demo query)
- Hot Module Replacement (HMR) for development

## Development Server

The development server runs on `http://localhost:5173/` and includes:
- Hot reloading
- TypeScript support
- Tailwind CSS processing
- Vue DevTools support
