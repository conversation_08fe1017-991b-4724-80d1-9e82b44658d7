# Multi-stage Dockerfile for Vue.js Frontend
# Supports both development and production deployments
# Using Node.js LTS for better security and long-term support

# Base stage with common dependencies
FROM node:lts-alpine AS base

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# Install pnpm globally
RUN npm install -g pnpm

# Copy package files
COPY package.json ./

# Development stage
FROM base AS development

# Install all dependencies (including dev dependencies)
# Note: Using --no-frozen-lockfile for workspace compatibility
RUN pnpm install --no-frozen-lockfile

# Copy source code
COPY . .

# Build arguments for environment variables
ARG VITE_API_BASE_URL=/api
ARG VITE_BACKEND_URL=http://wbs-kg.aistech.id
ARG VITE_WS_URL=ws://wbs-kg.aistech.id

# Set environment variables
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_BACKEND_URL=$VITE_BACKEND_URL
ENV VITE_WS_URL=$VITE_WS_URL

# Build the application for development
RUN pnpm run build

# Expose port (not used in production, but useful for development)
EXPOSE 5173

# Default command for development
CMD ["pnpm", "run", "dev", "--host", "0.0.0.0"]

# Build stage for production
FROM base AS build

# Install all dependencies for building
# Note: Using --no-frozen-lockfile for workspace compatibility
RUN pnpm install --no-frozen-lockfile

# Copy source code
COPY . .

# Build arguments for environment variables
ARG VITE_API_BASE_URL=/api
ARG VITE_BACKEND_URL=https://wbs-kg.aistech.id
ARG VITE_WS_URL=wss://wbs-kg.aistech.id

# Set environment variables for build
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_BACKEND_URL=$VITE_BACKEND_URL
ENV VITE_WS_URL=$VITE_WS_URL

# Build the application
RUN pnpm run build

# Production stage - serves static files only
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built assets from the build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy custom nginx configuration if needed
# COPY nginx.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://wbs-kg.aistech.id/ || exit 1

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]