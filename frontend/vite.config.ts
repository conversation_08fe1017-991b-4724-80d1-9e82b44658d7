import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 5173, // Explicitly define frontend port
    proxy: {
      '/api': {
        target: 'http://localhost:3000', // Your NestJS backend port
        changeOrigin: true,
        // Don't rewrite the path in development - let the backend handle /api prefix
        // rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    // Production build configuration
    outDir: 'dist',
    sourcemap: mode === 'development',
    minify: mode === 'production' ? 'esbuild' : false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['@tanstack/vue-query'],
        },
      },
    },
  },
  // Environment variables configuration
  envPrefix: 'VITE_',
}))
